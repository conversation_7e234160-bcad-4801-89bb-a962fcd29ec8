# 2. Database Implementation

## 🗄️ **Database Implementation Overview**

This document provides comprehensive implementation guidelines for the UniversalWallet database layer, including entity relationships, repository patterns, data access optimization, migration strategies, and performance tuning for enterprise-scale operations.

## 🏗️ **JPA Entity Implementation**

### **Base Entity Class**
```java
@MappedSuperclass
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseEntity {
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @Version
    @Column(name = "version")
    private Long version;
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

### **User Profile Entity**
```java
@Entity
@Table(name = "user_profiles")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class UserProfile extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "profile_id")
    private UUID profileId;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "first_name", length = 100)
    private String firstName;
    
    @Column(name = "last_name", length = 100)
    private String lastName;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private Gender gender;
    
    @Column(name = "national_id", length = 50)
    private String nationalId;
    
    @Embedded
    private Address address;
    
    @Column(name = "occupation", length = 100)
    private String occupation;
    
    @Column(name = "employer", length = 255)
    private String employer;
    
    @Column(name = "monthly_income", precision = 15, scale = 2)
    private BigDecimal monthlyIncome;
    
    @Column(name = "profile_picture_url", length = 500)
    private String profilePictureUrl;
    
    @Formula("CONCAT(first_name, ' ', last_name)")
    private String fullName;
}

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Address {
    
    @Column(name = "address_line1")
    private String addressLine1;
    
    @Column(name = "address_line2")
    private String addressLine2;
    
    @Column(name = "city", length = 100)
    private String city;
    
    @Column(name = "province", length = 100)
    private String province;
    
    @Column(name = "postal_code", length = 20)
    private String postalCode;
    
    @Column(name = "country", length = 50)
    private String country = "Zimbabwe";
}
```

### **Business Profile Entity**
```java
@Entity
@Table(name = "business_profiles")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class BusinessProfile extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "business_id")
    private UUID businessId;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "business_name", nullable = false)
    private String businessName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "business_type", nullable = false)
    private BusinessType businessType;
    
    @Column(name = "registration_number", unique = true)
    private String registrationNumber;
    
    @Column(name = "tax_number")
    private String taxNumber;
    
    @Column(name = "industry")
    private String industry;
    
    @Column(name = "business_description", columnDefinition = "TEXT")
    private String businessDescription;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @Column(name = "business_phone")
    private String businessPhone;
    
    @Column(name = "business_email")
    private String businessEmail;
    
    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "addressLine1", column = @Column(name = "business_address_line1")),
        @AttributeOverride(name = "addressLine2", column = @Column(name = "business_address_line2")),
        @AttributeOverride(name = "city", column = @Column(name = "business_city")),
        @AttributeOverride(name = "province", column = @Column(name = "business_province")),
        @AttributeOverride(name = "postalCode", column = @Column(name = "business_postal_code"))
    })
    private Address businessAddress;
    
    @Column(name = "annual_revenue", precision = 15, scale = 2)
    private BigDecimal annualRevenue;
    
    @Column(name = "employee_count")
    private Integer employeeCount;
    
    @Column(name = "business_logo_url")
    private String businessLogoUrl;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "verification_status")
    private VerificationStatus verificationStatus = VerificationStatus.PENDING;
    
    @OneToMany(mappedBy = "business", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BusinessUser> businessUsers = new ArrayList<>();
    
    @OneToMany(mappedBy = "business", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Invoice> invoices = new ArrayList<>();
}
```

---

## 🔍 **Repository Implementation**

### **Base Repository Interface**
```java
@NoRepositoryBean
public interface BaseRepository<T, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {
    
    @Query("SELECT e FROM #{#entityName} e WHERE e.createdAt BETWEEN :startDate AND :endDate")
    List<T> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                   @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(e) FROM #{#entityName} e WHERE e.createdAt >= :date")
    long countCreatedSince(@Param("date") LocalDateTime date);
    
    @Modifying
    @Query("UPDATE #{#entityName} e SET e.updatedAt = :now WHERE e.id = :id")
    void updateTimestamp(@Param("id") ID id, @Param("now") LocalDateTime now);
}
```

### **User Repository**
```java
@Repository
public interface UserRepository extends BaseRepository<User, UUID> {
    
    @Query("SELECT u FROM User u WHERE u.phoneNumber = :phoneNumber")
    Optional<User> findByPhoneNumber(@Param("phoneNumber") String phoneNumber);
    
    @Query("SELECT u FROM User u WHERE u.email = :email")
    Optional<User> findByEmail(@Param("email") String email);
    
    @Query("SELECT u FROM User u WHERE u.status = :status AND u.userType = :userType")
    List<User> findByStatusAndUserType(@Param("status") UserStatus status, 
                                       @Param("userType") UserType userType);
    
    @Query("SELECT u FROM User u WHERE u.kycLevel = :kycLevel AND u.status = 'ACTIVE'")
    List<User> findActiveUsersByKycLevel(@Param("kycLevel") KycLevel kycLevel);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.userType = :userType AND u.createdAt >= :date")
    long countNewUsersByType(@Param("userType") UserType userType, @Param("date") LocalDateTime date);
    
    @Query("SELECT u FROM User u WHERE u.lastLoginAt < :date AND u.status = 'ACTIVE'")
    List<User> findInactiveUsers(@Param("date") LocalDateTime date);
    
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.userId = :userId")
    void updateUserStatus(@Param("userId") UUID userId, @Param("status") UserStatus status);
    
    @Modifying
    @Query("UPDATE User u SET u.failedLoginAttempts = 0, u.accountLockedUntil = null WHERE u.userId = :userId")
    void resetFailedLoginAttempts(@Param("userId") UUID userId);
    
    boolean existsByPhoneNumber(String phoneNumber);
    boolean existsByEmail(String email);
}
```

### **Transaction Repository**
```java
@Repository
public interface TransactionRepository extends BaseRepository<Transaction, UUID> {
    
    @Query("SELECT t FROM Transaction t WHERE t.user.userId = :userId ORDER BY t.initiatedAt DESC")
    Page<Transaction> findByUserIdOrderByInitiatedAtDesc(@Param("userId") UUID userId, Pageable pageable);
    
    @Query("SELECT t FROM Transaction t WHERE t.referenceNumber = :referenceNumber")
    Optional<Transaction> findByReferenceNumber(@Param("referenceNumber") String referenceNumber);
    
    @Query("SELECT t FROM Transaction t WHERE t.status = :status AND t.initiatedAt < :date")
    List<Transaction> findStaleTransactions(@Param("status") TransactionStatus status, 
                                           @Param("date") LocalDateTime date);
    
    @Query("SELECT t FROM Transaction t WHERE t.user.userId = :userId AND t.status = :status " +
           "AND t.initiatedAt BETWEEN :startDate AND :endDate")
    List<Transaction> findUserTransactionsByStatusAndDateRange(
        @Param("userId") UUID userId,
        @Param("status") TransactionStatus status,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.user.userId = :userId " +
           "AND t.status = 'COMPLETED' AND t.initiatedAt >= :date")
    BigDecimal sumCompletedTransactionAmountSince(@Param("userId") UUID userId, 
                                                  @Param("date") LocalDateTime date);
    
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.user.userId = :userId " +
           "AND t.status = 'COMPLETED' AND DATE(t.initiatedAt) = CURRENT_DATE")
    long countTodayCompletedTransactions(@Param("userId") UUID userId);
    
    @Query("SELECT t.transactionType, COUNT(t), SUM(t.amount) FROM Transaction t " +
           "WHERE t.status = 'COMPLETED' AND t.initiatedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY t.transactionType")
    List<Object[]> getTransactionStatsByType(@Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate);
    
    @Modifying
    @Query("UPDATE Transaction t SET t.status = :newStatus WHERE t.status = :oldStatus " +
           "AND t.initiatedAt < :cutoffDate")
    int updateStaleTransactionStatus(@Param("oldStatus") TransactionStatus oldStatus,
                                    @Param("newStatus") TransactionStatus newStatus,
                                    @Param("cutoffDate") LocalDateTime cutoffDate);
}
```

### **Custom Repository Implementation**
```java
@Repository
@Transactional(readOnly = true)
public class CustomTransactionRepositoryImpl implements CustomTransactionRepository {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<TransactionSummary> getTransactionSummaryByUser(UUID userId, 
                                                               LocalDateTime startDate, 
                                                               LocalDateTime endDate) {
        
        String jpql = """
            SELECT new com.universalwallet.dto.TransactionSummary(
                t.transactionType,
                COUNT(t),
                SUM(t.amount),
                AVG(t.amount),
                SUM(t.feeAmount)
            )
            FROM Transaction t
            WHERE t.user.userId = :userId
            AND t.status = 'COMPLETED'
            AND t.initiatedAt BETWEEN :startDate AND :endDate
            GROUP BY t.transactionType
            """;
        
        return entityManager.createQuery(jpql, TransactionSummary.class)
            .setParameter("userId", userId)
            .setParameter("startDate", startDate)
            .setParameter("endDate", endDate)
            .getResultList();
    }
    
    @Override
    public Page<Transaction> findTransactionsWithFilters(TransactionSearchCriteria criteria, 
                                                         Pageable pageable) {
        
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Transaction> query = cb.createQuery(Transaction.class);
        Root<Transaction> root = query.from(Transaction.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        // User filter
        if (criteria.getUserId() != null) {
            predicates.add(cb.equal(root.get("user").get("userId"), criteria.getUserId()));
        }
        
        // Status filter
        if (criteria.getStatus() != null) {
            predicates.add(cb.equal(root.get("status"), criteria.getStatus()));
        }
        
        // Transaction type filter
        if (criteria.getTransactionType() != null) {
            predicates.add(cb.equal(root.get("transactionType"), criteria.getTransactionType()));
        }
        
        // Date range filter
        if (criteria.getStartDate() != null && criteria.getEndDate() != null) {
            predicates.add(cb.between(root.get("initiatedAt"), 
                                    criteria.getStartDate(), criteria.getEndDate()));
        }
        
        // Amount range filter
        if (criteria.getMinAmount() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("amount"), criteria.getMinAmount()));
        }
        if (criteria.getMaxAmount() != null) {
            predicates.add(cb.lessThanOrEqualTo(root.get("amount"), criteria.getMaxAmount()));
        }
        
        // Recipient phone filter
        if (criteria.getRecipientPhone() != null) {
            predicates.add(cb.like(root.get("recipientPhone"), 
                                 "%" + criteria.getRecipientPhone() + "%"));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(cb.desc(root.get("initiatedAt")));
        
        TypedQuery<Transaction> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        
        List<Transaction> transactions = typedQuery.getResultList();
        
        // Count query for pagination
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Transaction> countRoot = countQuery.from(Transaction.class);
        countQuery.select(cb.count(countRoot));
        countQuery.where(predicates.toArray(new Predicate[0]));
        
        Long total = entityManager.createQuery(countQuery).getSingleResult();
        
        return new PageImpl<>(transactions, pageable, total);
    }
}
```

---

## 🚀 **Database Migration Implementation**

### **Flyway Migration Scripts**

#### **V1__Initial_Schema.sql**
```sql
-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create enums
CREATE TYPE user_type AS ENUM ('personal', 'business', 'agent', 'admin');
CREATE TYPE user_status AS ENUM ('active', 'suspended', 'pending', 'blocked');
CREATE TYPE kyc_level AS ENUM ('basic', 'enhanced', 'business', 'agent');
CREATE TYPE kyc_status AS ENUM ('pending', 'approved', 'rejected', 'expired');
CREATE TYPE transaction_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed');
CREATE TYPE transaction_type AS ENUM ('p2p_transfer', 'bill_payment', 'cash_in', 'cash_out', 'wallet_to_bank', 'bank_to_wallet', 'interoperable_transfer', 'bulk_payment', 'agent_commission', 'refund');
CREATE TYPE provider_name AS ENUM ('ecocash', 'onemoney', 'innbucks', 'cabs', 'stanbic', 'barclays', 'fbc', 'steward', 'universalwallet');
CREATE TYPE account_type AS ENUM ('mobile_money', 'bank_account', 'savings', 'current', 'wallet');
CREATE TYPE connection_status AS ENUM ('connected', 'disconnected', 'error', 'pending');

-- Create users table
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    country_code VARCHAR(5) NOT NULL DEFAULT '+263',
    email VARCHAR(255) UNIQUE,
    user_type user_type NOT NULL,
    status user_status DEFAULT 'pending',
    kyc_level kyc_level DEFAULT 'basic',
    kyc_status kyc_status DEFAULT 'pending',
    pin_hash VARCHAR(255) NOT NULL,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    created_by UUID REFERENCES users(user_id)
);

-- Create user profiles table
CREATE TABLE user_profiles (
    profile_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    gender VARCHAR(10),
    national_id VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Zimbabwe',
    occupation VARCHAR(100),
    employer VARCHAR(255),
    monthly_income DECIMAL(15,2),
    profile_picture_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

-- Create linked accounts table
CREATE TABLE linked_accounts (
    account_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    provider_name provider_name NOT NULL,
    account_type account_type NOT NULL,
    account_number VARCHAR(100),
    account_name VARCHAR(255),
    currency VARCHAR(3) DEFAULT 'ZWG',
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_balance DECIMAL(15,2) DEFAULT 0.00,
    last_balance_update TIMESTAMP,
    connection_status connection_status DEFAULT 'pending',
    connection_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    UNIQUE(user_id, provider_name, account_number)
);

-- Create transactions table
CREATE TABLE transactions (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference_number VARCHAR(100) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(user_id),
    transaction_type transaction_type NOT NULL,
    status transaction_status DEFAULT 'pending',
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZWG',
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL,
    source_account_id UUID REFERENCES linked_accounts(account_id),
    destination_account_id UUID REFERENCES linked_accounts(account_id),
    recipient_phone VARCHAR(20),
    recipient_name VARCHAR(255),
    description TEXT,
    metadata JSONB,
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    failure_reason TEXT,
    external_reference VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);
```

#### **V2__Add_Indexes.sql**
```sql
-- User indexes
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status_type ON users(status, user_type);
CREATE INDEX idx_users_kyc_level ON users(kyc_level);
CREATE INDEX idx_users_created_at ON users(created_at);

-- User profile indexes
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_national_id ON user_profiles(national_id);

-- Linked account indexes
CREATE INDEX idx_linked_accounts_user_id ON linked_accounts(user_id);
CREATE INDEX idx_linked_accounts_provider ON linked_accounts(provider_name);
CREATE INDEX idx_linked_accounts_user_provider ON linked_accounts(user_id, provider_name);
CREATE INDEX idx_linked_accounts_status ON linked_accounts(connection_status);

-- Transaction indexes
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_reference ON transactions(reference_number);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_initiated_at ON transactions(initiated_at);
CREATE INDEX idx_transactions_user_initiated ON transactions(user_id, initiated_at DESC);
CREATE INDEX idx_transactions_status_initiated ON transactions(status, initiated_at);
CREATE INDEX idx_transactions_recipient_phone ON transactions(recipient_phone);

-- Composite indexes for common queries
CREATE INDEX idx_transactions_user_status_date ON transactions(user_id, status, initiated_at);
CREATE INDEX idx_transactions_type_status_date ON transactions(transaction_type, status, initiated_at);
```

**This comprehensive database implementation provides enterprise-level data management with optimized performance, security, and scalability for the UniversalWallet platform.** 🗄️
