# 3. Version Control and Workflow

## 🔄 **Git Workflow Overview**

The Taxicab platform uses a structured Git workflow based on GitFlow with adaptations for modern development practices. This workflow ensures code quality, facilitates collaboration, and maintains a stable main branch for production deployments.

### **Workflow Principles**
- **Main Branch Protection**: Main branch always contains production-ready code
- **Feature Isolation**: All new features developed in separate branches
- **Code Review**: All changes require peer review before merging
- **Automated Testing**: CI/CD pipeline runs tests on all pull requests
- **Semantic Versioning**: Clear versioning strategy for releases

## 🌳 **Branch Strategy**

### **1. Branch Types and Naming**

#### **Main Branches**
```bash
# Main production branch
main                    # Production-ready code, protected

# Development integration branch
develop                 # Integration branch for features
```

#### **Supporting Branches**
```bash
# Feature branches (from develop)
feature/ride-booking-system
feature/driver-profile-sharing
feature/whatsapp-integration
feature/payment-gateway-integration

# Release branches (from develop)
release/v1.0.0
release/v1.1.0

# Hotfix branches (from main)
hotfix/critical-payment-bug
hotfix/location-tracking-fix

# Bug fix branches (from develop)
bugfix/driver-rating-calculation
bugfix/notification-delivery
```

### **2. Branch Naming Conventions**
```bash
# Feature branches
feature/<ticket-number>-<short-description>
feature/TAX-123-implement-ride-booking
feature/TAX-456-add-driver-chat

# Bug fix branches
bugfix/<ticket-number>-<short-description>
bugfix/TAX-789-fix-payment-processing

# Hotfix branches
hotfix/<ticket-number>-<short-description>
hotfix/TAX-999-critical-security-patch

# Release branches
release/v<major>.<minor>.<patch>
release/v1.2.0
```

## 🔧 **Git Configuration and Setup**

### **1. Repository Setup**
```bash
# Clone repository
git clone https://github.com/your-org/taxicab-platform.git
cd taxicab-platform

# Set up remote branches
git checkout -b develop origin/develop

# Configure Git hooks
cp .githooks/* .git/hooks/
chmod +x .git/hooks/*

# Set up Git aliases for common operations
git config alias.co checkout
git config alias.br branch
git config alias.ci commit
git config alias.st status
git config alias.unstage 'reset HEAD --'
git config alias.last 'log -1 HEAD'
git config alias.visual '!gitk'
```

### **2. Commit Message Standards**
```bash
# Commit message format
<type>(<scope>): <subject>

<body>

<footer>

# Types
feat:     # New feature
fix:      # Bug fix
docs:     # Documentation changes
style:    # Code style changes (formatting, etc.)
refactor: # Code refactoring
test:     # Adding or updating tests
chore:    # Maintenance tasks

# Examples
feat(auth): implement phone number verification

Add SMS-based OTP verification for user authentication.
- Integrate with Twilio SMS service
- Add OTP validation logic
- Update user registration flow

Closes #123

fix(payment): resolve EcoCash payment timeout issue

The payment processing was timing out due to incorrect
API endpoint configuration.

- Update EcoCash API endpoint URL
- Increase timeout to 30 seconds
- Add retry logic for failed payments

Fixes #456
```

### **3. Pre-commit Hooks**
```bash
#!/bin/sh
# .githooks/pre-commit

echo "Running pre-commit checks..."

# Run linting
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ Linting failed. Please fix errors before committing."
  exit 1
fi

# Run type checking
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ Type checking failed. Please fix type errors before committing."
  exit 1
fi

# Run tests
npm run test
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Please fix failing tests before committing."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
```

## 🔄 **Development Workflow**

### **1. Feature Development Workflow**
```bash
# 1. Start new feature from develop
git checkout develop
git pull origin develop
git checkout -b feature/TAX-123-implement-ride-booking

# 2. Work on feature with regular commits
git add .
git commit -m "feat(rides): add ride request validation"

git add .
git commit -m "feat(rides): implement driver matching algorithm"

# 3. Keep feature branch updated with develop
git checkout develop
git pull origin develop
git checkout feature/TAX-123-implement-ride-booking
git rebase develop

# 4. Push feature branch
git push origin feature/TAX-123-implement-ride-booking

# 5. Create pull request via GitHub/GitLab
# - Target: develop
# - Include description, screenshots, testing notes
# - Request reviewers
# - Link to ticket/issue

# 6. After approval and merge, clean up
git checkout develop
git pull origin develop
git branch -d feature/TAX-123-implement-ride-booking
git push origin --delete feature/TAX-123-implement-ride-booking
```

### **2. Hotfix Workflow**
```bash
# 1. Create hotfix from main
git checkout main
git pull origin main
git checkout -b hotfix/TAX-999-critical-payment-bug

# 2. Fix the issue
git add .
git commit -m "fix(payment): resolve critical payment processing bug"

# 3. Test thoroughly
npm run test
npm run e2e:critical

# 4. Merge to main and develop
git checkout main
git merge --no-ff hotfix/TAX-999-critical-payment-bug
git tag -a v1.0.1 -m "Hotfix v1.0.1: Critical payment bug fix"
git push origin main --tags

git checkout develop
git merge --no-ff hotfix/TAX-999-critical-payment-bug
git push origin develop

# 5. Clean up
git branch -d hotfix/TAX-999-critical-payment-bug
git push origin --delete hotfix/TAX-999-critical-payment-bug
```

### **3. Release Workflow**
```bash
# 1. Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.1.0

# 2. Prepare release
# - Update version numbers
# - Update CHANGELOG.md
# - Final testing and bug fixes

git add .
git commit -m "chore(release): prepare v1.1.0 release"

# 3. Merge to main
git checkout main
git merge --no-ff release/v1.1.0
git tag -a v1.1.0 -m "Release v1.1.0"
git push origin main --tags

# 4. Merge back to develop
git checkout develop
git merge --no-ff release/v1.1.0
git push origin develop

# 5. Clean up
git branch -d release/v1.1.0
git push origin --delete release/v1.1.0
```

## 📋 **Pull Request Guidelines**

### **1. Pull Request Template**
```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Related Issues
Closes #123
Related to #456

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Cross-browser testing (if applicable)
- [ ] Mobile testing (if applicable)

## Screenshots (if applicable)
[Add screenshots here]

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

### **2. Code Review Process**
```bash
# Reviewer checklist:
# - Code follows project standards
# - Logic is sound and efficient
# - Error handling is appropriate
# - Tests are comprehensive
# - Documentation is updated
# - Security considerations addressed
# - Performance impact considered

# Review commands
git checkout feature/branch-name
git pull origin feature/branch-name

# Test the changes
npm install
npm run test
npm run dev

# Leave review comments in GitHub/GitLab
# Approve or request changes
```

## 🏷️ **Tagging and Versioning**

### **1. Semantic Versioning**
```bash
# Version format: MAJOR.MINOR.PATCH
# Example: v1.2.3

# MAJOR: Breaking changes
v1.0.0 → v2.0.0  # API changes, major refactoring

# MINOR: New features (backward compatible)
v1.0.0 → v1.1.0  # New ride booking features

# PATCH: Bug fixes (backward compatible)
v1.0.0 → v1.0.1  # Bug fixes, security patches
```

### **2. Tagging Strategy**
```bash
# Create annotated tags for releases
git tag -a v1.0.0 -m "Release v1.0.0: Initial platform launch"
git tag -a v1.1.0 -m "Release v1.1.0: WhatsApp integration and driver chat"

# Push tags to remote
git push origin --tags

# List all tags
git tag -l

# Show tag information
git show v1.0.0
```

## 🔒 **Branch Protection Rules**

### **1. Main Branch Protection**
```yaml
# GitHub branch protection settings for main
protection_rules:
  main:
    required_status_checks:
      strict: true
      contexts:
        - "ci/build"
        - "ci/test"
        - "ci/lint"
        - "ci/type-check"
    enforce_admins: true
    required_pull_request_reviews:
      required_approving_review_count: 2
      dismiss_stale_reviews: true
      require_code_owner_reviews: true
    restrictions:
      users: []
      teams: ["senior-developers", "tech-leads"]
```

### **2. Develop Branch Protection**
```yaml
# GitHub branch protection settings for develop
protection_rules:
  develop:
    required_status_checks:
      strict: true
      contexts:
        - "ci/build"
        - "ci/test"
        - "ci/lint"
    required_pull_request_reviews:
      required_approving_review_count: 1
      dismiss_stale_reviews: true
```

## 📊 **Git Best Practices**

### **1. Commit Best Practices**
```bash
# Make atomic commits (one logical change per commit)
# ❌ Bad
git add .
git commit -m "Fix bugs and add features"

# ✅ Good
git add src/auth/
git commit -m "feat(auth): implement phone verification"

git add src/payment/
git commit -m "fix(payment): resolve timeout issue"

# Write descriptive commit messages
# ❌ Bad
git commit -m "fix stuff"

# ✅ Good
git commit -m "fix(rides): resolve driver location update race condition"

# Use present tense in commit messages
# ❌ Bad
git commit -m "Added new feature"

# ✅ Good
git commit -m "feat: add driver profile sharing"
```

### **2. Merge vs Rebase Guidelines**
```bash
# Use rebase for feature branches to maintain linear history
git checkout feature/my-feature
git rebase develop

# Use merge for integrating features (creates merge commit)
git checkout develop
git merge --no-ff feature/my-feature

# Never rebase public branches (main, develop)
# Only rebase your own feature branches
```

### **3. Conflict Resolution**
```bash
# When conflicts occur during rebase
git rebase develop
# Fix conflicts in files
git add .
git rebase --continue

# When conflicts occur during merge
git merge develop
# Fix conflicts in files
git add .
git commit -m "resolve merge conflicts"
```

This version control workflow ensures code quality, facilitates team collaboration, and maintains a stable codebase for the Taxicab platform development.
