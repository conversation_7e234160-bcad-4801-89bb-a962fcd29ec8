# 2. End-to-End Testing

## 🎯 **E2E Testing Overview**

This document provides comprehensive guidelines for end-to-end testing of the UniversalWallet platform, covering user journey testing, cross-platform testing, automated E2E test suites, and performance validation across mobile and web applications.

## 🏗️ **E2E Testing Architecture**

### **Testing Stack**
- **Web E2E**: <PERSON><PERSON>, <PERSON><PERSON>
- **Mobile E2E**: <PERSON><PERSON> (React Native), Appium
- **API E2E**: REST Assured, <PERSON><PERSON>/Newman
- **Cross-Browser**: BrowserStack, Sauce Labs
- **Performance**: Lighthouse, WebPageTest

### **Test Environment Setup**
```yaml
# docker-compose.e2e.yml
version: '3.8'
services:
  # Application services
  backend:
    build: ./backend
    environment:
      - SPRING_PROFILES_ACTIVE=e2e
      - DATABASE_URL=***************************************************
    depends_on:
      - postgres
      - redis
    ports:
      - "8080:8080"

  web:
    build: ./frontend-web
    environment:
      - REACT_APP_API_URL=http://backend:8080/api/v1
      - REACT_APP_ENV=e2e
    ports:
      - "3000:3000"
    depends_on:
      - backend

  # Test infrastructure
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: universalwallet_e2e
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # Mock external services
  wiremock:
    image: wiremock/wiremock:latest
    ports:
      - "8089:8080"
    volumes:
      - ./e2e/mocks:/home/<USER>
```

---

## 🌐 **Web E2E Testing with Cypress**

### **Cypress Configuration**
```typescript
// cypress.config.ts
import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    env: {
      apiUrl: 'http://localhost:8080/api/v1',
      testUser: {
        phone: '+************',
        pin: '1234',
        email: '<EMAIL>'
      }
    },
    setupNodeEvents(on, config) {
      // Database seeding
      on('task', {
        seedDatabase() {
          return require('./cypress/tasks/database').seedTestData();
        },
        clearDatabase() {
          return require('./cypress/tasks/database').clearTestData();
        }
      });

      // Custom commands
      on('before:browser:launch', (browser, launchOptions) => {
        if (browser.name === 'chrome') {
          launchOptions.args.push('--disable-dev-shm-usage');
        }
        return launchOptions;
      });
    }
  }
});
```

### **Custom Commands**
```typescript
// cypress/support/commands.ts
declare global {
  namespace Cypress {
    interface Chainable {
      login(phone: string, pin: string): Chainable<void>;
      loginAsBusinessUser(): Chainable<void>;
      loginAsAgent(): Chainable<void>;
      createTestTransaction(): Chainable<void>;
      waitForTransactionCompletion(transactionId: string): Chainable<void>;
    }
  }
}

Cypress.Commands.add('login', (phone: string, pin: string) => {
  cy.visit('/login');
  
  cy.get('[data-testid="phone-input"]').type(phone);
  cy.get('[data-testid="pin-input"]').type(pin);
  cy.get('[data-testid="login-button"]').click();
  
  // Wait for successful login
  cy.url().should('include', '/dashboard');
  cy.get('[data-testid="user-menu"]').should('be.visible');
});

Cypress.Commands.add('loginAsBusinessUser', () => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/login`,
    body: {
      email: '<EMAIL>',
      password: 'password123'
    }
  }).then((response) => {
    window.localStorage.setItem('accessToken', response.body.data.accessToken);
    window.localStorage.setItem('refreshToken', response.body.data.refreshToken);
    window.localStorage.setItem('user', JSON.stringify(response.body.data.user));
  });
  
  cy.visit('/business/dashboard');
});

Cypress.Commands.add('createTestTransaction', () => {
  cy.get('[data-testid="send-money-button"]').click();
  cy.get('[data-testid="recipient-phone"]').type('+************');
  cy.get('[data-testid="amount-input"]').type('100');
  cy.get('[data-testid="description-input"]').type('Test payment');
  cy.get('[data-testid="continue-button"]').click();
  
  // Confirm transaction
  cy.get('[data-testid="confirm-button"]').click();
  cy.get('[data-testid="pin-input"]').type('1234');
  cy.get('[data-testid="submit-button"]').click();
});

Cypress.Commands.add('waitForTransactionCompletion', (transactionId: string) => {
  cy.intercept('GET', `**/transactions/${transactionId}`).as('getTransaction');
  
  const checkStatus = () => {
    cy.wait('@getTransaction').then((interception) => {
      const status = interception.response?.body.data.status;
      if (status === 'completed' || status === 'failed') {
        return;
      } else {
        cy.wait(2000);
        cy.reload();
        checkStatus();
      }
    });
  };
  
  checkStatus();
});
```

### **User Journey Tests**
```typescript
// cypress/e2e/user-journeys/personal-user-journey.cy.ts
describe('Personal User Journey', () => {
  beforeEach(() => {
    cy.task('clearDatabase');
    cy.task('seedDatabase');
  });

  it('should complete full user onboarding and first transaction', () => {
    // Step 1: Registration
    cy.visit('/register');
    
    cy.get('[data-testid="phone-input"]').type('+************');
    cy.get('[data-testid="pin-input"]').type('1234');
    cy.get('[data-testid="confirm-pin-input"]').type('1234');
    cy.get('[data-testid="terms-checkbox"]').check();
    cy.get('[data-testid="register-button"]').click();
    
    // Step 2: OTP Verification
    cy.get('[data-testid="otp-input"]').type('123456');
    cy.get('[data-testid="verify-button"]').click();
    
    // Should redirect to dashboard
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid="welcome-message"]').should('contain', 'Welcome to UniversalWallet');
    
    // Step 3: Complete Profile
    cy.get('[data-testid="complete-profile-button"]').click();
    
    cy.get('[data-testid="first-name"]').type('John');
    cy.get('[data-testid="last-name"]').type('Doe');
    cy.get('[data-testid="date-of-birth"]').type('1990-01-01');
    cy.get('[data-testid="national-id"]').type('63-123456-A-12');
    cy.get('[data-testid="save-profile-button"]').click();
    
    cy.get('[data-testid="success-message"]').should('contain', 'Profile updated successfully');
    
    // Step 4: Link External Account
    cy.get('[data-testid="link-account-button"]').click();
    
    cy.get('[data-testid="provider-select"]').select('ecocash');
    cy.get('[data-testid="account-number"]').type('+************');
    cy.get('[data-testid="account-pin"]').type('1234');
    cy.get('[data-testid="link-button"]').click();
    
    cy.get('[data-testid="account-linked-message"]').should('be.visible');
    
    // Step 5: First Transaction
    cy.createTestTransaction();
    
    // Verify transaction appears in history
    cy.get('[data-testid="transaction-history"]').should('contain', '+************');
    cy.get('[data-testid="transaction-amount"]').should('contain', '100.00');
    
    // Step 6: Check Updated Balance
    cy.get('[data-testid="total-balance"]').should('not.contain', '1,000.00');
  });

  it('should handle transaction failure gracefully', () => {
    cy.login('+************', '1234');
    
    // Mock external service failure
    cy.intercept('POST', '**/transactions/transfer', {
      statusCode: 400,
      body: {
        success: false,
        error: {
          code: 'EXTERNAL_SERVICE_ERROR',
          message: 'External service temporarily unavailable'
        }
      }
    }).as('failedTransfer');
    
    cy.createTestTransaction();
    
    cy.wait('@failedTransfer');
    
    // Should show error message
    cy.get('[data-testid="error-message"]')
      .should('contain', 'External service temporarily unavailable');
    
    // Should allow retry
    cy.get('[data-testid="retry-button"]').should('be.visible');
  });
});
```

### **Business User E2E Tests**
```typescript
// cypress/e2e/user-journeys/business-user-journey.cy.ts
describe('Business User Journey', () => {
  beforeEach(() => {
    cy.task('clearDatabase');
    cy.task('seedDatabase');
    cy.loginAsBusinessUser();
  });

  it('should complete bulk payment workflow', () => {
    // Navigate to bulk payments
    cy.get('[data-testid="bulk-payments-menu"]').click();
    cy.url().should('include', '/business/bulk-payments');
    
    // Upload payment file
    const fileName = 'test-payments.csv';
    cy.fixture(fileName).then(fileContent => {
      cy.get('[data-testid="file-upload"]').selectFile({
        contents: Cypress.Buffer.from(fileContent),
        fileName: fileName,
        mimeType: 'text/csv'
      });
    });
    
    // Verify file validation
    cy.get('[data-testid="validation-results"]').should('contain', '10 payments ready');
    cy.get('[data-testid="continue-button"]').click();
    
    // Review payments
    cy.get('[data-testid="payment-preview-table"]').should('be.visible');
    cy.get('[data-testid="total-amount"]').should('contain', '1,000.00');
    cy.get('[data-testid="total-fees"]').should('contain', '50.00');
    
    // Submit for approval
    cy.get('[data-testid="submit-approval-button"]').click();
    
    // Verify submission
    cy.get('[data-testid="success-message"]')
      .should('contain', 'Bulk payment submitted for approval');
    
    // Check status in bulk payments list
    cy.visit('/business/bulk-payments');
    cy.get('[data-testid="bulk-payment-row"]').first()
      .should('contain', 'Pending Approval');
  });

  it('should generate and send invoice', () => {
    // Navigate to invoices
    cy.get('[data-testid="invoices-menu"]').click();
    cy.get('[data-testid="create-invoice-button"]').click();
    
    // Fill invoice details
    cy.get('[data-testid="customer-name"]').type('ABC Company');
    cy.get('[data-testid="customer-email"]').type('<EMAIL>');
    cy.get('[data-testid="customer-phone"]').type('+************');
    
    // Add line items
    cy.get('[data-testid="add-line-item"]').click();
    cy.get('[data-testid="item-description"]').type('Web Development Services');
    cy.get('[data-testid="item-quantity"]').type('1');
    cy.get('[data-testid="item-price"]').type('5000');
    
    // Set dates
    cy.get('[data-testid="invoice-date"]').type('2024-01-15');
    cy.get('[data-testid="due-date"]').type('2024-02-15');
    
    // Create invoice
    cy.get('[data-testid="create-invoice-button"]').click();
    
    // Verify invoice creation
    cy.get('[data-testid="invoice-number"]').should('contain', 'INV-');
    cy.get('[data-testid="total-amount"]').should('contain', '5,750.00'); // Including tax
    
    // Send invoice
    cy.get('[data-testid="send-invoice-button"]').click();
    cy.get('[data-testid="confirm-send-button"]').click();
    
    // Verify invoice sent
    cy.get('[data-testid="invoice-status"]').should('contain', 'Sent');
    cy.get('[data-testid="success-message"]')
      .should('contain', 'Invoice sent successfully');
  });
});
```

---

## 📱 **Mobile E2E Testing with Detox**

### **Detox Configuration**
```json
// .detoxrc.json
{
  "testRunner": "jest",
  "runnerConfig": "e2e/jest.config.js",
  "configurations": {
    "ios.sim.debug": {
      "device": "simulator",
      "app": "ios.debug"
    },
    "android.emu.debug": {
      "device": "emulator",
      "app": "android.debug"
    }
  },
  "devices": {
    "simulator": {
      "type": "ios.simulator",
      "device": {
        "type": "iPhone 14"
      }
    },
    "emulator": {
      "type": "android.emulator",
      "device": {
        "avdName": "Pixel_4_API_30"
      }
    }
  },
  "apps": {
    "ios.debug": {
      "type": "ios.app",
      "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/UniversalWallet.app"
    },
    "android.debug": {
      "type": "android.apk",
      "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk"
    }
  }
}
```

### **Mobile User Journey Tests**
```typescript
// e2e/mobile-user-journey.e2e.ts
describe('Mobile User Journey', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should complete user registration and first transaction', async () => {
    // Registration flow
    await element(by.id('register-button')).tap();
    
    await element(by.id('phone-input')).typeText('+************');
    await element(by.id('pin-input')).typeText('1234');
    await element(by.id('confirm-pin-input')).typeText('1234');
    await element(by.id('terms-checkbox')).tap();
    await element(by.id('register-submit')).tap();
    
    // OTP verification
    await waitFor(element(by.id('otp-screen'))).toBeVisible().withTimeout(5000);
    await element(by.id('otp-input')).typeText('123456');
    await element(by.id('verify-otp')).tap();
    
    // Should navigate to dashboard
    await waitFor(element(by.id('dashboard-screen'))).toBeVisible().withTimeout(10000);
    await expect(element(by.id('welcome-message'))).toBeVisible();
    
    // Complete profile
    await element(by.id('complete-profile')).tap();
    await element(by.id('first-name')).typeText('John');
    await element(by.id('last-name')).typeText('Doe');
    await element(by.id('save-profile')).tap();
    
    // Link account
    await element(by.id('link-account')).tap();
    await element(by.id('provider-ecocash')).tap();
    await element(by.id('account-number')).typeText('+************');
    await element(by.id('account-pin')).typeText('1234');
    await element(by.id('link-submit')).tap();
    
    await waitFor(element(by.id('account-linked'))).toBeVisible().withTimeout(10000);
    
    // First transaction
    await element(by.id('send-money')).tap();
    await element(by.id('recipient-phone')).typeText('+************');
    await element(by.id('amount-input')).typeText('100');
    await element(by.id('continue-button')).tap();
    
    // Confirm transaction
    await element(by.id('confirm-button')).tap();
    await element(by.id('pin-input')).typeText('1234');
    await element(by.id('submit-button')).tap();
    
    // Verify transaction success
    await waitFor(element(by.id('transaction-success'))).toBeVisible().withTimeout(15000);
    await expect(element(by.text('Transaction completed successfully'))).toBeVisible();
  });

  it('should handle biometric authentication', async () => {
    // Enable biometric authentication
    await element(by.id('profile-tab')).tap();
    await element(by.id('security-settings')).tap();
    await element(by.id('enable-biometric')).tap();
    
    // Mock biometric authentication
    await device.setBiometricEnrollment(true);
    
    // Logout and login with biometric
    await element(by.id('logout-button')).tap();
    await element(by.id('biometric-login')).tap();
    
    // Simulate successful biometric authentication
    await device.matchBiometric();
    
    // Should navigate to dashboard
    await waitFor(element(by.id('dashboard-screen'))).toBeVisible().withTimeout(5000);
  });

  it('should work offline and sync when online', async () => {
    // Login first
    await element(by.id('login-button')).tap();
    await element(by.id('phone-input')).typeText('+************');
    await element(by.id('pin-input')).typeText('1234');
    await element(by.id('login-submit')).tap();
    
    await waitFor(element(by.id('dashboard-screen'))).toBeVisible().withTimeout(10000);
    
    // Go offline
    await device.setNetworkConnection('none');
    
    // Try to make a transaction (should queue)
    await element(by.id('send-money')).tap();
    await element(by.id('recipient-phone')).typeText('+************');
    await element(by.id('amount-input')).typeText('50');
    await element(by.id('continue-button')).tap();
    await element(by.id('confirm-button')).tap();
    await element(by.id('pin-input')).typeText('1234');
    await element(by.id('submit-button')).tap();
    
    // Should show offline message
    await expect(element(by.text('Transaction queued for when online'))).toBeVisible();
    
    // Go back online
    await device.setNetworkConnection('wifi');
    
    // Should sync and process transaction
    await waitFor(element(by.text('Transaction completed successfully')))
      .toBeVisible().withTimeout(30000);
  });
});
```

---

## 🔄 **Cross-Platform Integration Tests**

### **API Contract Testing**
```typescript
// e2e/api-contract.e2e.ts
describe('API Contract Tests', () => {
  const apiUrl = 'http://localhost:8080/api/v1';
  let authToken: string;

  beforeAll(async () => {
    // Get auth token
    const response = await fetch(`${apiUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const data = await response.json();
    authToken = data.data.accessToken;
  });

  it('should maintain API contract for user endpoints', async () => {
    const response = await fetch(`${apiUrl}/users/profile`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data).toMatchObject({
      success: true,
      data: {
        userId: expect.any(String),
        phoneNumber: expect.stringMatching(/^\+263\d{9}$/),
        userType: expect.stringMatching(/^(personal|business|agent|admin)$/),
        kycLevel: expect.stringMatching(/^(basic|enhanced|business|agent)$/),
        status: expect.stringMatching(/^(active|suspended|pending)$/)
      },
      timestamp: expect.any(String)
    });
  });

  it('should maintain API contract for transaction endpoints', async () => {
    const transferResponse = await fetch(`${apiUrl}/transactions/transfer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipientPhone: '+************',
        amount: 100.00,
        sourceAccountId: 'test-account-id',
        description: 'Test transfer'
      })
    });
    
    expect(transferResponse.status).toBe(200);
    
    const transferData = await transferResponse.json();
    expect(transferData).toMatchObject({
      success: true,
      data: {
        transactionId: expect.any(String),
        referenceNumber: expect.stringMatching(/^UW\d{8}\d{3}$/),
        status: 'pending',
        amount: 100.00,
        fee: expect.any(Number),
        totalAmount: expect.any(Number),
        recipient: {
          phone: '+************'
        }
      }
    });
  });
});
```

**This comprehensive end-to-end testing framework ensures reliable user experiences across all platforms and validates complete business workflows for the UniversalWallet platform.** 🎯
