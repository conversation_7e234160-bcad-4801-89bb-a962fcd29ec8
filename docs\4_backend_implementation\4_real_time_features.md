# 4. Real-Time Features

## ⚡ **Real-Time Features Overview**

The Taxicab platform leverages Supabase Realtime for live data synchronization, providing instant updates for ride tracking, driver locations, notifications, and driver community features. This document covers the complete implementation of real-time functionality.

### **Real-Time Architecture Principles**
- **Event-Driven Updates**: All real-time features built on database change events
- **Selective Subscriptions**: Users receive only relevant updates to minimize bandwidth
- **Conflict Resolution**: Proper handling of concurrent updates and data conflicts
- **Offline Resilience**: Graceful handling of connection drops and reconnections
- **Performance Optimized**: Efficient message distribution and filtering

## 🚗 **Live Ride Tracking Implementation**

### **1. Real-Time Ride Status Updates**
```typescript
// packages/api-client/src/services/realtimeRideService.ts
import { supabase } from '../supabase';
import type { RealtimeChannel } from '@supabase/supabase-js';

export interface RideStatusUpdate {
  ride_id: string;
  status: string;
  driver_id?: string;
  estimated_arrival?: number;
  current_location?: {
    latitude: number;
    longitude: number;
  };
  timestamp: string;
}

export class RealtimeRideService {
  private activeSubscriptions: Map<string, RealtimeChannel> = new Map();
  private eventListeners: Map<string, Set<Function>> = new Map();

  /**
   * Subscribe to ride status updates
   */
  async subscribeToRide(
    rideId: string,
    onUpdate: (update: RideStatusUpdate) => void
  ): Promise<void> {
    try {
      const channelName = `ride:${rideId}`;
      
      // Check if already subscribed
      if (this.activeSubscriptions.has(channelName)) {
        this.addEventListener(channelName, onUpdate);
        return;
      }

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'rides',
            filter: `id=eq.${rideId}`,
          },
          (payload) => {
            const update = this.transformRidePayload(payload);
            this.notifyListeners(channelName, update);
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to ride updates: ${rideId}`);
          } else if (status === 'CHANNEL_ERROR') {
            console.error(`Failed to subscribe to ride: ${rideId}`);
            this.handleSubscriptionError(channelName);
          }
        });

      this.activeSubscriptions.set(channelName, channel);
      this.addEventListener(channelName, onUpdate);
    } catch (error) {
      console.error('Failed to subscribe to ride updates:', error);
      throw error;
    }
  }

  /**
   * Subscribe to driver location updates for active ride
   */
  async subscribeToDriverLocation(
    rideId: string,
    driverId: string,
    onLocationUpdate: (location: DriverLocationUpdate) => void
  ): Promise<void> {
    try {
      const channelName = `driver-location:${rideId}`;
      
      if (this.activeSubscriptions.has(channelName)) {
        this.addEventListener(channelName, onLocationUpdate);
        return;
      }

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'driver_locations',
            filter: `driver_id=eq.${driverId}`,
          },
          (payload) => {
            const locationUpdate = this.transformLocationPayload(payload.new);
            this.notifyListeners(channelName, locationUpdate);
          }
        )
        .subscribe();

      this.activeSubscriptions.set(channelName, channel);
      this.addEventListener(channelName, onLocationUpdate);
    } catch (error) {
      console.error('Failed to subscribe to driver location:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from ride updates
   */
  async unsubscribeFromRide(rideId: string): Promise<void> {
    const channelName = `ride:${rideId}`;
    await this.unsubscribeChannel(channelName);
    
    const locationChannelName = `driver-location:${rideId}`;
    await this.unsubscribeChannel(locationChannelName);
  }

  /**
   * Update ride status (triggers real-time broadcast)
   */
  async updateRideStatus(
    rideId: string,
    status: string,
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString(),
        ...additionalData,
      };

      // Add timestamp for specific status changes
      switch (status) {
        case 'accepted':
          updateData.accepted_at = new Date().toISOString();
          break;
        case 'driver_arrived':
          // Driver has arrived at pickup location
          break;
        case 'in_progress':
          updateData.started_at = new Date().toISOString();
          break;
        case 'completed':
          updateData.completed_at = new Date().toISOString();
          break;
        case 'cancelled':
          updateData.cancelled_at = new Date().toISOString();
          break;
      }

      const { error } = await supabase
        .from('rides')
        .update(updateData)
        .eq('id', rideId);

      if (error) {
        throw new Error(`Failed to update ride status: ${error.message}`);
      }
    } catch (error) {
      console.error('Error updating ride status:', error);
      throw error;
    }
  }

  private transformRidePayload(payload: any): RideStatusUpdate {
    const { new: rideData, eventType } = payload;
    
    return {
      ride_id: rideData.id,
      status: rideData.status,
      driver_id: rideData.driver_id,
      estimated_arrival: rideData.estimated_arrival,
      timestamp: rideData.updated_at,
    };
  }

  private transformLocationPayload(locationData: any): DriverLocationUpdate {
    const coordinates = this.parsePostGISPoint(locationData.coordinates);
    
    return {
      driver_id: locationData.driver_id,
      coordinates,
      heading: locationData.heading,
      speed: locationData.speed,
      accuracy: locationData.accuracy,
      timestamp: locationData.timestamp,
    };
  }

  private parsePostGISPoint(pointString: string): { latitude: number; longitude: number } {
    const matches = pointString.match(/POINT\(([^)]+)\)/);
    if (!matches) throw new Error('Invalid point format');
    
    const [longitude, latitude] = matches[1].split(' ').map(Number);
    return { latitude, longitude };
  }

  private addEventListener(channelName: string, listener: Function): void {
    if (!this.eventListeners.has(channelName)) {
      this.eventListeners.set(channelName, new Set());
    }
    this.eventListeners.get(channelName)!.add(listener);
  }

  private notifyListeners(channelName: string, data: any): void {
    const listeners = this.eventListeners.get(channelName);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  private async unsubscribeChannel(channelName: string): Promise<void> {
    const channel = this.activeSubscriptions.get(channelName);
    if (channel) {
      await supabase.removeChannel(channel);
      this.activeSubscriptions.delete(channelName);
      this.eventListeners.delete(channelName);
    }
  }

  private handleSubscriptionError(channelName: string): void {
    // Implement retry logic with exponential backoff
    setTimeout(() => {
      console.log(`Retrying subscription: ${channelName}`);
      // Retry subscription logic here
    }, 5000);
  }
}

export interface DriverLocationUpdate {
  driver_id: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  heading?: number;
  speed?: number;
  accuracy?: number;
  timestamp: string;
}
```

### **2. Driver Location Broadcasting**
```typescript
// packages/api-client/src/services/driverLocationService.ts
export class DriverLocationService {
  private locationUpdateInterval?: NodeJS.Timeout;
  private isTracking: boolean = false;
  private lastKnownLocation?: GeolocationPosition;

  /**
   * Start broadcasting driver location
   */
  async startLocationTracking(driverId: string): Promise<void> {
    if (this.isTracking) {
      console.warn('Location tracking already active');
      return;
    }

    try {
      // Check if geolocation is available
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      this.isTracking = true;
      
      // Get initial position
      const initialPosition = await this.getCurrentPosition();
      await this.updateDriverLocation(driverId, initialPosition);
      
      // Set up periodic location updates (every 5 seconds)
      this.locationUpdateInterval = setInterval(async () => {
        try {
          const position = await this.getCurrentPosition();
          
          // Only update if location has changed significantly (>10 meters)
          if (this.hasLocationChanged(position, this.lastKnownLocation)) {
            await this.updateDriverLocation(driverId, position);
            this.lastKnownLocation = position;
          }
        } catch (error) {
          console.error('Failed to update driver location:', error);
          // Continue tracking even if one update fails
        }
      }, 5000);

      console.log('Driver location tracking started');
    } catch (error) {
      this.isTracking = false;
      console.error('Failed to start location tracking:', error);
      throw error;
    }
  }

  /**
   * Stop broadcasting driver location
   */
  stopLocationTracking(): void {
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = undefined;
    }
    
    this.isTracking = false;
    this.lastKnownLocation = undefined;
    console.log('Driver location tracking stopped');
  }

  /**
   * Get current GPS position
   */
  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 5000,
        }
      );
    });
  }

  /**
   * Update driver location in database (triggers real-time broadcast)
   */
  private async updateDriverLocation(
    driverId: string,
    position: GeolocationPosition
  ): Promise<void> {
    try {
      const { coords } = position;
      
      const { error } = await supabase
        .from('driver_locations')
        .insert({
          driver_id: driverId,
          coordinates: `POINT(${coords.longitude} ${coords.latitude})`,
          heading: coords.heading || null,
          speed: coords.speed || null,
          accuracy: coords.accuracy,
          timestamp: new Date().toISOString(),
        });

      if (error) {
        throw new Error(`Failed to update location: ${error.message}`);
      }

      // Also update driver's last known location
      await supabase
        .from('drivers')
        .update({
          last_location: `POINT(${coords.longitude} ${coords.latitude})`,
          last_location_update: new Date().toISOString(),
        })
        .eq('id', driverId);
    } catch (error) {
      console.error('Error updating driver location:', error);
      throw error;
    }
  }

  /**
   * Check if location has changed significantly
   */
  private hasLocationChanged(
    newPosition: GeolocationPosition,
    lastPosition?: GeolocationPosition
  ): boolean {
    if (!lastPosition) return true;

    const distance = this.calculateDistance(
      {
        latitude: newPosition.coords.latitude,
        longitude: newPosition.coords.longitude,
      },
      {
        latitude: lastPosition.coords.latitude,
        longitude: lastPosition.coords.longitude,
      }
    );

    // Update if moved more than 10 meters
    return distance > 0.01; // 0.01 km = 10 meters
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Get tracking status
   */
  isLocationTracking(): boolean {
    return this.isTracking;
  }
}
```

## 💬 **Driver Community Chat Implementation**

### **1. Real-Time Chat Service**
```typescript
// packages/api-client/src/services/driverChatService.ts
export interface ChatMessage {
  id: string;
  driver_id: string;
  driver_name: string;
  message: string;
  timestamp: string;
  channel: string;
}

export interface OnlineDriver {
  driver_id: string;
  driver_name: string;
  online_at: string;
}

export class DriverChatService {
  private chatChannels: Map<string, RealtimeChannel> = new Map();
  private messageListeners: Map<string, Set<Function>> = new Map();
  private presenceListeners: Map<string, Set<Function>> = new Map();

  /**
   * Join a city-based chat channel
   */
  async joinChatChannel(
    city: string,
    driverId: string,
    driverName: string,
    onMessage?: (message: ChatMessage) => void,
    onPresenceUpdate?: (drivers: OnlineDriver[]) => void
  ): Promise<void> {
    try {
      const channelName = `chat:${city.toLowerCase()}`;
      
      if (this.chatChannels.has(channelName)) {
        console.warn(`Already joined channel: ${channelName}`);
        return;
      }

      const channel = supabase
        .channel(channelName)
        .on('broadcast', { event: 'message' }, (payload) => {
          const message = payload.payload as ChatMessage;
          this.notifyMessageListeners(channelName, message);
        })
        .on('presence', { event: 'sync' }, () => {
          const presenceState = channel.presenceState();
          const onlineDrivers = this.extractOnlineDrivers(presenceState);
          this.notifyPresenceListeners(channelName, onlineDrivers);
        })
        .on('presence', { event: 'join' }, ({ key, newPresences }) => {
          console.log('Driver joined:', key, newPresences);
        })
        .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
          console.log('Driver left:', key, leftPresences);
        })
        .subscribe(async (status) => {
          if (status === 'SUBSCRIBED') {
            // Track driver presence in the channel
            await channel.track({
              driver_id: driverId,
              driver_name: driverName,
              online_at: new Date().toISOString(),
            });
            
            console.log(`Joined chat channel: ${channelName}`);
          } else if (status === 'CHANNEL_ERROR') {
            console.error(`Failed to join chat channel: ${channelName}`);
          }
        });

      this.chatChannels.set(channelName, channel);
      
      if (onMessage) {
        this.addMessageListener(channelName, onMessage);
      }
      
      if (onPresenceUpdate) {
        this.addPresenceListener(channelName, onPresenceUpdate);
      }
    } catch (error) {
      console.error('Failed to join chat channel:', error);
      throw error;
    }
  }

  /**
   * Send message to chat channel
   */
  async sendMessage(
    city: string,
    driverId: string,
    driverName: string,
    message: string
  ): Promise<void> {
    try {
      const channelName = `chat:${city.toLowerCase()}`;
      const channel = this.chatChannels.get(channelName);
      
      if (!channel) {
        throw new Error(`Not connected to ${city} chat channel`);
      }

      // Validate message
      if (!message.trim() || message.length > 1000) {
        throw new Error('Invalid message length');
      }

      const chatMessage: ChatMessage = {
        id: crypto.randomUUID(),
        driver_id: driverId,
        driver_name: driverName,
        message: message.trim(),
        timestamp: new Date().toISOString(),
        channel: city.toLowerCase(),
      };

      // Broadcast message to all drivers in the channel
      await channel.send({
        type: 'broadcast',
        event: 'message',
        payload: chatMessage,
      });

      // Store message in database for history
      await this.storeChatMessage(chatMessage);
    } catch (error) {
      console.error('Failed to send chat message:', error);
      throw error;
    }
  }

  /**
   * Get chat history for a channel
   */
  async getChatHistory(
    city: string,
    limit: number = 50
  ): Promise<ChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          id,
          driver_id,
          message,
          timestamp,
          channel,
          driver:drivers(user:users(full_name))
        `)
        .eq('channel', city.toLowerCase())
        .eq('is_deleted', false)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(`Failed to fetch chat history: ${error.message}`);
      }

      return (data || []).map(msg => ({
        id: msg.id,
        driver_id: msg.driver_id,
        driver_name: msg.driver?.user?.full_name || 'Unknown Driver',
        message: msg.message,
        timestamp: msg.timestamp,
        channel: msg.channel,
      })).reverse(); // Reverse to show oldest first
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      throw error;
    }
  }

  /**
   * Leave chat channel
   */
  async leaveChatChannel(city: string): Promise<void> {
    const channelName = `chat:${city.toLowerCase()}`;
    const channel = this.chatChannels.get(channelName);
    
    if (channel) {
      await supabase.removeChannel(channel);
      this.chatChannels.delete(channelName);
      this.messageListeners.delete(channelName);
      this.presenceListeners.delete(channelName);
      
      console.log(`Left chat channel: ${channelName}`);
    }
  }

  /**
   * Store chat message in database
   */
  private async storeChatMessage(message: ChatMessage): Promise<void> {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .insert({
          id: message.id,
          channel: message.channel,
          driver_id: message.driver_id,
          message: message.message,
          timestamp: message.timestamp,
        });

      if (error) {
        console.error('Failed to store chat message:', error);
        // Don't throw error as message was already broadcast
      }
    } catch (error) {
      console.error('Error storing chat message:', error);
    }
  }

  private extractOnlineDrivers(presenceState: any): OnlineDriver[] {
    const drivers: OnlineDriver[] = [];
    
    Object.values(presenceState).forEach((presences: any) => {
      presences.forEach((presence: any) => {
        drivers.push({
          driver_id: presence.driver_id,
          driver_name: presence.driver_name,
          online_at: presence.online_at,
        });
      });
    });
    
    return drivers;
  }

  private addMessageListener(channelName: string, listener: Function): void {
    if (!this.messageListeners.has(channelName)) {
      this.messageListeners.set(channelName, new Set());
    }
    this.messageListeners.get(channelName)!.add(listener);
  }

  private addPresenceListener(channelName: string, listener: Function): void {
    if (!this.presenceListeners.has(channelName)) {
      this.presenceListeners.set(channelName, new Set());
    }
    this.presenceListeners.get(channelName)!.add(listener);
  }

  private notifyMessageListeners(channelName: string, message: ChatMessage): void {
    const listeners = this.messageListeners.get(channelName);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(message);
        } catch (error) {
          console.error('Error in message listener:', error);
        }
      });
    }
  }

  private notifyPresenceListeners(channelName: string, drivers: OnlineDriver[]): void {
    const listeners = this.presenceListeners.get(channelName);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(drivers);
        } catch (error) {
          console.error('Error in presence listener:', error);
        }
      });
    }
  }
}
```

## 🔔 **Real-Time Notifications**

### **1. Notification Service Implementation**
```typescript
// packages/api-client/src/services/notificationService.ts
export interface NotificationData {
  user_id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
}

export interface Notification extends NotificationData {
  id: string;
  is_read: boolean;
  sent_at: string;
  read_at?: string;
}

export class NotificationService {
  private notificationChannel?: RealtimeChannel;
  private notificationListeners: Set<Function> = new Set();

  /**
   * Subscribe to user-specific notifications
   */
  async subscribeToNotifications(
    userId: string,
    onNotification?: (notification: Notification) => void
  ): Promise<void> {
    try {
      if (this.notificationChannel) {
        console.warn('Already subscribed to notifications');
        return;
      }

      this.notificationChannel = supabase
        .channel(`notifications:${userId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${userId}`,
          },
          (payload) => {
            const notification = payload.new as Notification;
            this.handleNewNotification(notification);
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('Subscribed to notifications');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Failed to subscribe to notifications');
          }
        });

      if (onNotification) {
        this.addNotificationListener(onNotification);
      }
    } catch (error) {
      console.error('Failed to subscribe to notifications:', error);
      throw error;
    }
  }

  /**
   * Send notification (triggers real-time broadcast)
   */
  async sendNotification(notification: NotificationData): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: notification.user_id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data || {},
          sent_at: new Date().toISOString(),
        });

      if (error) {
        throw new Error(`Failed to send notification: ${error.message}`);
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString(),
        })
        .eq('id', notificationId);

      if (error) {
        throw new Error(`Failed to mark notification as read: ${error.message}`);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Get unread notifications count
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) {
        throw new Error(`Failed to get unread count: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  private handleNewNotification(notification: Notification): void {
    // Notify all listeners
    this.notificationListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });

    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  private showBrowserNotification(notification: Notification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon-192x192.png',
        tag: notification.id,
        requireInteraction: notification.type === 'ride_request',
      });
    }
  }

  private addNotificationListener(listener: Function): void {
    this.notificationListeners.add(listener);
  }

  /**
   * Unsubscribe from notifications
   */
  async unsubscribeFromNotifications(): Promise<void> {
    if (this.notificationChannel) {
      await supabase.removeChannel(this.notificationChannel);
      this.notificationChannel = undefined;
      this.notificationListeners.clear();
      console.log('Unsubscribed from notifications');
    }
  }
}
```

This comprehensive real-time features implementation provides live updates for all critical platform operations including ride tracking, driver locations, community chat, and notifications with proper error handling and performance optimization.
