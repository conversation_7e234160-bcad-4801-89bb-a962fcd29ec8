# 1. System Integration Testing

## 🔗 **Integration Testing Overview**

This document provides comprehensive guidelines for system integration testing of the UniversalWallet platform, covering API integration testing, external service integration, end-to-end workflows, and enterprise-level testing strategies.

## 🏗️ **Integration Testing Architecture**

### **Testing Pyramid for Integration**
```
                    E2E Tests
                   /           \
              API Integration Tests
             /                     \
        Service Integration Tests
       /                           \
  Component Integration Tests
 /                               \
Unit Tests                    Contract Tests
```

### **Integration Test Categories**
- **Component Integration**: Testing component interactions within the same service
- **Service Integration**: Testing interactions between microservices
- **API Integration**: Testing REST API endpoints and data flow
- **External Integration**: Testing third-party service integrations
- **End-to-End Integration**: Testing complete user workflows

---

## 🧪 **API Integration Testing**

### **Spring Boot Integration Test Setup**
```java
// src/test/java/com/universalwallet/integration/BaseIntegrationTest.java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
@Testcontainers
@Transactional
@Rollback
public abstract class BaseIntegrationTest {
    
    @Autowired
    protected TestRestTemplate restTemplate;
    
    @Autowired
    protected JwtTokenProvider jwtTokenProvider;
    
    @Autowired
    protected UserRepository userRepository;
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("universalwallet_test")
            .withUsername("test")
            .withPassword("test");
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withExposedPorts(6379);
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
    }
    
    protected HttpHeaders createAuthHeaders(User user) {
        String token = jwtTokenProvider.generateAccessToken(user);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
    
    protected User createTestUser(UserType userType) {
        return User.builder()
                .phoneNumber("+************")
                .userType(userType)
                .status(UserStatus.ACTIVE)
                .kycLevel(KycLevel.ENHANCED)
                .pinHash("$2a$12$encrypted_pin_hash")
                .build();
    }
    
    protected LinkedAccount createTestAccount(User user, ProviderName provider) {
        return LinkedAccount.builder()
                .user(user)
                .providerName(provider)
                .accountType(AccountType.MOBILE_MONEY)
                .accountNumber("+************")
                .accountName("Test Account")
                .lastBalance(new BigDecimal("1000.00"))
                .connectionStatus(ConnectionStatus.CONNECTED)
                .build();
    }
}
```

### **User Authentication Integration Tests**
```java
// src/test/java/com/universalwallet/integration/AuthenticationIntegrationTest.java
@DisplayName("Authentication Integration Tests")
class AuthenticationIntegrationTest extends BaseIntegrationTest {
    
    @Test
    @DisplayName("Should successfully register new user")
    void shouldRegisterNewUser() {
        // Given
        UserRegistrationRequest request = UserRegistrationRequest.builder()
                .phoneNumber("+************")
                .pin("1234")
                .userType(UserType.PERSONAL)
                .build();
        
        // When
        ResponseEntity<ApiResponse<UserRegistrationResponse>> response = restTemplate.postForEntity(
                "/api/v1/users/register",
                request,
                new ParameterizedTypeReference<ApiResponse<UserRegistrationResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData().getPhoneNumber()).isEqualTo("+************");
        assertThat(response.getBody().getData().getStatus()).isEqualTo(UserStatus.PENDING);
        
        // Verify user was created in database
        Optional<User> savedUser = userRepository.findByPhoneNumber("+************");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUserType()).isEqualTo(UserType.PERSONAL);
    }
    
    @Test
    @DisplayName("Should successfully login with valid credentials")
    void shouldLoginWithValidCredentials() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        LoginRequest request = LoginRequest.builder()
                .phoneNumber(user.getPhoneNumber())
                .pin("1234")
                .build();
        
        // When
        ResponseEntity<ApiResponse<AuthenticationResponse>> response = restTemplate.postForEntity(
                "/api/v1/users/login",
                request,
                new ParameterizedTypeReference<ApiResponse<AuthenticationResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData().getAccessToken()).isNotNull();
        assertThat(response.getBody().getData().getRefreshToken()).isNotNull();
        assertThat(response.getBody().getData().getUser().getPhoneNumber()).isEqualTo(user.getPhoneNumber());
    }
    
    @Test
    @DisplayName("Should reject login with invalid credentials")
    void shouldRejectInvalidCredentials() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        LoginRequest request = LoginRequest.builder()
                .phoneNumber(user.getPhoneNumber())
                .pin("wrong_pin")
                .build();
        
        // When
        ResponseEntity<ApiResponse<AuthenticationResponse>> response = restTemplate.postForEntity(
                "/api/v1/users/login",
                request,
                new ParameterizedTypeReference<ApiResponse<AuthenticationResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getError().getCode()).isEqualTo("INVALID_CREDENTIALS");
    }
    
    @Test
    @DisplayName("Should refresh access token with valid refresh token")
    void shouldRefreshAccessToken() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
        
        RefreshTokenRequest request = RefreshTokenRequest.builder()
                .refreshToken(refreshToken)
                .build();
        
        // When
        ResponseEntity<ApiResponse<AuthenticationResponse>> response = restTemplate.postForEntity(
                "/api/v1/auth/refresh",
                request,
                new ParameterizedTypeReference<ApiResponse<AuthenticationResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData().getAccessToken()).isNotNull();
        assertThat(response.getBody().getData().getRefreshToken()).isNotNull();
    }
}
```

### **Transaction Integration Tests**
```java
// src/test/java/com/universalwallet/integration/TransactionIntegrationTest.java
@DisplayName("Transaction Integration Tests")
class TransactionIntegrationTest extends BaseIntegrationTest {
    
    @Autowired
    private LinkedAccountRepository linkedAccountRepository;
    
    @MockBean
    private ExternalProviderService externalProviderService;
    
    @Test
    @DisplayName("Should successfully create interoperable transfer")
    void shouldCreateInteroperableTransfer() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        LinkedAccount sourceAccount = createTestAccount(user, ProviderName.UNIVERSALWALLET);
        linkedAccountRepository.save(sourceAccount);
        
        // Mock external provider response
        when(externalProviderService.sendToEcoCash(anyString(), any(BigDecimal.class), anyString()))
                .thenReturn(TransferResult.success("EXT123456"));
        
        TransferRequest request = TransferRequest.builder()
                .recipientPhone("+************")
                .amount(new BigDecimal("100.00"))
                .sourceAccountId(sourceAccount.getAccountId())
                .description("Test transfer")
                .build();
        
        HttpHeaders headers = createAuthHeaders(user);
        HttpEntity<TransferRequest> entity = new HttpEntity<>(request, headers);
        
        // When
        ResponseEntity<ApiResponse<TransactionResponse>> response = restTemplate.exchange(
                "/api/v1/transactions/transfer",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        TransactionResponse transactionResponse = response.getBody().getData();
        assertThat(transactionResponse.getAmount()).isEqualByComparingTo(new BigDecimal("100.00"));
        assertThat(transactionResponse.getRecipient().getPhone()).isEqualTo("+************");
        assertThat(transactionResponse.getStatus()).isEqualTo(TransactionStatus.PENDING);
        
        // Verify external service was called
        verify(externalProviderService).sendToEcoCash(
                eq("+************"),
                eq(new BigDecimal("100.00")),
                anyString()
        );
    }
    
    @Test
    @DisplayName("Should reject transfer with insufficient balance")
    void shouldRejectTransferWithInsufficientBalance() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        LinkedAccount sourceAccount = createTestAccount(user, ProviderName.UNIVERSALWALLET);
        sourceAccount.setLastBalance(new BigDecimal("50.00")); // Insufficient balance
        linkedAccountRepository.save(sourceAccount);
        
        TransferRequest request = TransferRequest.builder()
                .recipientPhone("+************")
                .amount(new BigDecimal("100.00"))
                .sourceAccountId(sourceAccount.getAccountId())
                .description("Test transfer")
                .build();
        
        HttpHeaders headers = createAuthHeaders(user);
        HttpEntity<TransferRequest> entity = new HttpEntity<>(request, headers);
        
        // When
        ResponseEntity<ApiResponse<TransactionResponse>> response = restTemplate.exchange(
                "/api/v1/transactions/transfer",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getError().getCode()).isEqualTo("INSUFFICIENT_BALANCE");
    }
    
    @Test
    @DisplayName("Should get transaction history with pagination")
    void shouldGetTransactionHistoryWithPagination() {
        // Given
        User user = createTestUser(UserType.PERSONAL);
        userRepository.save(user);
        
        // Create test transactions
        for (int i = 0; i < 25; i++) {
            Transaction transaction = Transaction.builder()
                    .user(user)
                    .referenceNumber("UW2024" + String.format("%06d", i))
                    .transactionType(TransactionType.P2P_TRANSFER)
                    .amount(new BigDecimal("10.00"))
                    .status(TransactionStatus.COMPLETED)
                    .build();
            transactionRepository.save(transaction);
        }
        
        HttpHeaders headers = createAuthHeaders(user);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        
        // When
        ResponseEntity<ApiResponse<TransactionHistoryResponse>> response = restTemplate.exchange(
                "/api/v1/transactions?page=1&limit=10",
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<ApiResponse<TransactionHistoryResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        TransactionHistoryResponse historyResponse = response.getBody().getData();
        assertThat(historyResponse.getTransactions()).hasSize(10);
        assertThat(historyResponse.getPagination().getCurrentPage()).isEqualTo(1);
        assertThat(historyResponse.getPagination().getTotalRecords()).isEqualTo(25);
        assertThat(historyResponse.getPagination().isHasNext()).isTrue();
    }
}
```

---

## 🔌 **External Service Integration Testing**

### **Mock External Services**
```java
// src/test/java/com/universalwallet/integration/ExternalServiceIntegrationTest.java
@DisplayName("External Service Integration Tests")
class ExternalServiceIntegrationTest extends BaseIntegrationTest {
    
    @Autowired
    private ExternalProviderService externalProviderService;
    
    @Test
    @DisplayName("Should handle EcoCash API integration")
    @WireMockTest
    void shouldHandleEcoCashIntegration() {
        // Given
        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/ecocash/api/v2/transactions/send"))
                .withHeader("Authorization", WireMock.matching("Bearer .*"))
                .withRequestBody(WireMock.matchingJsonPath("$.amount"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "success": true,
                                    "transaction_id": "EC123456789",
                                    "status": "pending",
                                    "reference": "UW20240115001"
                                }
                                """)));
        
        // When
        TransferResult result = externalProviderService.sendToEcoCash(
                "+************",
                new BigDecimal("100.00"),
                "UW20240115001"
        );
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getExternalReference()).isEqualTo("EC123456789");
        
        // Verify the request was made correctly
        WireMock.verify(WireMock.postRequestedFor(WireMock.urlEqualTo("/ecocash/api/v2/transactions/send"))
                .withRequestBody(WireMock.matchingJsonPath("$.amount", WireMock.equalTo("100.00")))
                .withRequestBody(WireMock.matchingJsonPath("$.recipient", WireMock.equalTo("+************"))));
    }
    
    @Test
    @DisplayName("Should handle external service timeout")
    @WireMockTest
    void shouldHandleExternalServiceTimeout() {
        // Given
        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/ecocash/api/v2/transactions/send"))
                .willReturn(WireMock.aResponse()
                        .withFixedDelay(35000) // Longer than timeout
                        .withStatus(200)));
        
        // When & Then
        assertThatThrownBy(() -> externalProviderService.sendToEcoCash(
                "+************",
                new BigDecimal("100.00"),
                "UW20240115001"
        )).isInstanceOf(ExternalServiceTimeoutException.class);
    }
    
    @Test
    @DisplayName("Should handle external service error response")
    @WireMockTest
    void shouldHandleExternalServiceError() {
        // Given
        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/ecocash/api/v2/transactions/send"))
                .willReturn(WireMock.aResponse()
                        .withStatus(400)
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "success": false,
                                    "error": "INSUFFICIENT_BALANCE",
                                    "message": "Insufficient balance in sender account"
                                }
                                """)));
        
        // When
        TransferResult result = externalProviderService.sendToEcoCash(
                "+************",
                new BigDecimal("100.00"),
                "UW20240115001"
        );
        
        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getErrorCode()).isEqualTo("INSUFFICIENT_BALANCE");
        assertThat(result.getErrorMessage()).isEqualTo("Insufficient balance in sender account");
    }
}
```

---

## 🔄 **End-to-End Workflow Testing**

### **Complete Transfer Workflow Test**
```java
// src/test/java/com/universalwallet/integration/TransferWorkflowIntegrationTest.java
@DisplayName("Transfer Workflow Integration Tests")
class TransferWorkflowIntegrationTest extends BaseIntegrationTest {
    
    @Test
    @DisplayName("Should complete full interoperable transfer workflow")
    void shouldCompleteFullTransferWorkflow() {
        // Step 1: Create and authenticate user
        User sender = createTestUser(UserType.PERSONAL);
        userRepository.save(sender);
        
        LinkedAccount senderAccount = createTestAccount(sender, ProviderName.UNIVERSALWALLET);
        senderAccount.setLastBalance(new BigDecimal("1000.00"));
        linkedAccountRepository.save(senderAccount);
        
        // Step 2: Initiate transfer
        TransferRequest transferRequest = TransferRequest.builder()
                .recipientPhone("+************")
                .amount(new BigDecimal("100.00"))
                .sourceAccountId(senderAccount.getAccountId())
                .description("Test transfer")
                .build();
        
        HttpHeaders headers = createAuthHeaders(sender);
        HttpEntity<TransferRequest> entity = new HttpEntity<>(transferRequest, headers);
        
        ResponseEntity<ApiResponse<TransactionResponse>> transferResponse = restTemplate.exchange(
                "/api/v1/transactions/transfer",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
        );
        
        assertThat(transferResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        String transactionId = transferResponse.getBody().getData().getTransactionId();
        
        // Step 3: Confirm transfer with PIN
        ConfirmTransferRequest confirmRequest = ConfirmTransferRequest.builder()
                .transactionId(transactionId)
                .pin("1234")
                .build();
        
        HttpEntity<ConfirmTransferRequest> confirmEntity = new HttpEntity<>(confirmRequest, headers);
        
        ResponseEntity<ApiResponse<TransactionResponse>> confirmResponse = restTemplate.exchange(
                "/api/v1/transactions/confirm",
                HttpMethod.POST,
                confirmEntity,
                new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
        );
        
        assertThat(confirmResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(confirmResponse.getBody().getData().getStatus()).isEqualTo(TransactionStatus.PROCESSING);
        
        // Step 4: Wait for async processing and verify completion
        await().atMost(Duration.ofSeconds(30))
                .pollInterval(Duration.ofSeconds(2))
                .until(() -> {
                    ResponseEntity<ApiResponse<TransactionResponse>> statusResponse = restTemplate.exchange(
                            "/api/v1/transactions/" + transactionId,
                            HttpMethod.GET,
                            new HttpEntity<>(headers),
                            new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
                    );
                    
                    return statusResponse.getBody().getData().getStatus() == TransactionStatus.COMPLETED;
                });
        
        // Step 5: Verify final transaction state
        ResponseEntity<ApiResponse<TransactionResponse>> finalResponse = restTemplate.exchange(
                "/api/v1/transactions/" + transactionId,
                HttpMethod.GET,
                new HttpEntity<>(headers),
                new ParameterizedTypeReference<ApiResponse<TransactionResponse>>() {}
        );
        
        TransactionResponse finalTransaction = finalResponse.getBody().getData();
        assertThat(finalTransaction.getStatus()).isEqualTo(TransactionStatus.COMPLETED);
        assertThat(finalTransaction.getCompletedAt()).isNotNull();
        assertThat(finalTransaction.getExternalReference()).isNotNull();
        
        // Step 6: Verify account balance was updated
        ResponseEntity<ApiResponse<AccountBalanceResponse>> balanceResponse = restTemplate.exchange(
                "/api/v1/accounts/balances",
                HttpMethod.GET,
                new HttpEntity<>(headers),
                new ParameterizedTypeReference<ApiResponse<AccountBalanceResponse>>() {}
        );
        
        BigDecimal expectedBalance = new BigDecimal("1000.00")
                .subtract(finalTransaction.getTotalAmount());
        
        assertThat(balanceResponse.getBody().getData().getTotalBalance())
                .isEqualByComparingTo(expectedBalance);
    }
}
```

**This comprehensive system integration testing framework ensures reliable, secure, and performant integration across all UniversalWallet platform components and external services.** 🔗
