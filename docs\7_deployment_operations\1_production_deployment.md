# 1. Production Deployment

## 🚀 **Production Deployment Overview**

This document provides comprehensive guidance for deploying the Taxicab platform to production environments. The deployment strategy focuses on reliability, scalability, and zero-downtime deployments across all platform components.

### **Deployment Architecture**
- **Frontend**: Vercel for web dashboard, App Store/Google Play for mobile apps
- **Backend**: Supabase for database and API, Edge Functions for serverless logic
- **CDN**: Cloudflare for global content delivery and DDoS protection
- **Monitoring**: Comprehensive observability with alerts and dashboards
- **CI/CD**: Automated deployment pipelines with quality gates

## 🏗️ **Infrastructure Setup**

### **1. Supabase Production Configuration**
```yaml
# supabase/config.toml
[api]
enabled = true
port = 54321
schemas = ["public", "auth", "realtime"]
extra_search_path = ["public", "auth"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[studio]
enabled = true
port = 54323

[inbucket]
enabled = false

[storage]
enabled = true
file_size_limit = "50MiB"
image_transformation = true

[auth]
enabled = true
site_url = "https://taxicab.co.zw"
additional_redirect_urls = [
  "https://admin.taxicab.co.zw",
  "taxicab://auth/callback",
  "com.taxicab.driver://auth/callback"
]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true

[auth.email]
enable_signup = false
double_confirm_changes = true
enable_confirmations = true

[auth.sms]
enable_signup = true
enable_confirmations = true
template = "Your Taxicab verification code is {{ .Code }}"

[realtime]
enabled = true
max_concurrent_users = 200

[functions]
verify_jwt = true
```

### **2. Environment Configuration**
```bash
# Production Environment Variables

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database Configuration
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
DIRECT_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-key
ECOCASH_API_KEY=your-ecocash-key
ONEMONEY_API_KEY=your-onemoney-key
SMS_PROVIDER_API_KEY=your-sms-provider-key

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
API_RATE_LIMIT=1000

# Monitoring
SENTRY_DSN=your-sentry-dsn
DATADOG_API_KEY=your-datadog-key
NEW_RELIC_LICENSE_KEY=your-newrelic-key

# CDN and Storage
CLOUDFLARE_ZONE_ID=your-cloudflare-zone
AWS_S3_BUCKET=taxicab-assets
AWS_REGION=us-east-1

# Application URLs
ADMIN_DASHBOARD_URL=https://admin.taxicab.co.zw
API_BASE_URL=https://api.taxicab.co.zw
WEBSOCKET_URL=wss://realtime.taxicab.co.zw
```

### **3. Domain and SSL Configuration**
```nginx
# nginx.conf for reverse proxy (if needed)
server {
    listen 443 ssl http2;
    server_name admin.taxicab.co.zw;
    
    ssl_certificate /etc/ssl/certs/taxicab.crt;
    ssl_certificate_key /etc/ssl/private/taxicab.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    location / {
        proxy_pass https://taxicab-admin.vercel.app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/ {
        proxy_pass https://your-project.supabase.co/rest/v1/;
        proxy_set_header apikey $supabase_anon_key;
        proxy_set_header Authorization "Bearer $supabase_anon_key";
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name admin.taxicab.co.zw;
    return 301 https://$server_name$request_uri;
}
```

## 📱 **Mobile App Deployment**

### **1. iOS App Store Deployment**
```yaml
# .github/workflows/ios-deploy.yml
name: iOS Production Deployment

on:
  push:
    tags:
      - 'v*'
    paths:
      - 'apps/passenger-app/**'
      - 'apps/driver-app/**'

jobs:
  deploy-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
      
      - name: Install Fastlane
        run: gem install fastlane
      
      - name: Setup iOS certificates
        env:
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
        run: |
          cd apps/passenger-app/ios
          fastlane match appstore --readonly
      
      - name: Build and deploy to App Store
        env:
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
        run: |
          cd apps/passenger-app/ios
          fastlane release
```

### **2. Android Play Store Deployment**
```yaml
# .github/workflows/android-deploy.yml
name: Android Production Deployment

on:
  push:
    tags:
      - 'v*'
    paths:
      - 'apps/passenger-app/**'
      - 'apps/driver-app/**'

jobs:
  deploy-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Setup JDK
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'temurin'
      
      - name: Setup Android SDK
        uses: android-actions/setup-android@v2
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
      
      - name: Install Fastlane
        run: gem install fastlane
      
      - name: Decode keystore
        env:
          ENCODED_STRING: ${{ secrets.KEYSTORE_BASE64 }}
        run: |
          echo $ENCODED_STRING | base64 -d > apps/passenger-app/android/app/release.keystore
      
      - name: Build and deploy to Play Store
        env:
          STORE_PASSWORD: ${{ secrets.STORE_PASSWORD }}
          KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
        run: |
          cd apps/passenger-app/android
          fastlane deploy
```

### **3. Fastlane Configuration**
```ruby
# apps/passenger-app/ios/fastlane/Fastfile
default_platform(:ios)

platform :ios do
  desc "Deploy to App Store"
  lane :release do
    # Increment build number
    increment_build_number(xcodeproj: "TaxicabPassenger.xcodeproj")
    
    # Get certificates and provisioning profiles
    match(type: "appstore")
    
    # Build the app
    build_app(
      scheme: "TaxicabPassenger",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "com.taxicab.passenger" => "match AppStore com.taxicab.passenger"
        }
      }
    )
    
    # Upload to App Store Connect
    upload_to_app_store(
      force: true,
      reject_if_possible: true,
      skip_metadata: false,
      skip_screenshots: false,
      submit_for_review: true,
      automatic_release: false,
      submission_information: {
        add_id_info_uses_idfa: false,
        add_id_info_serves_ads: false,
        add_id_info_tracks_install: false,
        add_id_info_tracks_action: false,
        add_id_info_limits_tracking: false
      }
    )
    
    # Send notification
    slack(
      message: "Successfully deployed Taxicab Passenger app to App Store! 🚀",
      channel: "#deployments"
    )
  end
end

# apps/passenger-app/android/fastlane/Fastfile
default_platform(:android)

platform :android do
  desc "Deploy to Google Play Store"
  lane :deploy do
    # Increment version code
    increment_version_code(
      gradle_file_path: "app/build.gradle"
    )
    
    # Build the app
    gradle(
      task: "bundle",
      build_type: "Release",
      properties: {
        "android.injected.signing.store.file" => "release.keystore",
        "android.injected.signing.store.password" => ENV["STORE_PASSWORD"],
        "android.injected.signing.key.alias" => "taxicab",
        "android.injected.signing.key.password" => ENV["KEY_PASSWORD"]
      }
    )
    
    # Upload to Play Store
    upload_to_play_store(
      track: "production",
      release_status: "draft",
      aab: "app/build/outputs/bundle/release/app-release.aab"
    )
    
    # Send notification
    slack(
      message: "Successfully deployed Taxicab Passenger app to Play Store! 🚀",
      channel: "#deployments"
    )
  end
end
```

## 🌐 **Web Application Deployment**

### **1. Vercel Deployment Configuration**
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "apps/admin-dashboard/package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/admin/(.*)",
      "dest": "apps/admin-dashboard/$1"
    },
    {
      "src": "/api/(.*)",
      "dest": "https://your-project.supabase.co/rest/v1/$1",
      "headers": {
        "apikey": "$SUPABASE_ANON_KEY"
      }
    }
  ],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY": "@google-maps-key"
  },
  "functions": {
    "apps/admin-dashboard/pages/api/**/*.js": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        }
      ]
    }
  ]
}
```

### **2. GitHub Actions CI/CD Pipeline**
```yaml
# .github/workflows/deploy-production.yml
name: Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:ci
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Run security audit
        run: npm audit --audit-level high

  deploy-web:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}

  deploy-database:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
      
      - name: Run database migrations
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_ID }}
          supabase db push

  notify:
    needs: [deploy-web, deploy-database]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify deployment status
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## 🔒 **Security and Compliance**

### **1. Security Headers Configuration**
```typescript
// apps/admin-dashboard/next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  },
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app *.supabase.co;
      style-src 'self' 'unsafe-inline' fonts.googleapis.com;
      img-src 'self' data: blob: *.supabase.co *.googleapis.com;
      font-src 'self' fonts.gstatic.com;
      connect-src 'self' *.supabase.co *.googleapis.com wss:;
      frame-src 'none';
    `.replace(/\s{2,}/g, ' ').trim()
  }
];

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

### **2. Environment Security Checklist**
```markdown
# Production Security Checklist

## Database Security
- [ ] Row Level Security (RLS) enabled on all tables
- [ ] Database passwords rotated and stored securely
- [ ] Connection pooling configured
- [ ] Database backups automated and tested
- [ ] SSL/TLS encryption enabled for all connections

## API Security
- [ ] Rate limiting implemented
- [ ] API keys rotated regularly
- [ ] CORS configured properly
- [ ] Input validation on all endpoints
- [ ] SQL injection protection verified

## Authentication Security
- [ ] JWT tokens have appropriate expiration
- [ ] Refresh token rotation enabled
- [ ] Multi-factor authentication available
- [ ] Password policies enforced
- [ ] Session management secure

## Infrastructure Security
- [ ] HTTPS enforced everywhere
- [ ] Security headers configured
- [ ] DDoS protection enabled
- [ ] Firewall rules configured
- [ ] Regular security scans scheduled

## Compliance
- [ ] GDPR compliance verified
- [ ] Data retention policies implemented
- [ ] Audit logging enabled
- [ ] Privacy policy updated
- [ ] Terms of service current
```

This comprehensive production deployment guide ensures secure, scalable, and reliable deployment of the Taxicab platform across all environments with proper CI/CD pipelines, security measures, and compliance standards.
