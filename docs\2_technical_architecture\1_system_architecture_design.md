# 1. System Architecture Design

## 🏗️ **Overall Architecture Overview**

The Taxicab platform follows a modern, cloud-native architecture built on Supabase as the primary backend-as-a-service platform, with React-based frontends and React Native mobile applications. The architecture is designed for scalability, real-time capabilities, and seamless integration with external services.

### **Architecture Principles**
- **Serverless-First**: Leverage Supabase's serverless infrastructure for automatic scaling
- **Real-Time by Design**: Built-in real-time capabilities for live tracking and notifications
- **Security-First**: Row Level Security (RLS) and authentication at the database level
- **API-Driven**: RESTful APIs with automatic documentation and type safety
- **Mobile-First**: Optimized for mobile performance with offline capabilities

## 🔧 **Technology Stack**

### **Frontend Technologies**

#### **Admin Dashboard**
- **Framework**: React 18+ with TypeScript
- **UI Library**: Shadcn UI components with Tailwind CSS
- **State Management**: Zustand for lightweight state management
- **Data Fetching**: TanStack Query (React Query) for server state
- **Routing**: React Router v6 for client-side routing
- **Build Tool**: Vite for fast development and optimized builds

#### **Mobile Applications**
- **Framework**: React Native 0.72+ with TypeScript
- **Navigation**: React Navigation v6 for native navigation
- **State Management**: Zustand with React Native persistence
- **Maps**: React Native Maps with Google Maps integration
- **Real-time**: Supabase real-time subscriptions
- **Push Notifications**: React Native Firebase for notifications

### **Backend Technologies**

#### **Core Backend (Supabase)**
- **Database**: PostgreSQL 15+ with Row Level Security (RLS)
- **Authentication**: Supabase Auth with JWT tokens and multi-factor authentication
- **Real-time**: Supabase Realtime for live subscriptions
- **Storage**: Supabase Storage for file uploads and media
- **Edge Functions**: Deno-based serverless functions for business logic

#### **External Integrations**
- **Maps & Navigation**: Google Maps API, Places API, Directions API
- **Payments**: EcoCash API, OneMoney API, Stripe for card payments
- **Communications**: Twilio for SMS, WhatsApp Business API
- **AI Services**: Google Cloud Speech-to-Text, Natural Language API

## 🏛️ **System Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Applications                       │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Admin Dashboard│  Passenger App  │      Driver App             │
│  (React +       │  (React Native) │   (React Native)            │
│   Shadcn UI)    │                 │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
                           │
                    HTTPS/WebSocket
                           │
┌─────────────────────────────────────────────────────────────────┐
│                    Supabase Platform                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Supabase API  │  Realtime       │    Edge Functions           │
│   (REST/GraphQL)│  (WebSocket)    │    (Deno Runtime)           │
├─────────────────┼─────────────────┼─────────────────────────────┤
│   Supabase Auth │  PostgreSQL     │    Supabase Storage         │
│   (JWT + MFA)   │  (with RLS)     │    (File Storage)           │
└─────────────────┴─────────────────┴─────────────────────────────┘
                           │
                    External APIs
                           │
┌─────────────────────────────────────────────────────────────────┐
│                   External Services                              │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Google Maps   │   Payment APIs  │   Communication APIs        │
│   • Maps API    │   • EcoCash     │   • Twilio SMS              │
│   • Places API  │   • OneMoney    │   • WhatsApp Business       │
│   • Directions  │   • Stripe      │   • Push Notifications      │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 📊 **Data Flow Architecture**

### **Real-Time Data Flow**

#### **Ride Booking Flow**
1. **Passenger Request**: Passenger submits ride request via mobile app
2. **Database Insert**: Request stored in PostgreSQL with RLS policies
3. **Real-Time Broadcast**: Supabase Realtime broadcasts to nearby drivers
4. **Driver Response**: Driver accepts/declines via real-time subscription
5. **Status Updates**: All parties receive real-time status updates
6. **Trip Tracking**: Live GPS coordinates shared via real-time channels

#### **Location Tracking Flow**
1. **GPS Collection**: Mobile apps collect GPS coordinates
2. **Real-Time Updates**: Coordinates sent to Supabase every 5-10 seconds
3. **Database Storage**: Location history stored with timestamps
4. **Live Broadcasting**: Current location broadcast to relevant users
5. **Map Updates**: Real-time map updates in passenger and admin apps

### **Authentication Flow**

#### **User Authentication**
1. **Phone Verification**: User enters phone number
2. **OTP Generation**: Supabase Auth generates and sends OTP via SMS
3. **Token Creation**: JWT tokens created upon successful verification
4. **RLS Enforcement**: Database queries filtered by user identity
5. **Session Management**: Automatic token refresh and session handling

#### **Multi-Factor Authentication**
1. **Primary Auth**: Phone number and OTP verification
2. **Secondary Factor**: SMS-based 2FA for sensitive operations
3. **Biometric Auth**: Fingerprint/Face ID for mobile app access
4. **Session Security**: Secure session management with automatic logout

## 🔐 **Security Architecture**

### **Database Security (Row Level Security)**

#### **RLS Policy Structure**
```sql
-- Example RLS policies for ride data
CREATE POLICY "Users can view their own rides" ON rides
  FOR SELECT USING (passenger_id = auth.uid() OR driver_id = auth.uid());

CREATE POLICY "Drivers can update assigned rides" ON rides
  FOR UPDATE USING (driver_id = auth.uid());

CREATE POLICY "Admins can view all rides" ON rides
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
```

#### **Data Access Control**
- **User Isolation**: Users can only access their own data
- **Role-Based Access**: Different permissions for passengers, drivers, admins
- **Temporal Access**: Time-based access controls for sensitive operations
- **Audit Logging**: Complete audit trail for all data access

### **API Security**

#### **Authentication & Authorization**
- **JWT Tokens**: Secure token-based authentication
- **Token Refresh**: Automatic token refresh for seamless experience
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS Configuration**: Proper CORS setup for web applications

#### **Data Protection**
- **Encryption in Transit**: HTTPS/TLS for all API communications
- **Encryption at Rest**: Database encryption for sensitive data
- **PII Protection**: Special handling for personally identifiable information
- **Payment Security**: PCI DSS compliance for payment data

## 🚀 **Scalability and Performance**

### **Horizontal Scaling**

#### **Supabase Auto-Scaling**
- **Database Scaling**: Automatic PostgreSQL scaling based on load
- **Edge Functions**: Serverless functions scale automatically
- **CDN Distribution**: Global CDN for static assets and media
- **Connection Pooling**: Efficient database connection management

#### **Performance Optimization**
- **Database Indexing**: Optimized indexes for common queries
- **Query Optimization**: Efficient SQL queries with proper joins
- **Caching Strategy**: Redis caching for frequently accessed data
- **Image Optimization**: Automatic image compression and resizing

### **Real-Time Performance**

#### **WebSocket Optimization**
- **Connection Management**: Efficient WebSocket connection pooling
- **Message Filtering**: Server-side filtering to reduce client load
- **Compression**: Message compression for bandwidth optimization
- **Fallback Mechanisms**: HTTP polling fallback for poor connections

#### **Mobile Performance**
- **Offline Capability**: Local data caching for offline functionality
- **Background Sync**: Automatic data synchronization when online
- **Battery Optimization**: Efficient location tracking and updates
- **Data Usage**: Optimized for limited data plans

## 🔄 **Integration Architecture**

### **External Service Integration**

#### **Google Maps Integration**
```typescript
// Maps service integration
interface MapsService {
  geocode(address: string): Promise<Coordinates>;
  reverseGeocode(coords: Coordinates): Promise<Address>;
  calculateRoute(origin: Coordinates, destination: Coordinates): Promise<Route>;
  estimateTime(route: Route, trafficModel: TrafficModel): Promise<Duration>;
}
```

#### **Payment Gateway Integration**
```typescript
// Payment service abstraction
interface PaymentService {
  processPayment(amount: number, method: PaymentMethod): Promise<PaymentResult>;
  refundPayment(transactionId: string): Promise<RefundResult>;
  getPaymentStatus(transactionId: string): Promise<PaymentStatus>;
}
```

### **Webhook Architecture**

#### **Incoming Webhooks**
- **Payment Notifications**: Real-time payment status updates
- **SMS Delivery**: SMS delivery status confirmations
- **WhatsApp Events**: WhatsApp message and delivery events
- **Map Updates**: Traffic and route update notifications

#### **Outgoing Webhooks**
- **Corporate Integrations**: Trip data to corporate systems
- **Analytics Events**: Custom analytics and reporting
- **Third-Party Notifications**: External system notifications
- **Backup Systems**: Data backup and synchronization

## 📱 **Mobile Architecture**

### **React Native Architecture**

#### **App Structure**
```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── services/          # API and external service calls
├── stores/            # Zustand state management
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
└── constants/         # App constants and configuration
```

#### **State Management**
- **Global State**: Zustand for app-wide state (user, location, rides)
- **Server State**: TanStack Query for API data caching
- **Local Storage**: AsyncStorage for offline data persistence
- **Real-Time State**: Supabase subscriptions for live updates

### **Offline Capabilities**

#### **Data Synchronization**
- **Offline Queue**: Queue API calls when offline
- **Conflict Resolution**: Handle data conflicts when reconnecting
- **Cache Management**: Intelligent cache invalidation and updates
- **Background Sync**: Sync data when app returns to foreground

## 🔧 **Development Architecture**

### **Code Organization**

#### **Monorepo Structure**
```
taxicab/
├── apps/
│   ├── admin-dashboard/    # React admin dashboard
│   ├── passenger-app/      # React Native passenger app
│   └── driver-app/         # React Native driver app
├── packages/
│   ├── shared-types/       # Shared TypeScript types
│   ├── ui-components/      # Shared UI components
│   └── api-client/         # Shared API client
├── supabase/
│   ├── migrations/         # Database migrations
│   ├── functions/          # Edge functions
│   └── config/            # Supabase configuration
└── docs/                  # Project documentation
```

#### **Shared Libraries**
- **Type Definitions**: Shared TypeScript types across all apps
- **API Client**: Unified Supabase client with type safety
- **UI Components**: Shared components between web and mobile
- **Utilities**: Common utility functions and helpers

This comprehensive system architecture provides a solid foundation for building a scalable, secure, and performant ride-hailing platform using modern technologies and best practices.
