# 3. Platform Features and Functional Scope

## 🚀 **Core Service Modules**

### **MoversRide (General Taxi Service)**
**Purpose**: Standard on-demand taxi service for everyday transportation needs

#### **Service Features**
- **Vehicle Categories**
  - MoversLite: Economy vehicles (Toyota Vitz, Nissan March)
  - MoversRide: Standard vehicles (Toyota Corolla, Honda Fit)
  - MoversExec: Premium vehicles (Toyota Camry, Mercedes C-Class)
  - MoversXL: Large capacity vehicles (Toyota Hiace, Nissan Caravan)

- **Booking Options**
  - Immediate pickup (on-demand)
  - Scheduled rides (up to 7 days in advance)
  - Round trip booking with wait time
  - Multiple stops during single trip

- **Pricing Structure**
  - Base fare + distance-based pricing
  - Time-based pricing for traffic delays
  - Surge pricing during peak hours
  - Transparent fare estimation before booking

### **MoversCruiser (School & Work Runs)**
**Purpose**: Scheduled recurring transportation for schools and workplaces

#### **Service Features**
- **Recurring Schedules**
  - Daily school runs with verified drivers
  - Weekly work commute arrangements
  - Monthly corporate contracts
  - Flexible schedule modifications

- **Safety Features**
  - Enhanced driver background checks
  - Child safety seat requirements
  - Parent/guardian notifications
  - Real-time tracking for peace of mind

- **Route Optimization**
  - Multi-pickup route planning
  - Time-efficient scheduling
  - Traffic pattern analysis
  - Backup driver assignments

### **MoversExpress (Small Package & Courier)**
**Purpose**: On-demand delivery service for small packages and documents

#### **Service Features**
- **Package Categories**
  - Documents and letters
  - Small packages (up to 10kg)
  - Food delivery (future expansion)
  - Pharmacy and medical supplies

- **Delivery Options**
  - Same-day delivery
  - Express delivery (under 2 hours)
  - Scheduled delivery
  - Proof of delivery with photos

### **MoversErrands (Personal Errands)**
**Purpose**: Task-based service for personal errands and assistance

#### **Service Features**
- **Errand Types**
  - Grocery shopping with receipt photos
  - Bank and government office visits
  - Document collection and delivery
  - Market shopping and price comparison

- **Service Process**
  - Detailed task instructions
  - Real-time progress updates
  - Photo confirmation of completed tasks
  - Itemized receipts and expense tracking

### **Corporate Transport Solutions**
**Purpose**: Dedicated business transportation management platform

#### **Service Features**
- **Employee Management**
  - Bulk employee onboarding
  - Individual spending limits
  - Department-based access control
  - Travel policy enforcement

- **Booking Management**
  - Bulk ride scheduling
  - Approval workflows for high-value trips
  - Emergency transportation requests
  - VIP service for executives

- **Reporting and Analytics**
  - Detailed expense reporting
  - Employee travel pattern analysis
  - Cost center allocation
  - Budget tracking and alerts

## 🎯 **Advanced Platform Features**

### **AI-Powered Voice Booking System**
**Purpose**: Natural language booking in local languages

#### **Technical Features**
- **Multi-Language Support**
  - Shona voice recognition and processing
  - Ndebele voice recognition and processing
  - English voice recognition and processing
  - Code-switching between languages

- **Natural Language Processing**
  - Location extraction from speech
  - Intent recognition for service types
  - Context understanding for complex requests
  - Fallback to text input when needed

- **Voice Commands Examples**
  - "Ndiri kuda kuenda kuJoina City" → Destination: Joina City
  - "Ndinoda mota yekuenda kubasa mangwana mangwanani" → Scheduled work trip
  - "Book MoversExec to airport" → Premium service to airport

### **Driver Community Platform**
**Purpose**: Foster driver collaboration and information sharing

#### **Community Features**
- **City-Based Chat Channels**
  - #Harare: Harare-based drivers
  - #Bulawayo: Bulawayo-based drivers
  - #Mutare: Mutare-based drivers
  - #General: Platform-wide discussions

- **Information Sharing**
  - Real-time traffic updates
  - Road condition reports
  - Fuel station availability
  - Safety alerts and warnings

- **Professional Development**
  - Driver training announcements
  - Best practice sharing
  - Customer service tips
  - Earnings optimization strategies

### **Driver Profile Sharing System**
**Purpose**: Enable drivers to build direct customer relationships

#### **Profile Features**
- **Digital Business Card**
  - Driver photo and basic information
  - Vehicle details and license plate
  - Customer ratings and reviews
  - Contact information and availability

- **Sharing Mechanisms**
  - SMS with mobile-optimized profile link
  - QR code for quick profile access
  - WhatsApp profile sharing
  - Social media integration

- **Customer Benefits**
  - Save favorite drivers for future bookings
  - Direct booking with preferred drivers
  - Build trust through repeated interactions
  - Access to driver's schedule and availability

### **WhatsApp Integration Platform**
**Purpose**: Complete ride-hailing experience through WhatsApp

#### **WhatsApp Features**
- **Booking Flow**
  - Natural language booking commands
  - Location sharing for pickup points
  - Service type selection through quick replies
  - Fare confirmation and payment links

- **Trip Management**
  - Real-time trip status updates
  - Driver contact information sharing
  - Live location tracking links
  - Trip completion confirmations

- **Customer Support**
  - 24/7 automated customer service
  - Human agent escalation
  - Complaint handling and resolution
  - FAQ and help documentation

## 🏢 **Taxi Association Integration**

### **Association Management System**
**Purpose**: Seamless integration with existing taxi associations and companies

#### **Association Features**
- **Driver Onboarding**
  - Association selection during registration
  - Company-specific verification processes
  - Association membership validation
  - Fleet management for taxi companies

- **Commission Management**
  - Association-specific commission structures
  - Transparent fee calculation
  - Automated commission distribution
  - Financial reporting for associations

- **Brand Integration**
  - Association branding in driver profiles
  - Company-specific vehicle requirements
  - Association-based driver grouping
  - Marketing support for associations

### **Fleet Management Tools**
**Purpose**: Tools for taxi companies to manage their drivers and vehicles

#### **Fleet Features**
- **Vehicle Tracking**
  - Real-time fleet location monitoring
  - Vehicle utilization analytics
  - Maintenance scheduling and reminders
  - Fuel consumption tracking

- **Driver Management**
  - Driver performance monitoring
  - Shift scheduling and management
  - Training program coordination
  - Disciplinary action tracking

- **Financial Management**
  - Revenue sharing calculations
  - Driver payment processing
  - Expense tracking and reporting
  - Profit and loss analysis

## 📱 **Platform-Specific Features**

### **Mobile Applications (React Native)**

#### **Passenger App Features**
- **Intuitive Booking Interface**
  - Map-based pickup and destination selection
  - Service type selection with visual indicators
  - Real-time fare estimation
  - Multiple payment method integration

- **Trip Management**
  - Live driver tracking with ETA updates
  - In-app communication with driver
  - Trip sharing with family/friends
  - Emergency SOS button

- **Account Management**
  - Profile customization and preferences
  - Payment method management
  - Trip history and receipts
  - Favorite locations and drivers

#### **Driver App Features**
- **Ride Management Dashboard**
  - Online/offline status toggle
  - Incoming ride request notifications
  - Trip navigation and guidance
  - Earnings tracking and analytics

- **Customer Interaction Tools**
  - Profile sharing functionality
  - Customer communication features
  - Rating and feedback system
  - Favorite passenger management

- **Community Integration**
  - City-based chat channels
  - Information sharing platform
  - Association announcements
  - Professional development resources

### **Web Dashboard (React + Shadcn UI)**

#### **Admin Dashboard Features**
- **Platform Overview**
  - Real-time activity monitoring
  - Key performance indicator displays
  - System health and status monitoring
  - Quick action buttons for common tasks

- **User Management**
  - Driver verification and approval
  - Passenger account management
  - Corporate client onboarding
  - Association partnership management

- **Analytics and Reporting**
  - Comprehensive business intelligence
  - Custom report generation
  - Data visualization and insights
  - Export capabilities for stakeholders

#### **Corporate Portal Features**
- **Employee Transportation Management**
  - Bulk booking interface
  - Employee travel policy configuration
  - Approval workflow management
  - Emergency transportation requests

- **Financial Management**
  - Expense tracking and reporting
  - Budget management and alerts
  - Invoice generation and processing
  - Cost center allocation

## 🔧 **Technical Feature Implementation**

### **Real-Time Features (Supabase)**
- **Live Location Tracking**: Real-time GPS updates using Supabase subscriptions
- **Instant Notifications**: Push notifications for trip status changes
- **Live Chat**: Real-time messaging between drivers and passengers
- **Dynamic Pricing**: Real-time fare calculations based on demand and traffic

### **Security Features**
- **Row Level Security**: Database-level security for data protection
- **Multi-Factor Authentication**: SMS-based 2FA for all user types
- **Encrypted Communications**: End-to-end encryption for sensitive data
- **Audit Trails**: Complete activity logging for compliance and security

### **Performance Features**
- **Offline Capability**: Basic functionality during poor connectivity
- **Caching Strategy**: Intelligent data caching for improved performance
- **Load Balancing**: Automatic scaling based on demand
- **CDN Integration**: Fast content delivery for global accessibility

This comprehensive feature specification ensures that the Taxicab platform provides a complete, competitive, and locally-adapted ride-hailing experience for the Zimbabwean market.
