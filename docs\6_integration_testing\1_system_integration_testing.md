# 1. System Integration Testing

## 🔗 **Integration Testing Overview**

System integration testing for the Taxicab platform ensures all components work together seamlessly across the entire technology stack. This includes testing the integration between React frontend, Supabase backend, mobile applications, and external services.

### **Integration Testing Principles**
- **End-to-End Workflows**: Test complete user journeys across all systems
- **API Integration**: Verify all API endpoints and data flow
- **Real-Time Features**: Test live data synchronization and notifications
- **External Services**: Validate third-party integrations (Google Maps, payment gateways)
- **Cross-Platform Compatibility**: Ensure consistency across web and mobile

## 🏗️ **Testing Architecture**

### **1. Testing Environment Setup**
```typescript
// tests/integration/setup/testEnvironment.ts
import { createClient } from '@supabase/supabase-js';
import { QueryClient } from '@tanstack/react-query';

export class TestEnvironment {
  private static instance: TestEnvironment;
  public supabase: any;
  public queryClient: QueryClient;
  
  private constructor() {
    this.setupSupabase();
    this.setupQueryClient();
  }

  static getInstance(): TestEnvironment {
    if (!TestEnvironment.instance) {
      TestEnvironment.instance = new TestEnvironment();
    }
    return TestEnvironment.instance;
  }

  private setupSupabase() {
    this.supabase = createClient(
      process.env.SUPABASE_TEST_URL!,
      process.env.SUPABASE_TEST_ANON_KEY!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        },
      }
    );
  }

  private setupQueryClient() {
    this.queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          cacheTime: 0,
        },
        mutations: {
          retry: false,
        },
      },
    });
  }

  async cleanup() {
    // Clean up test data
    await this.cleanupTestUsers();
    await this.cleanupTestRides();
    await this.cleanupTestDrivers();
    
    // Clear query cache
    this.queryClient.clear();
  }

  private async cleanupTestUsers() {
    await this.supabase
      .from('users')
      .delete()
      .like('email', '%test%');
  }

  private async cleanupTestRides() {
    await this.supabase
      .from('rides')
      .delete()
      .like('pickup_address', '%test%');
  }

  private async cleanupTestDrivers() {
    await this.supabase
      .from('drivers')
      .delete()
      .like('license_number', '%TEST%');
  }
}

// Test data factory
export class TestDataFactory {
  static createTestUser(overrides: Partial<any> = {}) {
    return {
      phone: '+263771234567',
      full_name: 'Test User',
      email: '<EMAIL>',
      role: 'passenger',
      ...overrides,
    };
  }

  static createTestDriver(overrides: Partial<any> = {}) {
    return {
      license_number: 'TEST123456',
      license_expiry: '2025-12-31',
      status: 'approved',
      rating: 4.5,
      is_online: true,
      ...overrides,
    };
  }

  static createTestRide(overrides: Partial<any> = {}) {
    return {
      pickup_address: 'Test Pickup Location',
      pickup_coordinates: 'POINT(31.0335 -17.8252)',
      destination_address: 'Test Destination',
      destination_coordinates: 'POINT(31.0974 -17.8145)',
      service_type: 'movers_ride',
      estimated_fare: 10.50,
      status: 'requested',
      ...overrides,
    };
  }

  static createTestVehicle(overrides: Partial<any> = {}) {
    return {
      make: 'Toyota',
      model: 'Corolla',
      year: 2020,
      color: 'White',
      license_plate: 'TEST-001',
      category: 'standard',
      ...overrides,
    };
  }
}
```

### **2. Integration Test Configuration**
```typescript
// tests/integration/config/jest.config.js
module.exports = {
  displayName: 'Integration Tests',
  testMatch: ['<rootDir>/tests/integration/**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/tests/integration/setup/setupTests.ts'],
  testEnvironment: 'node',
  testTimeout: 30000,
  maxWorkers: 1, // Run tests sequentially to avoid database conflicts
  globalSetup: '<rootDir>/tests/integration/setup/globalSetup.ts',
  globalTeardown: '<rootDir>/tests/integration/setup/globalTeardown.ts',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1',
  },
};

// tests/integration/setup/setupTests.ts
import { TestEnvironment } from './testEnvironment';

let testEnv: TestEnvironment;

beforeAll(async () => {
  testEnv = TestEnvironment.getInstance();
});

afterEach(async () => {
  await testEnv.cleanup();
});

// Make test environment available globally
global.testEnv = testEnv;
```

## 🔄 **API Integration Tests**

### **1. Authentication Flow Integration**
```typescript
// tests/integration/auth/authFlow.test.ts
import { TestEnvironment, TestDataFactory } from '../setup/testEnvironment';

describe('Authentication Flow Integration', () => {
  let testEnv: TestEnvironment;

  beforeAll(() => {
    testEnv = TestEnvironment.getInstance();
  });

  describe('Phone Authentication', () => {
    it('should complete full phone authentication flow', async () => {
      const testUser = TestDataFactory.createTestUser();
      
      // Step 1: Send OTP
      const { data: otpData, error: otpError } = await testEnv.supabase.auth.signInWithOtp({
        phone: testUser.phone,
        options: {
          shouldCreateUser: true,
        },
      });

      expect(otpError).toBeNull();
      expect(otpData).toBeDefined();

      // Step 2: Verify OTP (using test OTP)
      const { data: verifyData, error: verifyError } = await testEnv.supabase.auth.verifyOtp({
        phone: testUser.phone,
        token: '123456', // Test OTP
        type: 'sms',
      });

      expect(verifyError).toBeNull();
      expect(verifyData.user).toBeDefined();
      expect(verifyData.session).toBeDefined();

      // Step 3: Verify user profile creation
      const { data: userProfile, error: profileError } = await testEnv.supabase
        .from('users')
        .select('*')
        .eq('id', verifyData.user!.id)
        .single();

      expect(profileError).toBeNull();
      expect(userProfile.phone).toBe(testUser.phone);
    });

    it('should handle invalid OTP gracefully', async () => {
      const testUser = TestDataFactory.createTestUser();
      
      // Send OTP first
      await testEnv.supabase.auth.signInWithOtp({
        phone: testUser.phone,
      });

      // Try to verify with invalid OTP
      const { error } = await testEnv.supabase.auth.verifyOtp({
        phone: testUser.phone,
        token: '000000',
        type: 'sms',
      });

      expect(error).toBeDefined();
      expect(error.message).toContain('Invalid');
    });
  });

  describe('Role-Based Access Control', () => {
    it('should enforce driver-only access to driver endpoints', async () => {
      // Create passenger user
      const passengerUser = TestDataFactory.createTestUser({ role: 'passenger' });
      
      const { data: authData } = await testEnv.supabase.auth.signInWithOtp({
        phone: passengerUser.phone,
      });

      // Try to access driver-only endpoint
      const { error } = await testEnv.supabase
        .from('driver_earnings')
        .select('*');

      expect(error).toBeDefined();
      expect(error.message).toContain('permission');
    });

    it('should allow admin access to all resources', async () => {
      // Create admin user
      const adminUser = TestDataFactory.createTestUser({ role: 'admin' });
      
      await testEnv.supabase.auth.signInWithOtp({
        phone: adminUser.phone,
      });

      // Should be able to access all tables
      const { error: usersError } = await testEnv.supabase
        .from('users')
        .select('*')
        .limit(1);

      const { error: ridesError } = await testEnv.supabase
        .from('rides')
        .select('*')
        .limit(1);

      expect(usersError).toBeNull();
      expect(ridesError).toBeNull();
    });
  });
});
```

### **2. Ride Booking Integration**
```typescript
// tests/integration/rides/rideBooking.test.ts
describe('Ride Booking Integration', () => {
  let testEnv: TestEnvironment;
  let passengerUser: any;
  let driverUser: any;

  beforeAll(async () => {
    testEnv = TestEnvironment.getInstance();
    
    // Set up test users
    passengerUser = await createTestPassenger();
    driverUser = await createTestDriver();
  });

  describe('Complete Ride Flow', () => {
    it('should handle complete ride booking and completion flow', async () => {
      // Step 1: Passenger creates ride request
      await authenticateUser(passengerUser);
      
      const rideData = TestDataFactory.createTestRide({
        passenger_id: passengerUser.id,
      });

      const { data: ride, error: createError } = await testEnv.supabase
        .from('rides')
        .insert(rideData)
        .select()
        .single();

      expect(createError).toBeNull();
      expect(ride.status).toBe('requested');

      // Step 2: Driver accepts ride
      await authenticateUser(driverUser);
      
      const { data: updatedRide, error: acceptError } = await testEnv.supabase
        .from('rides')
        .update({
          driver_id: driverUser.driver_id,
          status: 'accepted',
          accepted_at: new Date().toISOString(),
        })
        .eq('id', ride.id)
        .select()
        .single();

      expect(acceptError).toBeNull();
      expect(updatedRide.status).toBe('accepted');
      expect(updatedRide.driver_id).toBe(driverUser.driver_id);

      // Step 3: Driver starts ride
      const { data: startedRide, error: startError } = await testEnv.supabase
        .from('rides')
        .update({
          status: 'in_progress',
          started_at: new Date().toISOString(),
        })
        .eq('id', ride.id)
        .select()
        .single();

      expect(startError).toBeNull();
      expect(startedRide.status).toBe('in_progress');

      // Step 4: Driver completes ride
      const finalFare = 12.50;
      const { data: completedRide, error: completeError } = await testEnv.supabase
        .from('rides')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          final_fare: finalFare,
        })
        .eq('id', ride.id)
        .select()
        .single();

      expect(completeError).toBeNull();
      expect(completedRide.status).toBe('completed');
      expect(completedRide.final_fare).toBe(finalFare);

      // Step 5: Verify earnings record created
      const { data: earnings, error: earningsError } = await testEnv.supabase
        .from('driver_earnings')
        .select('*')
        .eq('ride_id', ride.id)
        .single();

      expect(earningsError).toBeNull();
      expect(earnings.gross_amount).toBe(finalFare);
      expect(earnings.net_earnings).toBeGreaterThan(0);
    });

    it('should handle ride cancellation by passenger', async () => {
      await authenticateUser(passengerUser);
      
      const rideData = TestDataFactory.createTestRide({
        passenger_id: passengerUser.id,
      });

      const { data: ride } = await testEnv.supabase
        .from('rides')
        .insert(rideData)
        .select()
        .single();

      // Cancel ride
      const { data: cancelledRide, error } = await testEnv.supabase
        .from('rides')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancellation_reason: 'Passenger cancelled',
        })
        .eq('id', ride.id)
        .select()
        .single();

      expect(error).toBeNull();
      expect(cancelledRide.status).toBe('cancelled');
      expect(cancelledRide.cancellation_reason).toBe('Passenger cancelled');
    });
  });

  describe('Driver Matching', () => {
    it('should find nearby drivers for ride request', async () => {
      // Create multiple test drivers at different locations
      const drivers = await createTestDriversAtLocations([
        { lat: -17.8252, lng: 31.0335, distance: 1 }, // 1km away
        { lat: -17.8352, lng: 31.0435, distance: 5 }, // 5km away
        { lat: -17.8452, lng: 31.0535, distance: 10 }, // 10km away
      ]);

      const pickupLocation = 'POINT(31.0335 -17.8252)';
      
      const { data: nearbyDrivers, error } = await testEnv.supabase
        .rpc('find_nearby_drivers', {
          pickup_point: pickupLocation,
          radius_km: 8,
          service_category: 'standard',
        });

      expect(error).toBeNull();
      expect(nearbyDrivers).toHaveLength(2); // Only drivers within 8km
      expect(nearbyDrivers[0].distance_km).toBeLessThan(nearbyDrivers[1].distance_km);
    });
  });

  // Helper functions
  async function createTestPassenger() {
    const userData = TestDataFactory.createTestUser({ role: 'passenger' });
    const { data } = await testEnv.supabase.auth.signInWithOtp({
      phone: userData.phone,
    });
    
    await testEnv.supabase
      .from('users')
      .insert({ ...userData, id: data.user!.id });
    
    return { ...userData, id: data.user!.id };
  }

  async function createTestDriver() {
    const userData = TestDataFactory.createTestUser({ role: 'driver' });
    const vehicleData = TestDataFactory.createTestVehicle();
    const driverData = TestDataFactory.createTestDriver();
    
    const { data } = await testEnv.supabase.auth.signInWithOtp({
      phone: userData.phone,
    });
    
    await testEnv.supabase
      .from('users')
      .insert({ ...userData, id: data.user!.id });
    
    const { data: vehicle } = await testEnv.supabase
      .from('vehicles')
      .insert(vehicleData)
      .select()
      .single();
    
    const { data: driver } = await testEnv.supabase
      .from('drivers')
      .insert({
        ...driverData,
        user_id: data.user!.id,
        vehicle_id: vehicle.id,
      })
      .select()
      .single();
    
    return { ...userData, id: data.user!.id, driver_id: driver.id };
  }

  async function authenticateUser(user: any) {
    await testEnv.supabase.auth.signInWithOtp({
      phone: user.phone,
    });
  }

  async function createTestDriversAtLocations(locations: Array<{ lat: number; lng: number; distance: number }>) {
    const drivers = [];
    
    for (const location of locations) {
      const driver = await createTestDriver();
      
      // Update driver location
      await testEnv.supabase
        .from('drivers')
        .update({
          last_location: `POINT(${location.lng} ${location.lat})`,
          last_location_update: new Date().toISOString(),
        })
        .eq('id', driver.driver_id);
      
      drivers.push(driver);
    }
    
    return drivers;
  }
});
```

## 🔄 **Real-Time Integration Tests**

### **1. Real-Time Subscriptions**
```typescript
// tests/integration/realtime/subscriptions.test.ts
describe('Real-Time Subscriptions Integration', () => {
  let testEnv: TestEnvironment;

  beforeAll(() => {
    testEnv = TestEnvironment.getInstance();
  });

  describe('Ride Status Updates', () => {
    it('should receive real-time ride status updates', async () => {
      const updates: any[] = [];
      let subscription: any;

      // Set up subscription
      const subscriptionPromise = new Promise((resolve) => {
        subscription = testEnv.supabase
          .channel('test-ride-updates')
          .on(
            'postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'rides',
            },
            (payload: any) => {
              updates.push(payload);
              if (updates.length === 2) {
                resolve(updates);
              }
            }
          )
          .subscribe();
      });

      // Create test ride
      const rideData = TestDataFactory.createTestRide();
      const { data: ride } = await testEnv.supabase
        .from('rides')
        .insert(rideData)
        .select()
        .single();

      // Update ride status twice
      await testEnv.supabase
        .from('rides')
        .update({ status: 'accepted' })
        .eq('id', ride.id);

      await testEnv.supabase
        .from('rides')
        .update({ status: 'in_progress' })
        .eq('id', ride.id);

      // Wait for updates
      await subscriptionPromise;

      expect(updates).toHaveLength(2);
      expect(updates[0].new.status).toBe('accepted');
      expect(updates[1].new.status).toBe('in_progress');

      // Cleanup
      await testEnv.supabase.removeChannel(subscription);
    });
  });

  describe('Driver Location Updates', () => {
    it('should receive real-time driver location updates', async () => {
      const locationUpdates: any[] = [];
      let subscription: any;

      // Create test driver
      const driver = await createTestDriver();

      // Set up subscription
      const subscriptionPromise = new Promise((resolve) => {
        subscription = testEnv.supabase
          .channel('test-location-updates')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'driver_locations',
              filter: `driver_id=eq.${driver.driver_id}`,
            },
            (payload: any) => {
              locationUpdates.push(payload);
              if (locationUpdates.length === 3) {
                resolve(locationUpdates);
              }
            }
          )
          .subscribe();
      });

      // Insert location updates
      const locations = [
        'POINT(31.0335 -17.8252)',
        'POINT(31.0340 -17.8255)',
        'POINT(31.0345 -17.8258)',
      ];

      for (const coordinates of locations) {
        await testEnv.supabase
          .from('driver_locations')
          .insert({
            driver_id: driver.driver_id,
            coordinates,
            timestamp: new Date().toISOString(),
          });
      }

      // Wait for updates
      await subscriptionPromise;

      expect(locationUpdates).toHaveLength(3);
      expect(locationUpdates[0].new.coordinates).toBe(locations[0]);
      expect(locationUpdates[2].new.coordinates).toBe(locations[2]);

      // Cleanup
      await testEnv.supabase.removeChannel(subscription);
    });
  });
});
```

This comprehensive system integration testing framework ensures all components of the Taxicab platform work together correctly with proper test data management, real-time feature testing, and complete workflow validation.
