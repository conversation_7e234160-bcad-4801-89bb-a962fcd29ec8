{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:ci": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["^build"], "outputs": ["test-results/**", "playwright-report/**"]}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}}