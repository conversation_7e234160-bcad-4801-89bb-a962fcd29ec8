# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database Configuration (for direct connections if needed)
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
DIRECT_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Payment Gateway APIs
ECOCASH_API_KEY=your-ecocash-api-key
ECOCASH_API_SECRET=your-ecocash-api-secret
ECOCASH_MERCHANT_CODE=your-ecocash-merchant-code

ONEMONEY_API_KEY=your-onemoney-api-key
ONEMONEY_API_SECRET=your-onemoney-api-secret
ONEMONEY_MERCHANT_ID=your-onemoney-merchant-id

STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# SMS Provider (for OTP)
SMS_PROVIDER_API_KEY=your-sms-provider-api-key
SMS_PROVIDER_API_SECRET=your-sms-provider-api-secret

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# Email Configuration (optional)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760  # 10MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Rate Limiting
API_RATE_LIMIT=1000  # requests per hour per IP

# Feature Flags
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_CORPORATE_BILLING=true
NEXT_PUBLIC_ENABLE_DRIVER_RATINGS=true

# Development
NODE_ENV=development
NEXT_PUBLIC_NODE_ENV=development

# Deployment
VERCEL_URL=your-vercel-deployment-url
