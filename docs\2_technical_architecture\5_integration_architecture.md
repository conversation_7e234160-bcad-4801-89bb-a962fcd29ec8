# 5. Integration Architecture

## 🔗 **Integration Overview**

The Taxicab platform integrates with multiple external services to provide comprehensive ride-hailing functionality. The integration architecture follows microservices principles with proper error handling, retry mechanisms, and fallback strategies to ensure system reliability.

### **Integration Principles**
- **Loose Coupling**: Services are independent and can be replaced without affecting others
- **Fault Tolerance**: Graceful degradation when external services are unavailable
- **Retry Logic**: Intelligent retry mechanisms with exponential backoff
- **Circuit Breaker**: Prevent cascade failures from external service outages
- **Monitoring**: Comprehensive monitoring of all external service interactions

## 🗺️ **Google Maps Integration**

### **Maps Services Configuration**
```typescript
// Google Maps API client configuration
interface GoogleMapsConfig {
  apiKey: string;
  libraries: string[];
  region: string;
  language: string;
}

const mapsConfig: GoogleMapsConfig = {
  apiKey: process.env.GOOGLE_MAPS_API_KEY!,
  libraries: ['places', 'geometry', 'directions'],
  region: 'ZW', // Zimbabwe
  language: 'en'
};

class GoogleMapsService {
  private client: GoogleMapsApi;
  
  constructor(config: GoogleMapsConfig) {
    this.client = new GoogleMapsApi({
      key: config.apiKey,
      Promise: Promise
    });
  }
  
  // Geocoding: Convert address to coordinates
  async geocode(address: string): Promise<Coordinates> {
    try {
      const response = await this.client.geocode({
        address: address,
        region: 'ZW',
        components: { country: 'ZW' }
      }).asPromise();
      
      if (response.json.status !== 'OK') {
        throw new Error(`Geocoding failed: ${response.json.status}`);
      }
      
      const location = response.json.results[0].geometry.location;
      return {
        latitude: location.lat,
        longitude: location.lng
      };
    } catch (error) {
      throw new GeocodingError(`Failed to geocode address: ${address}`, error);
    }
  }
  
  // Reverse Geocoding: Convert coordinates to address
  async reverseGeocode(coordinates: Coordinates): Promise<string> {
    try {
      const response = await this.client.reverseGeocode({
        latlng: `${coordinates.latitude},${coordinates.longitude}`,
        result_type: ['street_address', 'route', 'neighborhood']
      }).asPromise();
      
      if (response.json.status !== 'OK') {
        throw new Error(`Reverse geocoding failed: ${response.json.status}`);
      }
      
      return response.json.results[0].formatted_address;
    } catch (error) {
      throw new GeocodingError('Failed to reverse geocode coordinates', error);
    }
  }
  
  // Calculate route and fare estimate
  async calculateRoute(origin: Coordinates, destination: Coordinates): Promise<RouteInfo> {
    try {
      const response = await this.client.directions({
        origin: `${origin.latitude},${origin.longitude}`,
        destination: `${destination.latitude},${destination.longitude}`,
        mode: 'driving',
        traffic_model: 'best_guess',
        departure_time: 'now'
      }).asPromise();
      
      if (response.json.status !== 'OK') {
        throw new Error(`Route calculation failed: ${response.json.status}`);
      }
      
      const route = response.json.routes[0];
      const leg = route.legs[0];
      
      return {
        distance: leg.distance.value, // meters
        duration: leg.duration.value, // seconds
        duration_in_traffic: leg.duration_in_traffic?.value || leg.duration.value,
        polyline: route.overview_polyline.points,
        steps: leg.steps.map(step => ({
          instruction: step.html_instructions,
          distance: step.distance.value,
          duration: step.duration.value
        }))
      };
    } catch (error) {
      throw new RoutingError('Failed to calculate route', error);
    }
  }
}
```

### **Places API Integration**
```typescript
// Places autocomplete for address suggestions
class PlacesService {
  private client: GoogleMapsApi;
  
  async getPlaceSuggestions(input: string, location?: Coordinates): Promise<PlaceSuggestion[]> {
    try {
      const request: any = {
        input: input,
        types: ['establishment', 'geocode'],
        components: { country: 'zw' }
      };
      
      if (location) {
        request.location = `${location.latitude},${location.longitude}`;
        request.radius = 50000; // 50km radius
      }
      
      const response = await this.client.placesAutoComplete(request).asPromise();
      
      return response.json.predictions.map(prediction => ({
        place_id: prediction.place_id,
        description: prediction.description,
        main_text: prediction.structured_formatting.main_text,
        secondary_text: prediction.structured_formatting.secondary_text
      }));
    } catch (error) {
      throw new PlacesError('Failed to get place suggestions', error);
    }
  }
  
  async getPlaceDetails(placeId: string): Promise<PlaceDetails> {
    try {
      const response = await this.client.place({
        placeid: placeId,
        fields: ['name', 'formatted_address', 'geometry', 'place_id']
      }).asPromise();
      
      const place = response.json.result;
      return {
        place_id: place.place_id,
        name: place.name,
        address: place.formatted_address,
        coordinates: {
          latitude: place.geometry.location.lat,
          longitude: place.geometry.location.lng
        }
      };
    } catch (error) {
      throw new PlacesError('Failed to get place details', error);
    }
  }
}
```

## 💳 **Payment Gateway Integration**

### **EcoCash Integration**
```typescript
// EcoCash mobile money integration
class EcoCashService implements PaymentProvider {
  private apiUrl: string;
  private merchantCode: string;
  private apiKey: string;
  
  constructor(config: EcoCashConfig) {
    this.apiUrl = config.apiUrl;
    this.merchantCode = config.merchantCode;
    this.apiKey = config.apiKey;
  }
  
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const payload = {
        merchantCode: this.merchantCode,
        merchantTransactionId: request.transaction_id,
        customerMsisdn: request.phone,
        amount: request.amount,
        currencyCode: 'USD',
        description: `Taxicab ride payment - ${request.ride_id}`
      };
      
      const response = await fetch(`${this.apiUrl}/payments/initiate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new PaymentError(`EcoCash payment failed: ${result.message}`);
      }
      
      return {
        transaction_id: result.transactionId,
        status: this.mapEcoCashStatus(result.status),
        gateway_response: result
      };
    } catch (error) {
      throw new PaymentError('EcoCash payment processing failed', error);
    }
  }
  
  async checkPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/${transactionId}/status`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      
      const result = await response.json();
      
      return {
        transaction_id: transactionId,
        status: this.mapEcoCashStatus(result.status),
        amount: result.amount,
        processed_at: result.processedAt
      };
    } catch (error) {
      throw new PaymentError('Failed to check EcoCash payment status', error);
    }
  }
  
  private mapEcoCashStatus(ecoCashStatus: string): PaymentStatus['status'] {
    switch (ecoCashStatus) {
      case 'PENDING': return 'pending';
      case 'SUCCESS': return 'completed';
      case 'FAILED': return 'failed';
      case 'CANCELLED': return 'cancelled';
      default: return 'pending';
    }
  }
}
```

### **OneMoney Integration**
```typescript
// OneMoney mobile money integration
class OneMoneyService implements PaymentProvider {
  private apiUrl: string;
  private clientId: string;
  private clientSecret: string;
  private accessToken?: string;
  
  constructor(config: OneMoneyConfig) {
    this.apiUrl = config.apiUrl;
    this.clientId = config.clientId;
    this.clientSecret = config.clientSecret;
  }
  
  private async getAccessToken(): Promise<string> {
    if (this.accessToken) return this.accessToken;
    
    try {
      const response = await fetch(`${this.apiUrl}/oauth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret
        })
      });
      
      const result = await response.json();
      this.accessToken = result.access_token;
      
      // Schedule token refresh before expiry
      setTimeout(() => {
        this.accessToken = undefined;
      }, (result.expires_in - 300) * 1000); // Refresh 5 minutes before expiry
      
      return this.accessToken;
    } catch (error) {
      throw new PaymentError('Failed to get OneMoney access token', error);
    }
  }
  
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const token = await this.getAccessToken();
      
      const payload = {
        amount: request.amount,
        currency: 'USD',
        externalId: request.transaction_id,
        payer: {
          partyIdType: 'MSISDN',
          partyId: request.phone
        },
        payerMessage: `Taxicab ride payment`,
        payeeNote: `Payment for ride ${request.ride_id}`
      };
      
      const response = await fetch(`${this.apiUrl}/collection/v1_0/requesttopay`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Reference-Id': request.transaction_id,
          'X-Target-Environment': 'sandbox' // Change to 'live' for production
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new PaymentError(`OneMoney payment failed: ${error.message}`);
      }
      
      return {
        transaction_id: request.transaction_id,
        status: 'pending',
        gateway_response: { reference_id: request.transaction_id }
      };
    } catch (error) {
      throw new PaymentError('OneMoney payment processing failed', error);
    }
  }
}
```

## 📱 **Communication Services Integration**

### **Twilio SMS Integration**
```typescript
// Twilio SMS service for notifications and OTP
class TwilioSMSService {
  private client: Twilio;
  private fromNumber: string;
  
  constructor(config: TwilioConfig) {
    this.client = new Twilio(config.accountSid, config.authToken);
    this.fromNumber = config.fromNumber;
  }
  
  async sendOTP(phone: string, code: string): Promise<string> {
    try {
      const message = await this.client.messages.create({
        body: `Your Taxicab verification code is: ${code}. Valid for 5 minutes.`,
        from: this.fromNumber,
        to: phone
      });
      
      return message.sid;
    } catch (error) {
      throw new SMSError('Failed to send OTP SMS', error);
    }
  }
  
  async sendRideNotification(phone: string, message: string): Promise<string> {
    try {
      const sms = await this.client.messages.create({
        body: message,
        from: this.fromNumber,
        to: phone
      });
      
      return sms.sid;
    } catch (error) {
      throw new SMSError('Failed to send ride notification', error);
    }
  }
  
  async sendDriverProfile(phone: string, driverName: string, profileUrl: string): Promise<string> {
    try {
      const message = `Your Taxicab driver, ${driverName}, has shared their profile with you. View it here: ${profileUrl}`;
      
      const sms = await this.client.messages.create({
        body: message,
        from: this.fromNumber,
        to: phone
      });
      
      return sms.sid;
    } catch (error) {
      throw new SMSError('Failed to send driver profile', error);
    }
  }
}
```

### **WhatsApp Business API Integration**
```typescript
// WhatsApp Business API for customer engagement
class WhatsAppService {
  private apiUrl: string;
  private accessToken: string;
  private phoneNumberId: string;
  
  constructor(config: WhatsAppConfig) {
    this.apiUrl = config.apiUrl;
    this.accessToken = config.accessToken;
    this.phoneNumberId = config.phoneNumberId;
  }
  
  async sendTemplateMessage(to: string, templateName: string, parameters: string[]): Promise<string> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'template',
        template: {
          name: templateName,
          language: { code: 'en' },
          components: [
            {
              type: 'body',
              parameters: parameters.map(param => ({ type: 'text', text: param }))
            }
          ]
        }
      };
      
      const response = await fetch(`${this.apiUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new WhatsAppError(`WhatsApp message failed: ${result.error.message}`);
      }
      
      return result.messages[0].id;
    } catch (error) {
      throw new WhatsAppError('Failed to send WhatsApp template message', error);
    }
  }
  
  async sendTextMessage(to: string, text: string): Promise<string> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: { body: text }
      };
      
      const response = await fetch(`${this.apiUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      const result = await response.json();
      return result.messages[0].id;
    } catch (error) {
      throw new WhatsAppError('Failed to send WhatsApp text message', error);
    }
  }
  
  async handleIncomingMessage(webhook: WhatsAppWebhook): Promise<void> {
    for (const entry of webhook.entry) {
      for (const change of entry.changes) {
        if (change.field === 'messages') {
          for (const message of change.value.messages || []) {
            await this.processIncomingMessage(message, change.value.contacts[0]);
          }
        }
      }
    }
  }
  
  private async processIncomingMessage(message: any, contact: any): Promise<void> {
    const userPhone = contact.wa_id;
    const messageText = message.text?.body;
    
    if (!messageText) return;
    
    // Process ride booking commands
    if (messageText.toLowerCase().includes('book') || messageText === '1') {
      await this.initiateRideBooking(userPhone);
    } else if (messageText.toLowerCase().includes('track') || messageText === '2') {
      await this.sendRideTracking(userPhone);
    } else {
      await this.sendMainMenu(userPhone);
    }
  }
  
  private async initiateRideBooking(phone: string): Promise<void> {
    await this.sendTextMessage(phone, 
      "🚗 Let's book your ride! Please share your pickup location or type the address."
    );
  }
}
```

## 🔄 **Integration Resilience and Error Handling**

### **Circuit Breaker Pattern**
```typescript
// Circuit breaker for external service calls
class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime?: number;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000, // 1 minute
    private retryTimeout: number = 30000 // 30 seconds
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime! < this.retryTimeout) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    
    try {
      const result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), this.timeout)
        )
      ]);
      
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}

// Usage with external services
const mapsCircuitBreaker = new CircuitBreaker(5, 10000, 30000);
const paymentCircuitBreaker = new CircuitBreaker(3, 15000, 60000);

// Protected service calls
const protectedGeocode = (address: string) => 
  mapsCircuitBreaker.execute(() => mapsService.geocode(address));

const protectedPayment = (request: PaymentRequest) => 
  paymentCircuitBreaker.execute(() => paymentService.processPayment(request));
```

This comprehensive integration architecture ensures reliable communication with all external services while maintaining system stability and providing fallback mechanisms for service disruptions.
