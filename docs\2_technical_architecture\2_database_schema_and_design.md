# 2. Database Schema and Design

## 🗄️ **Database Overview**

The Taxicab platform uses PostgreSQL via Supabase as the primary database, leveraging Row Level Security (RLS) for data protection and real-time subscriptions for live updates. The schema is designed for scalability, data integrity, and optimal query performance.

### **Database Design Principles**
- **Normalization**: Properly normalized schema to reduce data redundancy
- **Performance**: Optimized indexes and query patterns for fast response times
- **Security**: Row Level Security (RLS) policies for data access control
- **Scalability**: Designed to handle millions of rides and users
- **Audit Trail**: Complete audit logging for compliance and debugging

## 📊 **Core Entity Relationship Diagram**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Users    │    │   Drivers   │    │ Associations│
│             │    │             │    │             │
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ phone       │◄──►│ user_id(FK) │    │ name        │
│ email       │    │ assoc_id(FK)│◄──►│ commission  │
│ role        │    │ vehicle_id  │    │ contact     │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │
       │                   │
       ▼                   ▼
┌─────────────┐    ┌─────────────┐
│    Rides    │    │  Vehicles   │
│             │    │             │
│ id (PK)     │    │ id (PK)     │
│ passenger_id│    │ make        │
│ driver_id   │    │ model       │
│ status      │    │ plate       │
│ fare        │    │ category    │
└─────────────┘    └─────────────┘
```

## 🏗️ **Core Tables**

### **Users Table**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone VARCHAR(20) UNIQUE NOT NULL,
  email VARCHAR(255),
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'passenger',
  profile_image_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User roles enum
CREATE TYPE user_role AS ENUM ('passenger', 'driver', 'admin', 'corporate_admin');
```

### **Taxi Associations Table**
```sql
CREATE TABLE taxi_associations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  registration_number VARCHAR(100) UNIQUE,
  contact_person VARCHAR(255),
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),
  address TEXT,
  commission_rate DECIMAL(5,2) DEFAULT 5.00, -- Percentage
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Drivers Table**
```sql
CREATE TABLE drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  association_id UUID REFERENCES taxi_associations(id),
  license_number VARCHAR(50) UNIQUE NOT NULL,
  license_expiry DATE NOT NULL,
  vehicle_id UUID REFERENCES vehicles(id),
  status driver_status DEFAULT 'pending',
  rating DECIMAL(3,2) DEFAULT 0.00,
  total_rides INTEGER DEFAULT 0,
  total_earnings DECIMAL(10,2) DEFAULT 0.00,
  is_online BOOLEAN DEFAULT FALSE,
  last_location POINT,
  last_location_update TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Driver status enum
CREATE TYPE driver_status AS ENUM ('pending', 'approved', 'suspended', 'rejected');
```

### **Vehicles Table**
```sql
CREATE TABLE vehicles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  make VARCHAR(100) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL,
  color VARCHAR(50) NOT NULL,
  license_plate VARCHAR(20) UNIQUE NOT NULL,
  category vehicle_category NOT NULL,
  capacity INTEGER DEFAULT 4,
  registration_expiry DATE NOT NULL,
  insurance_expiry DATE NOT NULL,
  inspection_expiry DATE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vehicle category enum
CREATE TYPE vehicle_category AS ENUM ('lite', 'standard', 'executive', 'xl');
```

### **Rides Table**
```sql
CREATE TABLE rides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  passenger_id UUID REFERENCES users(id) NOT NULL,
  driver_id UUID REFERENCES drivers(id),
  service_type service_type NOT NULL DEFAULT 'movers_ride',
  status ride_status DEFAULT 'requested',
  
  -- Location data
  pickup_address TEXT NOT NULL,
  pickup_coordinates POINT NOT NULL,
  destination_address TEXT NOT NULL,
  destination_coordinates POINT NOT NULL,
  
  -- Pricing
  estimated_fare DECIMAL(8,2),
  final_fare DECIMAL(8,2),
  distance_km DECIMAL(8,2),
  duration_minutes INTEGER,
  
  -- Timestamps
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  cancelled_at TIMESTAMP WITH TIME ZONE,
  
  -- Additional data
  cancellation_reason TEXT,
  passenger_rating INTEGER CHECK (passenger_rating >= 1 AND passenger_rating <= 5),
  driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
  passenger_comment TEXT,
  driver_comment TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Service type enum
CREATE TYPE service_type AS ENUM (
  'movers_ride', 'movers_exec', 'movers_xl', 'movers_cruiser', 'movers_express'
);

-- Ride status enum
CREATE TYPE ride_status AS ENUM (
  'requested', 'accepted', 'driver_arrived', 'in_progress', 'completed', 'cancelled'
);
```

## 💳 **Payment and Financial Tables**

### **Payments Table**
```sql
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID REFERENCES rides(id) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_method payment_method NOT NULL,
  payment_status payment_status DEFAULT 'pending',
  transaction_id VARCHAR(255),
  gateway_response JSONB,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment method enum
CREATE TYPE payment_method AS ENUM ('cash', 'ecocash', 'onemoney', 'card');

-- Payment status enum
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
```

### **Driver Earnings Table**
```sql
CREATE TABLE driver_earnings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  ride_id UUID REFERENCES rides(id) NOT NULL,
  gross_amount DECIMAL(10,2) NOT NULL,
  platform_commission DECIMAL(10,2) NOT NULL,
  association_commission DECIMAL(10,2) DEFAULT 0.00,
  net_earnings DECIMAL(10,2) NOT NULL,
  commission_rate DECIMAL(5,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🏢 **Corporate and Business Tables**

### **Corporate Accounts Table**
```sql
CREATE TABLE corporate_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_name VARCHAR(255) NOT NULL,
  registration_number VARCHAR(100) UNIQUE,
  admin_user_id UUID REFERENCES users(id) NOT NULL,
  billing_address TEXT,
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),
  monthly_limit DECIMAL(10,2),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Corporate Employees Table**
```sql
CREATE TABLE corporate_employees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  corporate_account_id UUID REFERENCES corporate_accounts(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  employee_id VARCHAR(100),
  department VARCHAR(100),
  spending_limit DECIMAL(8,2),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(corporate_account_id, user_id)
);
```

## 📱 **Real-Time and Communication Tables**

### **Driver Locations Table**
```sql
CREATE TABLE driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  coordinates POINT NOT NULL,
  heading DECIMAL(5,2), -- Direction in degrees
  speed DECIMAL(5,2), -- Speed in km/h
  accuracy DECIMAL(8,2), -- GPS accuracy in meters
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index for efficient location queries
  INDEX idx_driver_locations_driver_time (driver_id, timestamp),
  INDEX idx_driver_locations_spatial (coordinates) USING GIST
);
```

### **Notifications Table**
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  type notification_type NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  is_read BOOLEAN DEFAULT FALSE,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- Notification type enum
CREATE TYPE notification_type AS ENUM (
  'ride_request', 'ride_accepted', 'driver_arrived', 'trip_started', 
  'trip_completed', 'payment_processed', 'rating_received'
);
```

## 🔐 **Row Level Security (RLS) Policies**

### **Users Table RLS**
```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can view and update their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Admins can view all users
CREATE POLICY "Admins can view all users" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

### **Rides Table RLS**
```sql
-- Enable RLS
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;

-- Passengers can view their own rides
CREATE POLICY "Passengers can view own rides" ON rides
  FOR SELECT USING (passenger_id = auth.uid());

-- Drivers can view assigned rides
CREATE POLICY "Drivers can view assigned rides" ON rides
  FOR SELECT USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Drivers can update assigned rides
CREATE POLICY "Drivers can update assigned rides" ON rides
  FOR UPDATE USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );
```

### **Driver Locations RLS**
```sql
-- Enable RLS
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;

-- Drivers can insert their own location
CREATE POLICY "Drivers can insert own location" ON driver_locations
  FOR INSERT WITH CHECK (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Passengers can view driver location during active ride
CREATE POLICY "Passengers can view driver location during ride" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM rides 
      WHERE passenger_id = auth.uid() 
        AND driver_id = driver_locations.driver_id
        AND status IN ('accepted', 'driver_arrived', 'in_progress')
    )
  );
```

## 📈 **Performance Optimization**

### **Database Indexes**
```sql
-- Spatial indexes for location-based queries
CREATE INDEX idx_drivers_location ON drivers USING GIST (last_location);
CREATE INDEX idx_rides_pickup ON rides USING GIST (pickup_coordinates);
CREATE INDEX idx_rides_destination ON rides USING GIST (destination_coordinates);

-- Performance indexes for common queries
CREATE INDEX idx_rides_passenger_status ON rides (passenger_id, status);
CREATE INDEX idx_rides_driver_status ON rides (driver_id, status);
CREATE INDEX idx_rides_created_at ON rides (created_at);
CREATE INDEX idx_drivers_online_location ON drivers (is_online, last_location) WHERE is_online = true;

-- Composite indexes for complex queries
CREATE INDEX idx_rides_status_created ON rides (status, created_at);
CREATE INDEX idx_payments_ride_status ON payments (ride_id, payment_status);
```

### **Database Functions**
```sql
-- Function to find nearby drivers
CREATE OR REPLACE FUNCTION find_nearby_drivers(
  pickup_point POINT,
  radius_km DECIMAL DEFAULT 5.0,
  service_category vehicle_category DEFAULT 'standard'
)
RETURNS TABLE (
  driver_id UUID,
  distance_km DECIMAL,
  driver_rating DECIMAL,
  vehicle_info JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    (ST_Distance(d.last_location, pickup_point) / 1000)::DECIMAL as distance,
    d.rating,
    jsonb_build_object(
      'make', v.make,
      'model', v.model,
      'color', v.color,
      'plate', v.license_plate
    ) as vehicle_info
  FROM drivers d
  JOIN vehicles v ON d.vehicle_id = v.id
  WHERE d.is_online = true
    AND d.status = 'approved'
    AND v.category = service_category
    AND ST_DWithin(d.last_location, pickup_point, radius_km * 1000)
  ORDER BY distance, d.rating DESC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;
```

## 🔄 **Database Triggers and Automation**

### **Automatic Timestamp Updates**
```sql
-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables with updated_at column
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rides_updated_at 
  BEFORE UPDATE ON rides 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **Automatic Earnings Calculation**
```sql
-- Function to calculate driver earnings
CREATE OR REPLACE FUNCTION calculate_driver_earnings()
RETURNS TRIGGER AS $$
DECLARE
  platform_rate DECIMAL := 0.20; -- 20% platform commission
  association_rate DECIMAL := 0.05; -- 5% association commission
  gross_amount DECIMAL;
  platform_commission DECIMAL;
  association_commission DECIMAL;
  net_earnings DECIMAL;
BEGIN
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    gross_amount := NEW.final_fare;
    platform_commission := gross_amount * platform_rate;
    association_commission := gross_amount * association_rate;
    net_earnings := gross_amount - platform_commission - association_commission;
    
    INSERT INTO driver_earnings (
      driver_id, ride_id, gross_amount, platform_commission,
      association_commission, net_earnings, commission_rate
    ) VALUES (
      NEW.driver_id, NEW.id, gross_amount, platform_commission,
      association_commission, net_earnings, platform_rate
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_earnings_on_completion
  AFTER UPDATE ON rides
  FOR EACH ROW EXECUTE FUNCTION calculate_driver_earnings();
```

This comprehensive database schema provides a solid foundation for the Taxicab platform, ensuring data integrity, security, and optimal performance for all ride-hailing operations.
