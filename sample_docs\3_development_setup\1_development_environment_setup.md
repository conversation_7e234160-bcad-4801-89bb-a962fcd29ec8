# 1. Development Environment Setup

## 🛠️ **Development Environment Overview**

This document provides comprehensive setup instructions for the UniversalWallet development environment, ensuring consistent development across all team members with proper tooling, dependencies, and configurations.

## 💻 **System Requirements**

### **Hardware Requirements**
- **CPU**: Intel i5/AMD Ryzen 5 or better (8+ cores recommended)
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 500GB SSD minimum, 1TB recommended
- **Network**: Stable internet connection (minimum 10 Mbps)

### **Operating System Support**
- **Primary**: macOS 12+ or Ubuntu 20.04+ LTS
- **Secondary**: Windows 11 with WSL2
- **Docker**: Required for all platforms

---

## 🔧 **Core Development Tools**

### **Required Software Installation**

#### **1. Version Control**
```bash
# Git installation and configuration
git --version  # Should be 2.30+

# Global Git configuration
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
git config --global pull.rebase true
```

#### **2. Node.js and Package Managers**
```bash
# Install Node.js 18+ LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should be 18.x+
npm --version   # Should be 9.x+

# Install Yarn (recommended)
npm install -g yarn
yarn --version

# Install pnpm (alternative)
npm install -g pnpm
```

#### **3. Java Development Kit**
```bash
# Install OpenJDK 17 (LTS)
sudo apt update
sudo apt install openjdk-17-jdk

# Verify installation
java --version
javac --version

# Set JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64' >> ~/.bashrc
source ~/.bashrc
```

#### **4. Docker and Docker Compose**
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

---

## 🏗️ **Backend Development Setup**

### **Java Spring Boot Environment**

#### **1. Maven Installation**
```bash
# Install Maven 3.8+
sudo apt install maven

# Verify installation
mvn --version

# Configure Maven settings
mkdir -p ~/.m2
```

#### **2. IDE Setup - IntelliJ IDEA**
```bash
# Download and install IntelliJ IDEA Ultimate
# Configure plugins:
# - Spring Boot
# - Lombok
# - Docker
# - Database Navigator
# - SonarLint
# - GitToolBox
```

#### **3. Database Setup**
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: universalwallet_dev
      POSTGRES_USER: uwallet_dev
      POSTGRES_PASSWORD: dev_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
```

#### **4. Backend Project Structure**
```
universalwallet-backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/universalwallet/
│   │   │       ├── UniversalWalletApplication.java
│   │   │       ├── config/
│   │   │       ├── controller/
│   │   │       ├── service/
│   │   │       ├── repository/
│   │   │       ├── entity/
│   │   │       ├── dto/
│   │   │       ├── security/
│   │   │       └── integration/
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       └── db/migration/
│   └── test/
├── pom.xml
├── Dockerfile
└── docker-compose.yml
```

---

## 📱 **Frontend Development Setup**

### **React Native Environment**

#### **1. React Native CLI Setup**
```bash
# Install React Native CLI
npm install -g @react-native-community/cli

# Install Watchman (macOS)
brew install watchman

# Install CocoaPods (iOS development)
sudo gem install cocoapods
```

#### **2. Android Development Setup**
```bash
# Install Android Studio
# Download from: https://developer.android.com/studio

# Set environment variables
echo 'export ANDROID_HOME=$HOME/Android/Sdk' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.bashrc
source ~/.bashrc

# Create Android Virtual Device (AVD)
# Use Android Studio AVD Manager
```

#### **3. iOS Development Setup (macOS only)**
```bash
# Install Xcode from App Store
# Install Xcode Command Line Tools
xcode-select --install

# Install iOS Simulator
# Available through Xcode
```

#### **4. Frontend Project Structure**
```
universalwallet-mobile/
├── src/
│   ├── components/
│   ├── screens/
│   ├── navigation/
│   ├── services/
│   ├── store/
│   ├── utils/
│   ├── types/
│   └── assets/
├── android/
├── ios/
├── package.json
├── metro.config.js
├── babel.config.js
└── tsconfig.json
```

### **Web Portal Setup**

#### **1. React.js Project Setup**
```bash
# Create React TypeScript project
npx create-react-app universalwallet-web --template typescript

# Install additional dependencies
cd universalwallet-web
npm install @mui/material @emotion/react @emotion/styled
npm install @reduxjs/toolkit react-redux
npm install axios react-router-dom
npm install @types/node @types/react @types/react-dom
```

#### **2. Web Project Structure**
```
universalwallet-web/
├── public/
├── src/
│   ├── components/
│   ├── pages/
│   ├── hooks/
│   ├── services/
│   ├── store/
│   ├── utils/
│   ├── types/
│   └── assets/
├── package.json
├── tsconfig.json
└── webpack.config.js
```

---

## 🔧 **Development Tools Configuration**

### **Code Editor Setup - VS Code**

#### **Required Extensions**
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.java",
    "vscjava.vscode-spring-boot-dashboard",
    "ms-vscode.vscode-docker",
    "ms-vscode-remote.remote-containers",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "sonarsource.sonarlint-vscode",
    "ms-vscode.vscode-git-graph"
  ]
}
```

#### **VS Code Settings**
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.compile.nullAnalysis.mode": "automatic"
}
```

### **Linting and Formatting**

#### **ESLint Configuration (.eslintrc.js)**
```javascript
module.exports = {
  root: true,
  extends: [
    '@react-native-community',
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'prettier'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react-hooks'],
  rules: {
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn'
  }
};
```

#### **Prettier Configuration (.prettierrc)**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

---

## 🐳 **Docker Development Environment**

### **Complete Docker Compose Setup**

#### **docker-compose.dev.yml**
```yaml
version: '3.8'
services:
  # Backend Services
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - DATABASE_URL=***************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/target

  # Frontend Services
  web:
    build:
      context: ./frontend-web
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080/api/v1
    volumes:
      - ./frontend-web:/app
      - /app/node_modules

  # Database Services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: universalwallet_dev
      POSTGRES_USER: uwallet_dev
      POSTGRES_PASSWORD: dev_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Development Tools
  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"
      - "8025:8025"

  adminer:
    image: adminer
    ports:
      - "8081:8080"
    depends_on:
      - postgres

volumes:
  postgres_data:
  redis_data:
```

### **Development Scripts**

#### **package.json Scripts**
```json
{
  "scripts": {
    "dev:setup": "docker-compose -f docker-compose.dev.yml up -d postgres redis",
    "dev:start": "docker-compose -f docker-compose.dev.yml up",
    "dev:stop": "docker-compose -f docker-compose.dev.yml down",
    "dev:clean": "docker-compose -f docker-compose.dev.yml down -v",
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "jest --testPathPattern=unit",
    "test:integration": "jest --testPathPattern=integration",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "format": "prettier --write src/**/*.{ts,tsx,js,jsx}"
  }
}
```

---

## 🔐 **Environment Variables and Secrets**

### **Environment Configuration**

#### **.env.development**
```bash
# Database Configuration
DATABASE_URL=****************************************************
DATABASE_USERNAME=uwallet_dev
DATABASE_PASSWORD=dev_password_123

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=dev_jwt_secret_key_change_in_production
JWT_EXPIRATION=3600

# External API Configuration (Development)
ECOCASH_API_URL=https://sandbox.ecocash.co.zw/api/v2
ECOCASH_API_KEY=dev_api_key
ONEMONEY_API_URL=https://sandbox.onemoney.co.zw/api/v1
ONEMONEY_API_KEY=dev_api_key

# Email Configuration (Development)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=

# File Storage (Development)
AWS_ACCESS_KEY_ID=dev_access_key
AWS_SECRET_ACCESS_KEY=dev_secret_key
AWS_S3_BUCKET=universalwallet-dev-files
AWS_REGION=us-east-1

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FILE=logs/universalwallet-dev.log
```

### **Secrets Management**

#### **Development Secrets Setup**
```bash
# Create secrets directory
mkdir -p .secrets

# Generate development certificates
openssl req -x509 -newkey rsa:4096 -keyout .secrets/dev-private-key.pem -out .secrets/dev-certificate.pem -days 365 -nodes

# Create environment-specific secret files
echo "dev_jwt_secret_key_change_in_production" > .secrets/jwt-secret
echo "dev_database_password_123" > .secrets/db-password
```

---

## 🧪 **Testing Environment Setup**

### **Testing Tools Installation**
```bash
# Backend Testing (Java)
# JUnit 5, Mockito, TestContainers already in pom.xml

# Frontend Testing (JavaScript/TypeScript)
npm install --save-dev jest @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/react-native
npm install --save-dev supertest nock

# E2E Testing
npm install --save-dev cypress
npm install --save-dev detox
```

### **Test Database Setup**
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_DB: universalwallet_test
      POSTGRES_USER: uwallet_test
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    tmpfs:
      - /var/lib/postgresql/data
```

**This comprehensive development environment setup ensures consistent, efficient, and secure development across the entire UniversalWallet platform.** 🛠️
