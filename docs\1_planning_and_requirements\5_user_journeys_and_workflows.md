# 5. User Journeys and Workflows

## 🚗 **Passenger User Journeys**

### **Journey 1: New Passenger Registration and First Ride**

#### **Step 1: Account Creation**
1. **App Download**: User downloads Taxicab app from App Store/Google Play
2. **Welcome Screen**: User sees welcome screen with "Sign Up" and "Login" options
3. **Registration Form**: User enters phone number, full name, and email
4. **Phone Verification**: User receives SMS OTP and enters verification code
5. **Profile Setup**: User adds profile photo and sets payment preferences
6. **Location Permission**: User grants location access for pickup detection

#### **Step 2: First Ride Booking**
1. **Home Screen**: App opens to map view with current location as pickup point
2. **Destination Entry**: User types "Eastgate Mall" in "Where to?" search bar
3. **Route Preview**: Map shows route with estimated time and distance
4. **Service Selection**: User sees service options (MoversRide, MoversExec) with fare estimates
5. **Booking Confirmation**: User selects MoversRide and taps "Confirm Booking"
6. **Driver Matching**: "Finding your driver..." screen with loading animation

#### **Step 3: Trip Experience**
1. **Driver Found**: Screen shows driver details (name, photo, vehicle, rating, ETA)
2. **Live Tracking**: User sees driver approaching on map with real-time updates
3. **Driver Arrival**: Push notification "Your driver has arrived" with driver contact
4. **Trip Start**: Driver confirms passenger pickup and starts trip
5. **Live Navigation**: User sees real-time trip progress on map
6. **Trip Completion**: Driver ends trip, fare is calculated and displayed

#### **Step 4: Payment and Rating**
1. **Payment Screen**: User sees trip summary with fare breakdown
2. **Payment Method**: User selects payment method (EcoCash, cash, card)
3. **Payment Processing**: Payment is processed with confirmation message
4. **Driver Rating**: User rates driver (1-5 stars) and leaves optional comment
5. **Trip Receipt**: Digital receipt is generated and sent via email/SMS

### **Journey 2: Booking with Favorite Driver**

#### **Scenario**: Returning passenger wants to book with previously saved driver

1. **App Launch**: User opens app and sees home screen
2. **Destination Entry**: User enters destination "University of Zimbabwe"
3. **Service Selection**: Instead of standard options, user sees "Favorite Drivers" section
4. **Driver Selection**: User taps on saved driver "John Doe - Toyota Corolla"
5. **Priority Booking**: Request is sent exclusively to John Doe for 30 seconds
6. **Acceptance**: John accepts the ride, and normal trip flow continues
7. **Fallback**: If John doesn't accept, request goes to general driver pool

### **Journey 3: Corporate Employee Booking**

#### **Scenario**: Employee books ride using company account

1. **Corporate Login**: Employee logs in using company-provided credentials
2. **Trip Purpose**: User selects trip purpose (Meeting, Airport, Client Visit)
3. **Approval Check**: System checks if trip requires manager approval
4. **Booking Process**: Standard booking flow with corporate payment method
5. **Expense Tracking**: Trip is automatically categorized for expense reporting
6. **Receipt Generation**: Corporate receipt is sent to employee and finance team

## 🚕 **Driver User Journeys**

### **Journey 1: New Driver Onboarding**

#### **Step 1: Initial Registration**
1. **Landing Page**: Driver visits Taxicab driver portal or downloads driver app
2. **Registration Type**: Driver selects "Individual Driver" or "Fleet Driver"
3. **Personal Information**: Driver enters name, phone, email, address, ID number
4. **Taxi Association**: Driver selects their taxi association from dropdown list
5. **Account Creation**: Driver creates password and receives verification SMS

#### **Step 2: Vehicle and Document Upload**
1. **Vehicle Information**: Driver enters make, model, year, license plate, color
2. **Document Upload**: Driver uploads photos of:
   - Driver's license (front and back)
   - Vehicle registration book
   - Insurance certificate
   - Vehicle inspection certificate
   - Profile photo
3. **Association Verification**: System verifies association membership
4. **Submission**: Driver submits application for review

#### **Step 3: Verification and Approval**
1. **Admin Review**: Admin reviews documents and vehicle information
2. **Background Check**: System runs criminal background check
3. **Vehicle Inspection**: Driver schedules and completes vehicle inspection
4. **Training Module**: Driver completes online safety and platform training
5. **Approval Notification**: Driver receives approval SMS and email
6. **App Access**: Driver can now log into driver app and go online

### **Journey 2: Daily Driver Operations**

#### **Step 1: Going Online**
1. **App Launch**: Driver opens app and sees "Offline" status
2. **Vehicle Check**: Driver confirms vehicle is ready (fuel, cleanliness, safety)
3. **Go Online**: Driver taps toggle to go "Online" - button turns teal
4. **Location Sharing**: App begins sharing driver's real-time location
5. **Waiting for Rides**: Driver sees map with their location and nearby demand

#### **Step 2: Receiving and Accepting Rides**
1. **Ride Request**: Full-screen overlay appears with ride details:
   - Pickup location and distance
   - Destination and estimated fare
   - Passenger rating
   - 20-second countdown timer
2. **Decision Making**: Driver reviews details and decides to accept/decline
3. **Acceptance**: Driver taps "Accept" - overlay disappears
4. **Navigation**: Map shows route to pickup location with turn-by-turn directions
5. **Passenger Contact**: Driver can call passenger if needed

#### **Step 3: Trip Execution**
1. **Arrival**: Driver taps "Arrived" when reaching pickup location
2. **Passenger Pickup**: Driver confirms passenger identity and starts trip
3. **Navigation**: App provides turn-by-turn navigation to destination
4. **Trip Completion**: Driver taps "End Trip" when reaching destination
5. **Fare Collection**: Driver collects payment (cash/digital) and confirms in app
6. **Rating**: Driver rates passenger experience (1-5 stars)

### **Journey 3: Driver Profile Sharing**

#### **Scenario**: Driver wants to share profile with potential customer

1. **Profile Tab**: Driver taps "Share Profile" in bottom navigation
2. **Customer Contact**: Driver enters customer's phone number
3. **Profile Generation**: System generates mobile-optimized profile link
4. **SMS Sending**: Automated SMS sent to customer with profile link and app download link
5. **Customer View**: Customer receives SMS and can view driver profile
6. **Future Booking**: Customer can save driver as favorite for future rides

## 🏢 **Corporate Client Journeys**

### **Journey 1: Corporate Account Setup**

#### **Step 1: Business Registration**
1. **Corporate Portal**: Business admin visits corporate registration page
2. **Company Information**: Admin enters company name, registration number, address
3. **Admin Details**: Admin provides their name, email, phone, job title
4. **Verification Documents**: Admin uploads business registration and tax documents
5. **Submission**: Application is submitted for manual verification

#### **Step 2: Account Configuration**
1. **Verification Call**: Taxicab representative calls to verify business details
2. **Account Approval**: Admin receives approval email with login credentials
3. **Portal Access**: Admin logs into corporate portal for first time
4. **Company Setup**: Admin configures:
   - Travel policies and spending limits
   - Approval workflows
   - Cost centers and departments
   - Payment methods and billing preferences

#### **Step 3: Employee Onboarding**
1. **Employee Addition**: Admin adds employees with email and spending limits
2. **Invitation Emails**: Employees receive invitation emails with setup instructions
3. **Employee Registration**: Employees create accounts linked to corporate account
4. **Policy Acknowledgment**: Employees review and accept travel policies

### **Journey 2: Bulk Ride Management**

#### **Scenario**: Corporate admin books multiple rides for company event

1. **Bulk Booking**: Admin accesses "Bulk Booking" feature in corporate portal
2. **Event Details**: Admin enters event name, date, time, and location
3. **Employee Selection**: Admin selects employees who need transportation
4. **Pickup Locations**: Admin assigns pickup locations for each employee
5. **Service Type**: Admin selects appropriate service level (MoversRide/MoversExec)
6. **Scheduling**: Admin schedules rides with appropriate time buffers
7. **Confirmation**: System confirms all bookings and sends notifications to employees
8. **Tracking**: Admin can track all rides in real-time from dashboard

## 👨‍💼 **Admin User Journeys**

### **Journey 1: Driver Verification Process**

#### **Step 1: Application Review**
1. **Dashboard Alert**: Admin sees notification for new driver application
2. **Application Details**: Admin reviews driver's personal and vehicle information
3. **Document Verification**: Admin examines uploaded documents for authenticity
4. **Background Check**: Admin initiates and reviews background check results
5. **Association Verification**: Admin confirms driver's association membership

#### **Step 2: Decision Making**
1. **Approval Decision**: Admin decides to approve, reject, or request more information
2. **Status Update**: Admin updates application status in system
3. **Communication**: System sends appropriate notification to driver
4. **Account Activation**: If approved, driver account is activated for ride acceptance

### **Journey 2: Platform Monitoring and Management**

#### **Step 1: Daily Operations Monitoring**
1. **Dashboard Overview**: Admin views real-time platform metrics
2. **Active Monitoring**: Admin monitors active rides, driver locations, and system health
3. **Issue Detection**: Admin identifies potential issues (driver complaints, system errors)
4. **Response Coordination**: Admin coordinates response to issues and customer support

#### **Step 2: Performance Analysis**
1. **Analytics Review**: Admin reviews daily/weekly performance reports
2. **KPI Tracking**: Admin monitors key performance indicators and trends
3. **Decision Making**: Admin makes operational decisions based on data insights
4. **Strategy Adjustment**: Admin adjusts pricing, promotions, or policies as needed

## 📱 **WhatsApp Integration Journeys**

### **Journey 1: WhatsApp Ride Booking**

#### **Step 1: Initial Contact**
1. **WhatsApp Message**: User sends "Hi" to Taxicab WhatsApp number
2. **Bot Response**: Automated welcome message with menu options:
   - 1️⃣ Book a Ride
   - 2️⃣ Track My Ride
   - 3️⃣ Get Fare Estimate
   - 4️⃣ Customer Support

#### **Step 2: Booking Process**
1. **Service Selection**: User replies "1" for ride booking
2. **Location Request**: Bot asks for pickup location
3. **Location Sharing**: User shares location via WhatsApp location feature
4. **Destination Request**: Bot asks "Where would you like to go?"
5. **Destination Entry**: User types "Eastgate Mall"
6. **Fare Estimate**: Bot provides fare estimate and asks for confirmation
7. **Confirmation**: User confirms booking with quick reply button

#### **Step 3: Trip Management**
1. **Driver Assignment**: Bot sends driver details and tracking link
2. **Status Updates**: Bot provides real-time updates on driver location and ETA
3. **Trip Completion**: Bot confirms trip completion and sends payment link
4. **Receipt**: Bot sends digital receipt via WhatsApp

This comprehensive user journey documentation ensures that all user interactions are well-defined and optimized for the best possible experience across all touchpoints of the Taxicab platform.
