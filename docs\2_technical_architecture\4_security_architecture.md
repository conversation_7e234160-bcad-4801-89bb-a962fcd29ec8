# 4. Security Architecture

## 🔐 **Security Overview**

The Taxicab platform implements a comprehensive security architecture built on Supabase's enterprise-grade security features, including Row Level Security (RLS), JWT-based authentication, and end-to-end encryption. The security model follows defense-in-depth principles with multiple layers of protection.

### **Security Principles**
- **Zero Trust Architecture**: Never trust, always verify
- **Principle of Least Privilege**: Users access only what they need
- **Defense in Depth**: Multiple security layers for comprehensive protection
- **Data Privacy by Design**: Privacy considerations built into every feature
- **Compliance Ready**: Designed for regulatory compliance requirements

## 🔑 **Authentication and Authorization**

### **Multi-Factor Authentication (MFA)**

#### **Primary Authentication: Phone + OTP**
```typescript
// Phone-based authentication flow
interface PhoneAuthFlow {
  // Step 1: Request OTP
  requestOTP: (phone: string) => Promise<{
    message_id: string;
    expires_at: number;
  }>;
  
  // Step 2: Verify OTP
  verifyOTP: (phone: string, otp: string) => Promise<{
    access_token: string;
    refresh_token: string;
    user: UserProfile;
  }>;
}

// Implementation with Supabase Auth
const phoneAuth = {
  async requestOTP(phone: string) {
    const { data, error } = await supabase.auth.signInWithOtp({
      phone: phone,
      options: {
        shouldCreateUser: false // Only for existing users
      }
    });
    
    if (error) throw new AuthError(error.message);
    return data;
  },
  
  async verifyOTP(phone: string, otp: string) {
    const { data, error } = await supabase.auth.verifyOtp({
      phone: phone,
      token: otp,
      type: 'sms'
    });
    
    if (error) throw new AuthError(error.message);
    return data;
  }
};
```

#### **Secondary Authentication: SMS 2FA**
```typescript
// Enhanced security for sensitive operations
interface SecondaryAuth {
  // Trigger 2FA for sensitive operations
  requireSecondaryAuth: (operation: string) => Promise<string>; // Returns challenge_id
  
  // Verify secondary authentication
  verifySecondaryAuth: (challenge_id: string, code: string) => Promise<boolean>;
}

// Implementation for sensitive operations
const secondaryAuth = {
  async requireSecondaryAuth(operation: string) {
    const user = await supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');
    
    const { data, error } = await supabase.functions.invoke('send-2fa-code', {
      body: {
        user_id: user.data.user.id,
        operation: operation,
        phone: user.data.user.phone
      }
    });
    
    if (error) throw error;
    return data.challenge_id;
  },
  
  async verifySecondaryAuth(challenge_id: string, code: string) {
    const { data, error } = await supabase.functions.invoke('verify-2fa-code', {
      body: { challenge_id, code }
    });
    
    if (error) throw error;
    return data.verified;
  }
};
```

### **JWT Token Management**

#### **Token Structure and Claims**
```typescript
interface TaxicabJWTPayload {
  // Standard JWT claims
  sub: string; // User ID
  iat: number; // Issued at
  exp: number; // Expires at
  
  // Custom claims
  role: 'passenger' | 'driver' | 'admin' | 'corporate_admin';
  phone: string;
  email?: string;
  
  // Driver-specific claims
  driver_id?: string;
  association_id?: string;
  is_online?: boolean;
  
  // Corporate-specific claims
  corporate_account_id?: string;
  spending_limit?: number;
  
  // Security metadata
  session_id: string;
  device_id: string;
  ip_address: string;
}
```

#### **Token Refresh and Rotation**
```typescript
// Automatic token refresh with rotation
class TokenManager {
  private refreshTimer?: NodeJS.Timeout;
  
  async initializeTokenRefresh() {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) return;
    
    // Schedule refresh 5 minutes before expiration
    const refreshTime = (session.expires_at! * 1000) - Date.now() - (5 * 60 * 1000);
    
    this.refreshTimer = setTimeout(async () => {
      await this.refreshToken();
    }, refreshTime);
  }
  
  async refreshToken() {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      
      // Schedule next refresh
      await this.initializeTokenRefresh();
      
      // Update stored tokens securely
      await this.storeTokensSecurely(data.session);
    } catch (error) {
      // Handle refresh failure - redirect to login
      await this.handleRefreshFailure(error);
    }
  }
  
  private async storeTokensSecurely(session: Session) {
    // Use secure storage (Keychain on iOS, Keystore on Android)
    await SecureStore.setItemAsync('access_token', session.access_token);
    await SecureStore.setItemAsync('refresh_token', session.refresh_token);
  }
}
```

## 🛡️ **Row Level Security (RLS) Policies**

### **Comprehensive RLS Implementation**

#### **Users Table Security**
```sql
-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own profile
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid() = id);

-- Policy: Users can update their own profile
CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy: Admins can view all users
CREATE POLICY "users_admin_all" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy: Drivers can view passenger profiles during active rides
CREATE POLICY "users_driver_view_passenger" ON users
  FOR SELECT USING (
    role = 'passenger' AND
    EXISTS (
      SELECT 1 FROM rides r
      JOIN drivers d ON r.driver_id = d.id
      WHERE r.passenger_id = users.id
        AND d.user_id = auth.uid()
        AND r.status IN ('accepted', 'driver_arrived', 'in_progress')
    )
  );
```

#### **Rides Table Security**
```sql
-- Enable RLS on rides table
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;

-- Policy: Passengers can view their own rides
CREATE POLICY "rides_passenger_own" ON rides
  FOR SELECT USING (passenger_id = auth.uid());

-- Policy: Passengers can create rides
CREATE POLICY "rides_passenger_create" ON rides
  FOR INSERT WITH CHECK (passenger_id = auth.uid());

-- Policy: Drivers can view assigned rides
CREATE POLICY "rides_driver_assigned" ON rides
  FOR SELECT USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Policy: Drivers can update assigned rides
CREATE POLICY "rides_driver_update" ON rides
  FOR UPDATE USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Policy: Corporate admins can view employee rides
CREATE POLICY "rides_corporate_admin" ON rides
  FOR SELECT USING (
    passenger_id IN (
      SELECT ce.user_id 
      FROM corporate_employees ce
      JOIN corporate_accounts ca ON ce.corporate_account_id = ca.id
      WHERE ca.admin_user_id = auth.uid()
    )
  );
```

#### **Driver Locations Security**
```sql
-- Enable RLS on driver_locations table
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;

-- Policy: Drivers can insert their own location
CREATE POLICY "driver_locations_insert_own" ON driver_locations
  FOR INSERT WITH CHECK (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Policy: Passengers can view driver location during active ride
CREATE POLICY "driver_locations_passenger_view" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM rides 
      WHERE passenger_id = auth.uid() 
        AND driver_id = driver_locations.driver_id
        AND status IN ('accepted', 'driver_arrived', 'in_progress')
        AND created_at >= NOW() - INTERVAL '24 hours'
    )
  );

-- Policy: Admins can view all locations
CREATE POLICY "driver_locations_admin_all" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

## 🔒 **Data Encryption and Privacy**

### **Encryption at Rest**
```typescript
// Sensitive data encryption using Supabase Vault
interface EncryptedField {
  encrypt: (plaintext: string) => Promise<string>;
  decrypt: (ciphertext: string) => Promise<string>;
}

// Implementation for PII encryption
class PIIEncryption implements EncryptedField {
  async encrypt(plaintext: string): Promise<string> {
    const { data, error } = await supabase
      .rpc('vault_encrypt', {
        plaintext: plaintext,
        key_id: 'pii-encryption-key'
      });
    
    if (error) throw new EncryptionError(error.message);
    return data;
  }
  
  async decrypt(ciphertext: string): Promise<string> {
    const { data, error } = await supabase
      .rpc('vault_decrypt', {
        ciphertext: ciphertext,
        key_id: 'pii-encryption-key'
      });
    
    if (error) throw new DecryptionError(error.message);
    return data;
  }
}

// Usage for sensitive fields
const piiEncryption = new PIIEncryption();

// Encrypt before storing
const encryptedPhone = await piiEncryption.encrypt(user.phone);
const encryptedEmail = await piiEncryption.encrypt(user.email);

// Decrypt when retrieving
const plainPhone = await piiEncryption.decrypt(encryptedPhone);
```

### **Encryption in Transit**
```typescript
// TLS/SSL configuration for all communications
const secureApiClient = {
  // Force HTTPS for all API calls
  baseURL: 'https://your-project.supabase.co',
  
  // Certificate pinning for mobile apps
  certificatePinning: {
    hostname: 'your-project.supabase.co',
    publicKeyHash: 'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='
  },
  
  // Request/Response encryption for sensitive data
  async encryptRequest(data: any): Promise<string> {
    // Implement client-side encryption for extra security
    return await encrypt(JSON.stringify(data), CLIENT_ENCRYPTION_KEY);
  },
  
  async decryptResponse(encryptedData: string): Promise<any> {
    const decrypted = await decrypt(encryptedData, CLIENT_ENCRYPTION_KEY);
    return JSON.parse(decrypted);
  }
};
```

## 🚨 **Security Monitoring and Incident Response**

### **Real-Time Security Monitoring**
```typescript
// Security event monitoring
interface SecurityEvent {
  event_type: 'login_attempt' | 'failed_auth' | 'suspicious_activity' | 'data_access';
  user_id?: string;
  ip_address: string;
  user_agent: string;
  timestamp: string;
  metadata: Record<string, any>;
}

class SecurityMonitor {
  async logSecurityEvent(event: SecurityEvent) {
    // Log to Supabase for analysis
    await supabase
      .from('security_events')
      .insert(event);
    
    // Check for suspicious patterns
    await this.analyzeSuspiciousActivity(event);
  }
  
  private async analyzeSuspiciousActivity(event: SecurityEvent) {
    // Multiple failed login attempts
    if (event.event_type === 'failed_auth') {
      const recentFailures = await this.getRecentFailedAttempts(event.ip_address);
      if (recentFailures >= 5) {
        await this.triggerSecurityAlert('multiple_failed_logins', event);
      }
    }
    
    // Unusual location access
    if (event.event_type === 'login_attempt') {
      const isUnusualLocation = await this.checkUnusualLocation(event);
      if (isUnusualLocation) {
        await this.triggerSecurityAlert('unusual_location', event);
      }
    }
  }
  
  private async triggerSecurityAlert(alertType: string, event: SecurityEvent) {
    // Send alert to security team
    await supabase.functions.invoke('security-alert', {
      body: { alertType, event }
    });
    
    // Implement automatic response if needed
    if (alertType === 'multiple_failed_logins') {
      await this.temporaryIPBlock(event.ip_address);
    }
  }
}
```

### **Audit Trail Implementation**
```sql
-- Audit trail table for compliance
CREATE TABLE audit_trail (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name VARCHAR(100) NOT NULL,
  operation VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
  record_id UUID NOT NULL,
  old_values JSONB,
  new_values JSONB,
  user_id UUID REFERENCES users(id),
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_trail (
    table_name, operation, record_id, old_values, new_values, 
    user_id, ip_address, timestamp
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
    CASE WHEN TG_OP = 'INSERT' THEN row_to_json(NEW) 
         WHEN TG_OP = 'UPDATE' THEN row_to_json(NEW) 
         ELSE NULL END,
    auth.uid(),
    inet_client_addr(),
    NOW()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_rides AFTER INSERT OR UPDATE OR DELETE ON rides
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

## 🛡️ **API Security and Rate Limiting**

### **Rate Limiting Implementation**
```typescript
// Rate limiting configuration
const rateLimitConfig = {
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: 'Too many authentication attempts'
  },
  
  // General API endpoints
  api: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // 100 requests per minute
    message: 'Rate limit exceeded'
  },
  
  // Location updates (higher limit for drivers)
  location: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60, // 60 updates per minute
    message: 'Location update rate limit exceeded'
  }
};

// Edge Function rate limiting
const rateLimiter = async (request: Request, config: any) => {
  const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
  const key = `rate_limit:${clientIP}:${request.url}`;
  
  const current = await redis.incr(key);
  if (current === 1) {
    await redis.expire(key, config.windowMs / 1000);
  }
  
  if (current > config.max) {
    return new Response(config.message, { status: 429 });
  }
  
  return null; // Continue processing
};
```

This comprehensive security architecture ensures that the Taxicab platform maintains the highest security standards while providing a seamless user experience across all applications and user types.
