# 3. API Specifications

## 🔌 **API Overview**

The Taxicab platform uses Supabase's auto-generated RESTful APIs combined with custom Edge Functions for complex business logic. All APIs follow REST principles with consistent response formats, proper HTTP status codes, and comprehensive error handling.

### **API Design Principles**
- **RESTful Design**: Standard REST conventions for resource manipulation
- **Type Safety**: TypeScript interfaces for all API contracts
- **Consistent Responses**: Standardized response formats across all endpoints
- **Error Handling**: Comprehensive error responses with actionable messages
- **Authentication**: JWT-based authentication with Row Level Security

### **Base Configuration**
```typescript
// Supabase client configuration
const supabaseUrl = 'https://your-project.supabase.co'
const supabaseAnonKey = 'your-anon-key'

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})
```

## 🔐 **Authentication APIs**

### **User Registration**
```typescript
// POST /auth/v1/signup
interface RegisterRequest {
  phone: string;
  email?: string;
  full_name: string;
  role: 'passenger' | 'driver' | 'corporate_admin';
}

interface RegisterResponse {
  user: {
    id: string;
    phone: string;
    email?: string;
    full_name: string;
    role: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

// Implementation
const registerUser = async (data: RegisterRequest): Promise<RegisterResponse> => {
  const { data: authData, error } = await supabase.auth.signUp({
    phone: data.phone,
    password: generateTemporaryPassword(), // SMS OTP will be primary auth
    options: {
      data: {
        full_name: data.full_name,
        role: data.role
      }
    }
  });
  
  if (error) throw error;
  return authData;
};
```

### **Phone Verification**
```typescript
// POST /auth/v1/verify
interface VerifyPhoneRequest {
  phone: string;
  token: string; // OTP code
  type: 'sms';
}

interface VerifyPhoneResponse {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: UserProfile;
}

const verifyPhone = async (data: VerifyPhoneRequest): Promise<VerifyPhoneResponse> => {
  const { data: authData, error } = await supabase.auth.verifyOtp({
    phone: data.phone,
    token: data.token,
    type: 'sms'
  });
  
  if (error) throw error;
  return authData;
};
```

## 🚗 **Ride Management APIs**

### **Create Ride Request**
```typescript
// POST /rest/v1/rides
interface CreateRideRequest {
  pickup_address: string;
  pickup_coordinates: {
    latitude: number;
    longitude: number;
  };
  destination_address: string;
  destination_coordinates: {
    latitude: number;
    longitude: number;
  };
  service_type: 'movers_ride' | 'movers_exec' | 'movers_xl' | 'movers_cruiser';
  scheduled_time?: string; // ISO 8601 format
  passenger_notes?: string;
}

interface CreateRideResponse {
  id: string;
  status: 'requested';
  estimated_fare: number;
  estimated_duration: number;
  estimated_distance: number;
  created_at: string;
}

const createRide = async (data: CreateRideRequest): Promise<CreateRideResponse> => {
  // Calculate fare estimate using Edge Function
  const { data: fareData } = await supabase.functions.invoke('calculate-fare', {
    body: {
      pickup: data.pickup_coordinates,
      destination: data.destination_coordinates,
      service_type: data.service_type
    }
  });

  const { data: ride, error } = await supabase
    .from('rides')
    .insert({
      ...data,
      pickup_coordinates: `POINT(${data.pickup_coordinates.longitude} ${data.pickup_coordinates.latitude})`,
      destination_coordinates: `POINT(${data.destination_coordinates.longitude} ${data.destination_coordinates.latitude})`,
      estimated_fare: fareData.estimated_fare,
      passenger_id: (await supabase.auth.getUser()).data.user?.id
    })
    .select()
    .single();

  if (error) throw error;
  return ride;
};
```

### **Driver Accept Ride**
```typescript
// PATCH /rest/v1/rides?id=eq.{ride_id}
interface AcceptRideRequest {
  driver_id: string;
  estimated_arrival: number; // minutes
}

interface AcceptRideResponse {
  id: string;
  status: 'accepted';
  driver: DriverProfile;
  estimated_arrival: number;
  accepted_at: string;
}

const acceptRide = async (rideId: string, data: AcceptRideRequest): Promise<AcceptRideResponse> => {
  const { data: ride, error } = await supabase
    .from('rides')
    .update({
      driver_id: data.driver_id,
      status: 'accepted',
      accepted_at: new Date().toISOString()
    })
    .eq('id', rideId)
    .eq('status', 'requested') // Ensure ride is still available
    .select(`
      *,
      driver:drivers(
        id,
        user:users(full_name, profile_image_url),
        vehicle:vehicles(make, model, color, license_plate),
        rating
      )
    `)
    .single();

  if (error) throw error;
  return ride;
};
```

### **Update Ride Status**
```typescript
// PATCH /rest/v1/rides?id=eq.{ride_id}
interface UpdateRideStatusRequest {
  status: 'driver_arrived' | 'in_progress' | 'completed' | 'cancelled';
  current_location?: {
    latitude: number;
    longitude: number;
  };
  cancellation_reason?: string;
  final_fare?: number;
}

const updateRideStatus = async (
  rideId: string, 
  data: UpdateRideStatusRequest
): Promise<RideDetails> => {
  const updateData: any = {
    status: data.status,
    updated_at: new Date().toISOString()
  };

  // Add timestamp for specific status changes
  switch (data.status) {
    case 'in_progress':
      updateData.started_at = new Date().toISOString();
      break;
    case 'completed':
      updateData.completed_at = new Date().toISOString();
      updateData.final_fare = data.final_fare;
      break;
    case 'cancelled':
      updateData.cancelled_at = new Date().toISOString();
      updateData.cancellation_reason = data.cancellation_reason;
      break;
  }

  const { data: ride, error } = await supabase
    .from('rides')
    .update(updateData)
    .eq('id', rideId)
    .select()
    .single();

  if (error) throw error;
  return ride;
};
```

## 🚕 **Driver Management APIs**

### **Driver Registration**
```typescript
// POST /rest/v1/drivers
interface DriverRegistrationRequest {
  user_id: string;
  association_id?: string;
  license_number: string;
  license_expiry: string;
  vehicle: {
    make: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    category: 'lite' | 'standard' | 'executive' | 'xl';
  };
  documents: {
    license_front: File;
    license_back: File;
    vehicle_registration: File;
    insurance_certificate: File;
    profile_photo: File;
  };
}

const registerDriver = async (data: DriverRegistrationRequest): Promise<string> => {
  // Upload documents to Supabase Storage
  const documentUrls = await uploadDriverDocuments(data.documents);
  
  // Create vehicle record
  const { data: vehicle, error: vehicleError } = await supabase
    .from('vehicles')
    .insert(data.vehicle)
    .select()
    .single();

  if (vehicleError) throw vehicleError;

  // Create driver record
  const { data: driver, error: driverError } = await supabase
    .from('drivers')
    .insert({
      user_id: data.user_id,
      association_id: data.association_id,
      license_number: data.license_number,
      license_expiry: data.license_expiry,
      vehicle_id: vehicle.id,
      status: 'pending',
      document_urls: documentUrls
    })
    .select()
    .single();

  if (driverError) throw driverError;
  return driver.id;
};
```

### **Update Driver Location**
```typescript
// POST /rest/v1/driver_locations
interface UpdateLocationRequest {
  coordinates: {
    latitude: number;
    longitude: number;
  };
  heading?: number;
  speed?: number;
  accuracy?: number;
}

const updateDriverLocation = async (data: UpdateLocationRequest): Promise<void> => {
  const { error } = await supabase
    .from('driver_locations')
    .insert({
      driver_id: await getCurrentDriverId(),
      coordinates: `POINT(${data.coordinates.longitude} ${data.coordinates.latitude})`,
      heading: data.heading,
      speed: data.speed,
      accuracy: data.accuracy,
      timestamp: new Date().toISOString()
    });

  if (error) throw error;

  // Also update driver's last known location
  await supabase
    .from('drivers')
    .update({
      last_location: `POINT(${data.coordinates.longitude} ${data.coordinates.latitude})`,
      last_location_update: new Date().toISOString()
    })
    .eq('user_id', (await supabase.auth.getUser()).data.user?.id);
};
```

### **Toggle Driver Online Status**
```typescript
// PATCH /rest/v1/drivers?user_id=eq.{user_id}
interface ToggleOnlineStatusRequest {
  is_online: boolean;
  current_location?: {
    latitude: number;
    longitude: number;
  };
}

const toggleOnlineStatus = async (data: ToggleOnlineStatusRequest): Promise<boolean> => {
  const updateData: any = {
    is_online: data.is_online,
    updated_at: new Date().toISOString()
  };

  if (data.current_location) {
    updateData.last_location = `POINT(${data.current_location.longitude} ${data.current_location.latitude})`;
    updateData.last_location_update = new Date().toISOString();
  }

  const { error } = await supabase
    .from('drivers')
    .update(updateData)
    .eq('user_id', (await supabase.auth.getUser()).data.user?.id);

  if (error) throw error;
  return data.is_online;
};
```

## 💳 **Payment APIs**

### **Process Payment**
```typescript
// POST /functions/v1/process-payment
interface ProcessPaymentRequest {
  ride_id: string;
  amount: number;
  payment_method: 'cash' | 'ecocash' | 'onemoney' | 'card';
  payment_details?: {
    phone?: string; // For mobile money
    card_token?: string; // For card payments
  };
}

interface ProcessPaymentResponse {
  payment_id: string;
  status: 'pending' | 'completed' | 'failed';
  transaction_id?: string;
  message: string;
}

// Edge Function implementation
const processPayment = async (data: ProcessPaymentRequest): Promise<ProcessPaymentResponse> => {
  const { data: payment, error } = await supabase.functions.invoke('process-payment', {
    body: data
  });

  if (error) throw error;
  return payment;
};
```

## 📊 **Analytics and Reporting APIs**

### **Driver Analytics**
```typescript
// GET /rest/v1/rpc/driver_analytics
interface DriverAnalyticsRequest {
  driver_id: string;
  start_date: string;
  end_date: string;
}

interface DriverAnalyticsResponse {
  total_rides: number;
  total_earnings: number;
  average_rating: number;
  total_distance: number;
  total_hours: number;
  earnings_breakdown: {
    gross_earnings: number;
    platform_commission: number;
    association_commission: number;
    net_earnings: number;
  };
}

// Database function call
const getDriverAnalytics = async (params: DriverAnalyticsRequest): Promise<DriverAnalyticsResponse> => {
  const { data, error } = await supabase
    .rpc('get_driver_analytics', {
      driver_id: params.driver_id,
      start_date: params.start_date,
      end_date: params.end_date
    });

  if (error) throw error;
  return data;
};
```

This API specification provides a comprehensive foundation for all platform operations, ensuring type safety, consistency, and optimal performance across all client applications.
