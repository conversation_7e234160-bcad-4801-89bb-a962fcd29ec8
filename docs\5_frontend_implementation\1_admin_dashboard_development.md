# 1. Admin Dashboard Development

## 🖥️ **Admin Dashboard Overview**

The Taxicab admin dashboard is built using React 18+ with TypeScript, Shadcn UI components, and Tailwind CSS. It provides comprehensive platform management capabilities including user management, ride monitoring, analytics, and system configuration.

### **Dashboard Architecture Principles**
- **Component-Based Design**: Reusable, modular components with clear separation of concerns
- **Type Safety**: Full TypeScript implementation with strict type checking
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Real-Time Updates**: Live data synchronization using Supabase subscriptions
- **Performance Optimized**: Lazy loading, virtualization, and efficient state management

## 🏗️ **Project Structure and Setup**

### **1. Dashboard Project Structure**
```
apps/admin-dashboard/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # Shadcn UI components
│   │   ├── forms/           # Form components
│   │   ├── charts/          # Chart and visualization components
│   │   ├── tables/          # Data table components
│   │   └── layout/          # Layout components
│   ├── pages/               # Page components
│   │   ├── dashboard/       # Dashboard overview
│   │   ├── drivers/         # Driver management
│   │   ├── rides/           # Ride monitoring
│   │   ├── users/           # User management
│   │   ├── analytics/       # Analytics and reports
│   │   └── settings/        # System settings
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API service functions
│   ├── stores/              # Zustand state stores
│   ├── types/               # TypeScript type definitions
│   ├── utils/               # Utility functions
│   ├── lib/                 # Library configurations
│   └── assets/              # Static assets
├── public/                  # Public assets
├── package.json
├── tailwind.config.js
├── tsconfig.json
└── vite.config.ts
```

### **2. Dependencies and Configuration**
```json
// package.json
{
  "name": "taxicab-admin-dashboard",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "@supabase/supabase-js": "^2.38.0",
    "@tanstack/react-query": "^5.0.0",
    "zustand": "^4.4.0",
    "lucide-react": "^0.294.0",
    "date-fns": "^2.30.0",
    "recharts": "^2.8.0",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.0",
    "zod": "^3.22.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "autoprefixer": "^10.4.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.0",
    "postcss": "^8.4.0",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0"
  }
}
```

### **3. Vite Configuration**
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/lib': path.resolve(__dirname, './src/lib'),
    },
  },
  server: {
    port: 3000,
    host: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
```

## 🎨 **UI Components and Design System**

### **1. Shadcn UI Configuration**
```typescript
// src/lib/utils.ts
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format currency for Zimbabwe
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-ZW', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(amount);
}

// Format date for local timezone
export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-ZW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
}
```

### **2. Custom Dashboard Components**

#### **Dashboard Layout Component**
```typescript
// src/components/layout/DashboardLayout.tsx
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { cn } from '@/lib/utils';

interface DashboardLayoutProps {
  className?: string;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ className }) => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className={cn('min-h-screen bg-background', className)}>
      <Sidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />
      
      <div className="lg:pl-72">
        <Header onMenuClick={() => setSidebarOpen(true)} />
        
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};
```

#### **Sidebar Navigation Component**
```typescript
// src/components/layout/Sidebar.tsx
import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Car, 
  MapPin, 
  BarChart3, 
  Settings,
  X 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: LayoutDashboard },
  { name: 'Drivers', href: '/drivers', icon: Car },
  { name: 'Rides', href: '/rides', icon: MapPin },
  { name: 'Users', href: '/users', icon: Users },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export const Sidebar: React.FC<SidebarProps> = ({ open, onOpenChange }) => {
  return (
    <>
      {/* Mobile overlay */}
      {open && (
        <div 
          className="fixed inset-0 z-50 bg-black/80 lg:hidden"
          onClick={() => onOpenChange(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-xl transition-transform duration-300 ease-in-out lg:translate-x-0',
        open ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex h-16 items-center justify-between px-6">
          <div className="flex items-center">
            <Car className="h-8 w-8 text-primary" />
            <span className="ml-2 text-xl font-bold">Taxicab Admin</span>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-2">
            {navigation.map((item) => (
              <li key={item.name}>
                <NavLink
                  to={item.href}
                  className={({ isActive }) => cn(
                    'flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                  )}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </>
  );
};
```

#### **Statistics Cards Component**
```typescript
// src/components/dashboard/StatsCards.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: React.ReactNode;
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  changeLabel,
  icon,
  className,
}) => {
  const getTrendIcon = () => {
    if (change === undefined) return null;
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = () => {
    if (change === undefined) return '';
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <Card className={cn('', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change !== undefined && (
          <div className={cn('flex items-center text-xs', getTrendColor())}>
            {getTrendIcon()}
            <span className="ml-1">
              {Math.abs(change)}% {changeLabel || 'from last month'}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

interface StatsCardsProps {
  stats: {
    totalRides: number;
    activeDrivers: number;
    totalRevenue: number;
    averageRating: number;
    ridesChange?: number;
    driversChange?: number;
    revenueChange?: number;
    ratingChange?: number;
  };
}

export const StatsCards: React.FC<StatsCardsProps> = ({ stats }) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Rides"
        value={stats.totalRides.toLocaleString()}
        change={stats.ridesChange}
        icon={<MapPin className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Active Drivers"
        value={stats.activeDrivers.toLocaleString()}
        change={stats.driversChange}
        icon={<Car className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Total Revenue"
        value={`$${stats.totalRevenue.toLocaleString()}`}
        change={stats.revenueChange}
        icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Average Rating"
        value={stats.averageRating.toFixed(1)}
        change={stats.ratingChange}
        icon={<Star className="h-4 w-4 text-muted-foreground" />}
      />
    </div>
  );
};
```

## 📊 **Data Tables and Management**

### **1. Driver Management Table**
```typescript
// src/components/drivers/DriversTable.tsx
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MoreHorizontal, Eye, CheckCircle, XCircle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDate } from '@/lib/utils';
import type { Driver } from '@/types/driver';

interface DriversTableProps {
  drivers: Driver[];
  onViewDriver: (driver: Driver) => void;
  onApproveDriver: (driverId: string) => void;
  onRejectDriver: (driverId: string) => void;
  onSuspendDriver: (driverId: string) => void;
}

export const DriversTable: React.FC<DriversTableProps> = ({
  drivers,
  onViewDriver,
  onApproveDriver,
  onRejectDriver,
  onSuspendDriver,
}) => {
  const getStatusBadge = (status: string) => {
    const variants = {
      approved: 'default',
      pending: 'secondary',
      rejected: 'destructive',
      suspended: 'outline',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getOnlineStatus = (isOnline: boolean) => (
    <div className="flex items-center">
      <div className={`h-2 w-2 rounded-full mr-2 ${isOnline ? 'bg-green-500' : 'bg-gray-300'}`} />
      {isOnline ? 'Online' : 'Offline'}
    </div>
  );

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Driver</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Association</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Online</TableHead>
            <TableHead>Rating</TableHead>
            <TableHead>Total Rides</TableHead>
            <TableHead>Joined</TableHead>
            <TableHead className="w-[70px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {drivers.map((driver) => (
            <TableRow key={driver.id}>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={driver.user.profile_image_url} />
                    <AvatarFallback>
                      {driver.user.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{driver.user.full_name}</div>
                    <div className="text-sm text-muted-foreground">{driver.user.phone}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">
                    {driver.vehicle.make} {driver.vehicle.model}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {driver.vehicle.license_plate} • {driver.vehicle.color}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {driver.association?.name || 'Independent'}
              </TableCell>
              <TableCell>
                {getStatusBadge(driver.status)}
              </TableCell>
              <TableCell>
                {getOnlineStatus(driver.is_online)}
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                  {driver.rating.toFixed(1)}
                </div>
              </TableCell>
              <TableCell>{driver.total_rides}</TableCell>
              <TableCell>{formatDate(driver.created_at)}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onViewDriver(driver)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    {driver.status === 'pending' && (
                      <>
                        <DropdownMenuItem onClick={() => onApproveDriver(driver.id)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Approve
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onRejectDriver(driver.id)}>
                          <XCircle className="mr-2 h-4 w-4" />
                          Reject
                        </DropdownMenuItem>
                      </>
                    )}
                    {driver.status === 'approved' && (
                      <DropdownMenuItem onClick={() => onSuspendDriver(driver.id)}>
                        <XCircle className="mr-2 h-4 w-4" />
                        Suspend
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
```

### **2. Real-Time Ride Monitoring**
```typescript
// src/components/rides/RideMonitor.tsx
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Clock, DollarSign } from 'lucide-react';
import { useRealtimeRides } from '@/hooks/useRealtimeRides';
import { formatCurrency, formatDate } from '@/lib/utils';
import type { Ride } from '@/types/ride';

export const RideMonitor: React.FC = () => {
  const { activeRides, completedRides, loading } = useRealtimeRides();

  const getStatusColor = (status: string) => {
    const colors = {
      requested: 'bg-yellow-100 text-yellow-800',
      accepted: 'bg-blue-100 text-blue-800',
      driver_arrived: 'bg-purple-100 text-purple-800',
      in_progress: 'bg-green-100 text-green-800',
      completed: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || colors.requested;
  };

  if (loading) {
    return <div>Loading rides...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Active Rides */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5" />
            Active Rides ({activeRides.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activeRides.map((ride) => (
              <div key={ride.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge className={getStatusColor(ride.status)}>
                      {ride.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(ride.created_at)}
                    </span>
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">{ride.pickup_address}</div>
                    <div className="text-muted-foreground">→ {ride.destination_address}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{formatCurrency(ride.estimated_fare)}</div>
                  <div className="text-sm text-muted-foreground">
                    {ride.distance_km?.toFixed(1)} km
                  </div>
                </div>
              </div>
            ))}
            {activeRides.length === 0 && (
              <div className="text-center text-muted-foreground py-8">
                No active rides at the moment
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Completed Rides */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            Recent Completed Rides
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {completedRides.slice(0, 10).map((ride) => (
              <div key={ride.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                <div className="flex-1">
                  <div className="text-sm font-medium truncate">
                    {ride.pickup_address} → {ride.destination_address}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(ride.completed_at!)}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{formatCurrency(ride.final_fare!)}</div>
                  <div className="text-xs text-muted-foreground">
                    {ride.distance_km?.toFixed(1)} km
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
```

This comprehensive admin dashboard implementation provides a robust, type-safe, and user-friendly interface for managing all aspects of the Taxicab platform with real-time updates and responsive design.
