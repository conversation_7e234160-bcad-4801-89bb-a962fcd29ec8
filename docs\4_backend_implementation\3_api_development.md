# 3. API Development

## 🔌 **API Development Overview**

The Taxicab platform API is built using Supabase's auto-generated REST API combined with custom Edge Functions for complex business logic. This document covers the complete implementation of RESTful APIs, Edge Functions, and real-time subscriptions.

### **API Architecture Principles**
- **RESTful Design**: Standard REST conventions with proper HTTP methods and status codes
- **Type Safety**: TypeScript interfaces for all API contracts and responses
- **Error Handling**: Comprehensive error responses with actionable messages
- **Performance**: Optimized queries with proper caching and pagination
- **Security**: Row Level Security and authentication on all endpoints

## 🏗️ **Supabase REST API Implementation**

### **1. API Client Configuration**
```typescript
// packages/api-client/src/supabase.ts
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from './types/database';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!;

export const supabase: SupabaseClient<Database> = createClient(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'taxicab-platform',
      },
    },
  }
);

// Type-safe database interface
export type Tables = Database['public']['Tables'];
export type Enums = Database['public']['Enums'];
```

### **2. Core API Services**

#### **User Management API**
```typescript
// packages/api-client/src/services/userService.ts
import { supabase } from '../supabase';
import type { Tables } from '../supabase';

export type User = Tables['users']['Row'];
export type UserInsert = Tables['users']['Insert'];
export type UserUpdate = Tables['users']['Update'];

export class UserService {
  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        throw new ApiError('Failed to fetch user profile', error.message);
      }

      return data;
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(
    userId: string,
    updates: UserUpdate
  ): Promise<User> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new ApiError('Failed to update profile', error.message);
      }

      return data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Search users with pagination
   */
  static async searchUsers(
    query: string,
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResponse<User>> {
    try {
      const offset = (page - 1) * limit;

      const { data, error, count } = await supabase
        .from('users')
        .select('*', { count: 'exact' })
        .or(`full_name.ilike.%${query}%, phone.ilike.%${query}%, email.ilike.%${query}%`)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (error) {
        throw new ApiError('Failed to search users', error.message);
      }

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  /**
   * Get user by ID (admin only)
   */
  static async getUserById(userId: string): Promise<User> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        throw new ApiError('Failed to fetch user', error.message);
      }

      return data;
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      throw error;
    }
  }
}
```

#### **Ride Management API**
```typescript
// packages/api-client/src/services/rideService.ts
export type Ride = Tables['rides']['Row'];
export type RideInsert = Tables['rides']['Insert'];
export type RideUpdate = Tables['rides']['Update'];

export interface CreateRideRequest {
  pickup_address: string;
  pickup_coordinates: Coordinates;
  destination_address: string;
  destination_coordinates: Coordinates;
  service_type: Enums['service_type'];
  scheduled_time?: string;
  passenger_notes?: string;
}

export interface RideWithDetails extends Ride {
  driver?: {
    id: string;
    user: {
      full_name: string;
      profile_image_url?: string;
    };
    vehicle: {
      make: string;
      model: string;
      color: string;
      license_plate: string;
    };
    rating: number;
  };
  passenger: {
    full_name: string;
    phone: string;
  };
}

export class RideService {
  /**
   * Create new ride request
   */
  static async createRide(request: CreateRideRequest): Promise<Ride> {
    try {
      // Get fare estimate first
      const fareEstimate = await this.calculateFareEstimate(
        request.pickup_coordinates,
        request.destination_coordinates,
        request.service_type
      );

      const { data: user } = await supabase.auth.getUser();
      if (!user.user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('rides')
        .insert({
          passenger_id: user.user.id,
          pickup_address: request.pickup_address,
          pickup_coordinates: `POINT(${request.pickup_coordinates.longitude} ${request.pickup_coordinates.latitude})`,
          destination_address: request.destination_address,
          destination_coordinates: `POINT(${request.destination_coordinates.longitude} ${request.destination_coordinates.latitude})`,
          service_type: request.service_type,
          estimated_fare: fareEstimate.estimated_fare,
          distance_km: fareEstimate.distance_km,
          status: 'requested',
        })
        .select()
        .single();

      if (error) {
        throw new ApiError('Failed to create ride', error.message);
      }

      // Trigger driver matching
      await this.triggerDriverMatching(data.id);

      return data;
    } catch (error) {
      console.error('Error creating ride:', error);
      throw error;
    }
  }

  /**
   * Get ride details with driver and passenger info
   */
  static async getRideDetails(rideId: string): Promise<RideWithDetails> {
    try {
      const { data, error } = await supabase
        .from('rides')
        .select(`
          *,
          driver:drivers(
            id,
            rating,
            user:users(full_name, profile_image_url),
            vehicle:vehicles(make, model, color, license_plate)
          ),
          passenger:users!rides_passenger_id_fkey(full_name, phone)
        `)
        .eq('id', rideId)
        .single();

      if (error) {
        throw new ApiError('Failed to fetch ride details', error.message);
      }

      return data as RideWithDetails;
    } catch (error) {
      console.error('Error fetching ride details:', error);
      throw error;
    }
  }

  /**
   * Update ride status
   */
  static async updateRideStatus(
    rideId: string,
    status: Enums['ride_status'],
    updates?: Partial<RideUpdate>
  ): Promise<Ride> {
    try {
      const updateData: RideUpdate = {
        status,
        updated_at: new Date().toISOString(),
        ...updates,
      };

      // Add timestamp for specific status changes
      switch (status) {
        case 'accepted':
          updateData.accepted_at = new Date().toISOString();
          break;
        case 'in_progress':
          updateData.started_at = new Date().toISOString();
          break;
        case 'completed':
          updateData.completed_at = new Date().toISOString();
          break;
        case 'cancelled':
          updateData.cancelled_at = new Date().toISOString();
          break;
      }

      const { data, error } = await supabase
        .from('rides')
        .update(updateData)
        .eq('id', rideId)
        .select()
        .single();

      if (error) {
        throw new ApiError('Failed to update ride status', error.message);
      }

      return data;
    } catch (error) {
      console.error('Error updating ride status:', error);
      throw error;
    }
  }

  /**
   * Get user's ride history
   */
  static async getRideHistory(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResponse<RideWithDetails>> {
    try {
      const offset = (page - 1) * limit;

      const { data, error, count } = await supabase
        .from('rides')
        .select(`
          *,
          driver:drivers(
            id,
            rating,
            user:users(full_name, profile_image_url),
            vehicle:vehicles(make, model, color, license_plate)
          )
        `, { count: 'exact' })
        .eq('passenger_id', userId)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (error) {
        throw new ApiError('Failed to fetch ride history', error.message);
      }

      return {
        data: data as RideWithDetails[] || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching ride history:', error);
      throw error;
    }
  }

  /**
   * Calculate fare estimate using Edge Function
   */
  private static async calculateFareEstimate(
    pickup: Coordinates,
    destination: Coordinates,
    serviceType: Enums['service_type']
  ): Promise<{ estimated_fare: number; distance_km: number }> {
    try {
      const { data, error } = await supabase.functions.invoke('calculate-fare', {
        body: {
          pickup,
          destination,
          service_type: serviceType,
        },
      });

      if (error) {
        throw new Error(`Fare calculation failed: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error calculating fare:', error);
      throw error;
    }
  }

  /**
   * Trigger driver matching process
   */
  private static async triggerDriverMatching(rideId: string): Promise<void> {
    try {
      await supabase.functions.invoke('driver-matching', {
        body: { ride_id: rideId },
      });
    } catch (error) {
      console.error('Error triggering driver matching:', error);
      // Don't throw error as ride is already created
    }
  }
}
```

## ⚡ **Edge Functions Implementation**

### **1. Fare Calculation Edge Function**
```typescript
// supabase/functions/calculate-fare/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface FareRequest {
  pickup: { latitude: number; longitude: number };
  destination: { latitude: number; longitude: number };
  service_type: string;
  surge_multiplier?: number;
}

interface FareResponse {
  estimated_fare: number;
  distance_km: number;
  duration_minutes: number;
  surge_multiplier: number;
  breakdown: {
    base_fare: number;
    distance_fare: number;
    surge_amount: number;
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { pickup, destination, service_type, surge_multiplier = 1.0 }: FareRequest = await req.json();
    
    // Validate input
    if (!pickup || !destination || !service_type) {
      throw new Error('Missing required parameters');
    }

    // Calculate distance and duration using Google Maps API
    const routeInfo = await calculateRoute(pickup, destination);
    
    // Get fare calculation from database function
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    const { data: baseFare, error } = await supabase
      .rpc('calculate_fare_estimate', {
        distance_km: routeInfo.distance_km,
        service_type,
        surge_multiplier,
      });
    
    if (error) {
      throw new Error(`Fare calculation failed: ${error.message}`);
    }

    // Calculate fare breakdown
    const serviceRates = getServiceRates(service_type);
    const distanceFare = routeInfo.distance_km * serviceRates.rate_per_km;
    const surgeAmount = (serviceRates.base_fare + distanceFare) * (surge_multiplier - 1);

    const response: FareResponse = {
      estimated_fare: baseFare,
      distance_km: routeInfo.distance_km,
      duration_minutes: routeInfo.duration_minutes,
      surge_multiplier,
      breakdown: {
        base_fare: serviceRates.base_fare,
        distance_fare: distanceFare,
        surge_amount: surgeAmount,
      },
    };

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Fare calculation error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});

async function calculateRoute(
  pickup: { latitude: number; longitude: number },
  destination: { latitude: number; longitude: number }
): Promise<{ distance_km: number; duration_minutes: number }> {
  const apiKey = Deno.env.get('GOOGLE_MAPS_API_KEY');
  
  if (!apiKey) {
    // Fallback to Haversine formula if no API key
    const distance = calculateHaversineDistance(pickup, destination);
    return {
      distance_km: distance,
      duration_minutes: Math.round(distance * 2), // Rough estimate: 2 minutes per km
    };
  }

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/directions/json?` +
      `origin=${pickup.latitude},${pickup.longitude}&` +
      `destination=${destination.latitude},${destination.longitude}&` +
      `key=${apiKey}&` +
      `mode=driving&` +
      `traffic_model=best_guess&` +
      `departure_time=now`
    );

    const data = await response.json();
    
    if (data.status !== 'OK' || !data.routes.length) {
      throw new Error('Route calculation failed');
    }

    const route = data.routes[0];
    const leg = route.legs[0];

    return {
      distance_km: leg.distance.value / 1000,
      duration_minutes: Math.round(leg.duration_in_traffic?.value / 60 || leg.duration.value / 60),
    };
  } catch (error) {
    console.error('Google Maps API error:', error);
    // Fallback to Haversine calculation
    const distance = calculateHaversineDistance(pickup, destination);
    return {
      distance_km: distance,
      duration_minutes: Math.round(distance * 2),
    };
  }
}

function calculateHaversineDistance(
  point1: { latitude: number; longitude: number },
  point2: { latitude: number; longitude: number }
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
  const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

function getServiceRates(serviceType: string): { base_fare: number; rate_per_km: number } {
  const rates = {
    'movers_ride': { base_fare: 2.00, rate_per_km: 0.50 },
    'movers_exec': { base_fare: 3.00, rate_per_km: 0.75 },
    'movers_xl': { base_fare: 2.50, rate_per_km: 0.60 },
    'movers_cruiser': { base_fare: 2.00, rate_per_km: 0.45 },
    'movers_express': { base_fare: 1.50, rate_per_km: 0.40 },
  };

  return rates[serviceType as keyof typeof rates] || rates['movers_ride'];
}
```

### **2. Driver Matching Edge Function**
```typescript
// supabase/functions/driver-matching/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface DriverMatchingRequest {
  ride_id: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { ride_id }: DriverMatchingRequest = await req.json();
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get ride details
    const { data: ride, error: rideError } = await supabase
      .from('rides')
      .select('*')
      .eq('id', ride_id)
      .single();

    if (rideError || !ride) {
      throw new Error('Ride not found');
    }

    // Parse pickup coordinates
    const pickupCoords = parsePostGISPoint(ride.pickup_coordinates);
    
    // Find nearby drivers
    const { data: nearbyDrivers, error: driversError } = await supabase
      .rpc('find_nearby_drivers', {
        pickup_point: `POINT(${pickupCoords.longitude} ${pickupCoords.latitude})`,
        radius_km: 10,
        service_category: getVehicleCategoryForService(ride.service_type),
        max_results: 5,
      });

    if (driversError) {
      throw new Error(`Driver search failed: ${driversError.message}`);
    }

    if (!nearbyDrivers || nearbyDrivers.length === 0) {
      // No drivers available - update ride status
      await supabase
        .from('rides')
        .update({ 
          status: 'cancelled',
          cancellation_reason: 'No drivers available',
          cancelled_at: new Date().toISOString(),
        })
        .eq('id', ride_id);

      return new Response(
        JSON.stringify({ message: 'No drivers available' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      );
    }

    // Send ride requests to drivers via real-time notifications
    for (const driver of nearbyDrivers) {
      await sendRideRequestToDriver(supabase, driver.driver_id, ride);
    }

    return new Response(
      JSON.stringify({ 
        message: 'Ride requests sent to drivers',
        drivers_notified: nearbyDrivers.length 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    );
  } catch (error) {
    console.error('Driver matching error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    );
  }
});

function parsePostGISPoint(pointString: string): { latitude: number; longitude: number } {
  const matches = pointString.match(/POINT\(([^)]+)\)/);
  if (!matches) throw new Error('Invalid point format');
  
  const [longitude, latitude] = matches[1].split(' ').map(Number);
  return { latitude, longitude };
}

function getVehicleCategoryForService(serviceType: string): string {
  const mapping = {
    'movers_ride': 'standard',
    'movers_exec': 'executive',
    'movers_xl': 'xl',
    'movers_cruiser': 'standard',
    'movers_express': 'lite',
  };
  
  return mapping[serviceType as keyof typeof mapping] || 'standard';
}

async function sendRideRequestToDriver(
  supabase: any,
  driverId: string,
  ride: any
): Promise<void> {
  // Insert notification for driver
  await supabase
    .from('notifications')
    .insert({
      user_id: ride.passenger_id,
      type: 'ride_request',
      title: 'New Ride Request',
      message: `Ride request from ${ride.pickup_address} to ${ride.destination_address}`,
      data: {
        ride_id: ride.id,
        pickup_address: ride.pickup_address,
        destination_address: ride.destination_address,
        estimated_fare: ride.estimated_fare,
        distance_km: ride.distance_km,
      },
    });
}
```

This comprehensive API development implementation provides robust, type-safe, and performant APIs for all Taxicab platform operations with proper error handling, security, and real-time capabilities.
