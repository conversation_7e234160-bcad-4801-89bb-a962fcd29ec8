# 4. Supabase Setup and Configuration

## 🗄️ **Supabase Overview**

Supabase serves as the primary backend-as-a-service for the Taxicab platform, providing database, authentication, real-time subscriptions, storage, and edge functions. This guide covers complete setup and configuration for development, staging, and production environments.

### **Supabase Services Used**
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Auth**: User authentication with phone verification
- **Realtime**: Live subscriptions for ride tracking and notifications
- **Storage**: File uploads for driver documents and profile images
- **Edge Functions**: Serverless functions for business logic

## 🚀 **Project Setup**

### **1. Supabase Project Creation**
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Create new project (via Supabase Dashboard)
# 1. Go to https://app.supabase.com
# 2. Click "New Project"
# 3. Choose organization
# 4. Project name: "taxicab-platform"
# 5. Database password: Generate strong password
# 6. Region: Choose closest to Zimbabwe (eu-west-1 or ap-south-1)

# Initialize Supabase in your project
cd taxicab-platform
supabase init

# Link to your Supabase project
supabase link --project-ref your-project-ref
```

### **2. Local Development Setup**
```bash
# Start local Supabase stack
supabase start

# This starts:
# - PostgreSQL database (port 54322)
# - Supabase API (port 54321)
# - Supabase Studio (port 54323)
# - Inbucket (email testing, port 54324)
# - Supabase Edge Runtime (port 54325)

# Check status
supabase status

# Output will show:
# API URL: http://localhost:54321
# DB URL: postgresql://postgres:postgres@localhost:54322/postgres
# Studio URL: http://localhost:54323
# Inbucket URL: http://localhost:54324
# anon key: eyJ...
# service_role key: eyJ...
```

### **3. Environment Configuration**
```bash
# Create environment files for each application

# apps/admin-dashboard/.env.local
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_local_anon_key
VITE_SUPABASE_SERVICE_ROLE_KEY=your_local_service_role_key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key
VITE_APP_ENV=development

# apps/passenger-app/.env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_local_anon_key
GOOGLE_MAPS_API_KEY=your_google_maps_key
APP_ENV=development

# apps/driver-app/.env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_local_anon_key
GOOGLE_MAPS_API_KEY=your_google_maps_key
APP_ENV=development

# Production environment (.env.production)
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key
VITE_APP_ENV=production
```

## 🗃️ **Database Setup and Migrations**

### **1. Initial Database Schema**
```sql
-- supabase/migrations/20240101000001_initial_schema.sql

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types
CREATE TYPE user_role AS ENUM ('passenger', 'driver', 'admin', 'corporate_admin');
CREATE TYPE driver_status AS ENUM ('pending', 'approved', 'suspended', 'rejected');
CREATE TYPE vehicle_category AS ENUM ('lite', 'standard', 'executive', 'xl');
CREATE TYPE service_type AS ENUM ('movers_ride', 'movers_exec', 'movers_xl', 'movers_cruiser', 'movers_express');
CREATE TYPE ride_status AS ENUM ('requested', 'accepted', 'driver_arrived', 'in_progress', 'completed', 'cancelled');
CREATE TYPE payment_method AS ENUM ('cash', 'ecocash', 'onemoney', 'card');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE notification_type AS ENUM ('ride_request', 'ride_accepted', 'driver_arrived', 'trip_started', 'trip_completed', 'payment_processed', 'rating_received');

-- Create tables (see database schema document for complete table definitions)
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone VARCHAR(20) UNIQUE NOT NULL,
  email VARCHAR(255),
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'passenger',
  profile_image_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Additional tables...
-- (Include all tables from the database schema document)
```

### **2. Row Level Security (RLS) Policies**
```sql
-- supabase/migrations/20240101000002_rls_policies.sql

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Rides table policies
CREATE POLICY "Passengers can view own rides" ON rides
  FOR SELECT USING (passenger_id = auth.uid());

CREATE POLICY "Drivers can view assigned rides" ON rides
  FOR SELECT USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Additional RLS policies...
-- (Include all policies from the database schema document)
```

### **3. Database Functions**
```sql
-- supabase/migrations/20240101000003_database_functions.sql

-- Function to find nearby drivers
CREATE OR REPLACE FUNCTION find_nearby_drivers(
  pickup_point POINT,
  radius_km DECIMAL DEFAULT 5.0,
  service_category vehicle_category DEFAULT 'standard'
)
RETURNS TABLE (
  driver_id UUID,
  distance_km DECIMAL,
  driver_rating DECIMAL,
  vehicle_info JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    (ST_Distance(d.last_location, pickup_point) / 1000)::DECIMAL as distance,
    d.rating,
    jsonb_build_object(
      'make', v.make,
      'model', v.model,
      'color', v.color,
      'plate', v.license_plate
    ) as vehicle_info
  FROM drivers d
  JOIN vehicles v ON d.vehicle_id = v.id
  WHERE d.is_online = true
    AND d.status = 'approved'
    AND v.category = service_category
    AND ST_DWithin(d.last_location, pickup_point, radius_km * 1000)
  ORDER BY distance, d.rating DESC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate fare estimate
CREATE OR REPLACE FUNCTION calculate_fare_estimate(
  distance_km DECIMAL,
  service_type service_type,
  surge_multiplier DECIMAL DEFAULT 1.0
)
RETURNS DECIMAL AS $$
DECLARE
  base_fare DECIMAL;
  rate_per_km DECIMAL;
  total_fare DECIMAL;
BEGIN
  -- Set base fares and rates based on service type
  CASE service_type
    WHEN 'movers_ride' THEN
      base_fare := 2.00;
      rate_per_km := 0.50;
    WHEN 'movers_exec' THEN
      base_fare := 3.00;
      rate_per_km := 0.75;
    WHEN 'movers_xl' THEN
      base_fare := 2.50;
      rate_per_km := 0.60;
    ELSE
      base_fare := 2.00;
      rate_per_km := 0.50;
  END CASE;
  
  total_fare := (base_fare + (distance_km * rate_per_km)) * surge_multiplier;
  
  RETURN ROUND(total_fare, 2);
END;
$$ LANGUAGE plpgsql;
```

### **4. Migration Management**
```bash
# Create new migration
supabase migration new add_chat_messages_table

# Apply migrations to local database
supabase db push

# Reset local database (destructive)
supabase db reset

# Generate migration from schema changes
supabase db diff --schema public > supabase/migrations/new_migration.sql

# Apply migrations to remote database
supabase db push --linked
```

## 🔐 **Authentication Configuration**

### **1. Auth Settings**
```sql
-- Configure auth settings in Supabase Dashboard
-- Authentication > Settings

-- Site URL: http://localhost:3000 (development)
-- Site URL: https://your-domain.com (production)

-- Redirect URLs:
-- http://localhost:3000/auth/callback
-- https://your-domain.com/auth/callback
-- taxicab://auth/callback (for mobile apps)

-- Email Auth: Disabled (using phone auth only)
-- Phone Auth: Enabled
-- SMS Provider: Twilio

-- JWT Settings:
-- JWT expiry: 3600 (1 hour)
-- Refresh token expiry: 604800 (7 days)
```

### **2. Phone Authentication Setup**
```typescript
// Configure Twilio for SMS in Supabase Dashboard
// Authentication > Settings > SMS

// Twilio Configuration:
// Account SID: your_twilio_account_sid
// Auth Token: your_twilio_auth_token
// Phone Number: your_twilio_phone_number
// Message Template: "Your verification code is {{ .Code }}"

// Test phone authentication
const testPhoneAuth = async () => {
  // Send OTP
  const { data, error } = await supabase.auth.signInWithOtp({
    phone: '+************',
    options: {
      shouldCreateUser: true
    }
  });
  
  if (error) {
    console.error('Error sending OTP:', error);
    return;
  }
  
  console.log('OTP sent successfully');
  
  // Verify OTP (user enters code)
  const { data: verifyData, error: verifyError } = await supabase.auth.verifyOtp({
    phone: '+************',
    token: '123456', // User-entered code
    type: 'sms'
  });
  
  if (verifyError) {
    console.error('Error verifying OTP:', verifyError);
    return;
  }
  
  console.log('User authenticated:', verifyData.user);
};
```

## 📁 **Storage Configuration**

### **1. Storage Buckets Setup**
```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('driver-documents', 'driver-documents', false),
  ('profile-images', 'profile-images', true),
  ('vehicle-images', 'vehicle-images', true);

-- Storage policies for driver documents
CREATE POLICY "Drivers can upload own documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'driver-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Admins can view all documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'driver-documents' AND
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Storage policies for profile images
CREATE POLICY "Users can upload own profile images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profile-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Profile images are publicly viewable" ON storage.objects
  FOR SELECT USING (bucket_id = 'profile-images');
```

### **2. File Upload Utilities**
```typescript
// Utility functions for file uploads
export class StorageService {
  static async uploadDriverDocument(
    driverId: string,
    file: File,
    documentType: string
  ): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${driverId}/${documentType}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('driver-documents')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      });
    
    if (error) {
      throw new Error(`Failed to upload document: ${error.message}`);
    }
    
    return data.path;
  }
  
  static async uploadProfileImage(
    userId: string,
    file: File
  ): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/profile.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('profile-images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      });
    
    if (error) {
      throw new Error(`Failed to upload profile image: ${error.message}`);
    }
    
    // Get public URL
    const { data: urlData } = supabase.storage
      .from('profile-images')
      .getPublicUrl(data.path);
    
    return urlData.publicUrl;
  }
}
```

## ⚡ **Edge Functions Setup**

### **1. Edge Functions Structure**
```bash
# Create edge functions
supabase functions new calculate-fare
supabase functions new process-payment
supabase functions new send-notification
supabase functions new driver-matching

# Edge functions directory structure
supabase/functions/
├── calculate-fare/
│   └── index.ts
├── process-payment/
│   └── index.ts
├── send-notification/
│   └── index.ts
├── driver-matching/
│   └── index.ts
└── _shared/
    ├── cors.ts
    ├── supabase.ts
    └── types.ts
```

### **2. Sample Edge Function**
```typescript
// supabase/functions/calculate-fare/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface FareRequest {
  pickup: { latitude: number; longitude: number };
  destination: { latitude: number; longitude: number };
  service_type: string;
  surge_multiplier?: number;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { pickup, destination, service_type, surge_multiplier = 1.0 }: FareRequest = await req.json();
    
    // Calculate distance using Haversine formula
    const distance = calculateDistance(pickup, destination);
    
    // Get fare estimate from database function
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    const { data, error } = await supabase
      .rpc('calculate_fare_estimate', {
        distance_km: distance,
        service_type,
        surge_multiplier
      });
    
    if (error) {
      throw error;
    }
    
    return new Response(
      JSON.stringify({
        estimated_fare: data,
        distance_km: distance,
        surge_multiplier
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});

function calculateDistance(
  point1: { latitude: number; longitude: number },
  point2: { latitude: number; longitude: number }
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
  const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
```

### **3. Deploy Edge Functions**
```bash
# Deploy all functions
supabase functions deploy

# Deploy specific function
supabase functions deploy calculate-fare

# Set environment variables for functions
supabase secrets set GOOGLE_MAPS_API_KEY=your_key
supabase secrets set TWILIO_ACCOUNT_SID=your_sid
supabase secrets set TWILIO_AUTH_TOKEN=your_token

# Test function locally
supabase functions serve calculate-fare

# Test with curl
curl -X POST http://localhost:54321/functions/v1/calculate-fare \
  -H "Content-Type: application/json" \
  -d '{
    "pickup": {"latitude": -17.8252, "longitude": 31.0335},
    "destination": {"latitude": -17.8145, "longitude": 31.0974},
    "service_type": "movers_ride"
  }'
```

This comprehensive Supabase setup provides a robust foundation for the Taxicab platform's backend infrastructure with proper security, scalability, and development workflow integration.
