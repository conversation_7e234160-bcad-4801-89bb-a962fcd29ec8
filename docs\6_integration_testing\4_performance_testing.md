# 4. Performance Testing

## ⚡ **Performance Testing Overview**

Performance testing for the Taxicab platform ensures the system can handle expected user loads while maintaining acceptable response times, throughput, and resource utilization. This includes load testing, stress testing, and performance monitoring across all components.

### **Performance Testing Principles**
- **Realistic Load Simulation**: Test with realistic user patterns and data volumes
- **End-to-End Performance**: Measure performance across the entire system
- **Scalability Validation**: Ensure system scales with increasing load
- **Resource Optimization**: Identify and resolve performance bottlenecks
- **Continuous Monitoring**: Implement ongoing performance monitoring

## 📊 **Performance Requirements and SLAs**

### **1. Performance Targets**
```yaml
# Performance SLA Requirements

## Response Time Targets
web_dashboard:
  page_load: "< 3 seconds"
  api_response: "< 500ms"
  real_time_updates: "< 2 seconds"

mobile_apps:
  app_launch: "< 3 seconds"
  screen_navigation: "< 1 second"
  ride_booking: "< 30 seconds"
  map_loading: "< 5 seconds"

api_endpoints:
  authentication: "< 1 second"
  ride_creation: "< 2 seconds"
  driver_matching: "< 5 seconds"
  fare_calculation: "< 1 second"

## Throughput Targets
concurrent_users:
  passengers: 1000
  drivers: 500
  admins: 50

requests_per_second:
  ride_requests: 100
  location_updates: 500
  api_calls: 1000

## Availability Targets
uptime: "99.9%"
error_rate: "< 0.1%"
data_loss: "0%"

## Resource Utilization
cpu_usage: "< 70%"
memory_usage: "< 80%"
database_connections: "< 80% of pool"
```

### **2. Load Testing Configuration**
```typescript
// tests/performance/config/loadTestConfig.ts
export interface LoadTestConfig {
  baseUrl: string;
  duration: string;
  rampUpTime: string;
  users: {
    passengers: number;
    drivers: number;
    admins: number;
  };
  scenarios: LoadTestScenario[];
}

export interface LoadTestScenario {
  name: string;
  weight: number;
  userType: 'passenger' | 'driver' | 'admin';
  actions: LoadTestAction[];
}

export interface LoadTestAction {
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  payload?: any;
  thinkTime: number;
  expectedResponseTime: number;
}

export const loadTestConfig: LoadTestConfig = {
  baseUrl: process.env.LOAD_TEST_URL || 'https://api-staging.taxicab.co.zw',
  duration: '10m',
  rampUpTime: '2m',
  users: {
    passengers: 800,
    drivers: 400,
    admins: 20,
  },
  scenarios: [
    {
      name: 'passenger_ride_booking',
      weight: 60,
      userType: 'passenger',
      actions: [
        {
          name: 'login',
          endpoint: '/auth/login',
          method: 'POST',
          payload: { phone: '${phone}', otp: '123456' },
          thinkTime: 2000,
          expectedResponseTime: 1000,
        },
        {
          name: 'get_fare_estimate',
          endpoint: '/rides/fare-estimate',
          method: 'POST',
          payload: {
            pickup: { lat: -17.8252, lng: 31.0335 },
            destination: { lat: -17.8145, lng: 31.0974 },
            serviceType: 'movers_ride',
          },
          thinkTime: 5000,
          expectedResponseTime: 1000,
        },
        {
          name: 'create_ride',
          endpoint: '/rides',
          method: 'POST',
          payload: {
            pickup_address: 'Test Pickup',
            destination_address: 'Test Destination',
            service_type: 'movers_ride',
          },
          thinkTime: 3000,
          expectedResponseTime: 2000,
        },
        {
          name: 'track_ride',
          endpoint: '/rides/${rideId}',
          method: 'GET',
          thinkTime: 10000,
          expectedResponseTime: 500,
        },
      ],
    },
    {
      name: 'driver_location_updates',
      weight: 30,
      userType: 'driver',
      actions: [
        {
          name: 'login',
          endpoint: '/auth/login',
          method: 'POST',
          payload: { phone: '${phone}', otp: '123456' },
          thinkTime: 1000,
          expectedResponseTime: 1000,
        },
        {
          name: 'go_online',
          endpoint: '/drivers/status',
          method: 'PUT',
          payload: { is_online: true },
          thinkTime: 2000,
          expectedResponseTime: 500,
        },
        {
          name: 'update_location',
          endpoint: '/drivers/location',
          method: 'POST',
          payload: {
            coordinates: { lat: -17.8252, lng: 31.0335 },
            heading: 45,
            speed: 30,
          },
          thinkTime: 5000,
          expectedResponseTime: 300,
        },
      ],
    },
    {
      name: 'admin_monitoring',
      weight: 10,
      userType: 'admin',
      actions: [
        {
          name: 'login',
          endpoint: '/auth/login',
          method: 'POST',
          payload: { phone: '${adminPhone}', otp: '123456' },
          thinkTime: 3000,
          expectedResponseTime: 1000,
        },
        {
          name: 'get_dashboard_stats',
          endpoint: '/admin/dashboard/stats',
          method: 'GET',
          thinkTime: 10000,
          expectedResponseTime: 2000,
        },
        {
          name: 'get_active_rides',
          endpoint: '/admin/rides/active',
          method: 'GET',
          thinkTime: 15000,
          expectedResponseTime: 1500,
        },
      ],
    },
  ],
};
```

## 🔧 **K6 Load Testing Implementation**

### **1. K6 Test Scripts**
```javascript
// tests/performance/k6/rideBookingLoad.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const rideBookingTime = new Trend('ride_booking_duration');
const driverMatchingTime = new Trend('driver_matching_duration');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.01'],    // Error rate under 1%
    errors: ['rate<0.01'],
    ride_booking_duration: ['p(95)<30000'], // 95% of bookings under 30s
  },
};

// Test data
const passengers = [
  { phone: '+263771234567', name: 'Test User 1' },
  { phone: '+263771234568', name: 'Test User 2' },
  { phone: '+263771234569', name: 'Test User 3' },
];

const locations = [
  {
    pickup: { lat: -17.8252, lng: 31.0335, address: 'Harare CBD' },
    destination: { lat: -17.8145, lng: 31.0974, address: 'Borrowdale' },
  },
  {
    pickup: { lat: -17.8352, lng: 31.0435, address: 'Avondale' },
    destination: { lat: -17.8245, lng: 31.0874, address: 'Eastlea' },
  },
];

export default function () {
  const passenger = passengers[Math.floor(Math.random() * passengers.length)];
  const location = locations[Math.floor(Math.random() * locations.length)];
  
  // Step 1: Authentication
  const authResponse = http.post(`${__ENV.BASE_URL}/auth/verify`, {
    phone: passenger.phone,
    token: '123456',
  });
  
  const authSuccess = check(authResponse, {
    'auth status is 200': (r) => r.status === 200,
    'auth response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  if (!authSuccess) {
    errorRate.add(1);
    return;
  }
  
  const authToken = authResponse.json('access_token');
  const headers = { Authorization: `Bearer ${authToken}` };
  
  sleep(2); // Think time
  
  // Step 2: Get fare estimate
  const fareEstimateStart = Date.now();
  const fareResponse = http.post(
    `${__ENV.BASE_URL}/rides/fare-estimate`,
    JSON.stringify({
      pickup: location.pickup,
      destination: location.destination,
      service_type: 'movers_ride',
    }),
    { headers: { ...headers, 'Content-Type': 'application/json' } }
  );
  
  const fareSuccess = check(fareResponse, {
    'fare estimate status is 200': (r) => r.status === 200,
    'fare estimate response time < 1s': (r) => r.timings.duration < 1000,
    'fare estimate has amount': (r) => r.json('estimated_fare') > 0,
  });
  
  if (!fareSuccess) {
    errorRate.add(1);
    return;
  }
  
  sleep(3); // Think time
  
  // Step 3: Create ride
  const rideBookingStart = Date.now();
  const rideResponse = http.post(
    `${__ENV.BASE_URL}/rides`,
    JSON.stringify({
      pickup_address: location.pickup.address,
      pickup_coordinates: `POINT(${location.pickup.lng} ${location.pickup.lat})`,
      destination_address: location.destination.address,
      destination_coordinates: `POINT(${location.destination.lng} ${location.destination.lat})`,
      service_type: 'movers_ride',
    }),
    { headers: { ...headers, 'Content-Type': 'application/json' } }
  );
  
  const rideSuccess = check(rideResponse, {
    'ride creation status is 200': (r) => r.status === 200,
    'ride creation response time < 2s': (r) => r.timings.duration < 2000,
    'ride has ID': (r) => r.json('id') !== undefined,
  });
  
  if (!rideSuccess) {
    errorRate.add(1);
    return;
  }
  
  const rideId = rideResponse.json('id');
  const rideBookingEnd = Date.now();
  rideBookingTime.add(rideBookingEnd - rideBookingStart);
  
  // Step 4: Poll for driver matching
  const driverMatchingStart = Date.now();
  let driverFound = false;
  let attempts = 0;
  const maxAttempts = 10;
  
  while (!driverFound && attempts < maxAttempts) {
    sleep(2);
    
    const statusResponse = http.get(
      `${__ENV.BASE_URL}/rides/${rideId}`,
      { headers }
    );
    
    check(statusResponse, {
      'ride status check is 200': (r) => r.status === 200,
    });
    
    const rideStatus = statusResponse.json('status');
    if (rideStatus === 'accepted') {
      driverFound = true;
      const driverMatchingEnd = Date.now();
      driverMatchingTime.add(driverMatchingEnd - driverMatchingStart);
    }
    
    attempts++;
  }
  
  if (!driverFound) {
    console.log(`Driver matching failed for ride ${rideId}`);
    errorRate.add(1);
  }
  
  sleep(5); // Think time before next iteration
}

// Setup function
export function setup() {
  console.log('Starting load test...');
  console.log(`Base URL: ${__ENV.BASE_URL}`);
  console.log(`Test duration: ${options.stages.reduce((total, stage) => total + parseInt(stage.duration), 0)} minutes`);
}

// Teardown function
export function teardown(data) {
  console.log('Load test completed');
}
```

### **2. Real-Time Performance Testing**
```javascript
// tests/performance/k6/realtimeLoad.js
import ws from 'k6/ws';
import { check } from 'k6';
import { Rate, Counter } from 'k6/metrics';

const wsConnectionErrors = new Rate('ws_connection_errors');
const wsMessagesSent = new Counter('ws_messages_sent');
const wsMessagesReceived = new Counter('ws_messages_received');

export const options = {
  stages: [
    { duration: '1m', target: 50 },   // 50 concurrent WebSocket connections
    { duration: '3m', target: 50 },   // Hold for 3 minutes
    { duration: '1m', target: 100 },  // Scale to 100 connections
    { duration: '3m', target: 100 },  // Hold for 3 minutes
    { duration: '1m', target: 0 },    // Ramp down
  ],
};

export default function () {
  const url = `${__ENV.WS_URL}/realtime/v1/websocket?apikey=${__ENV.SUPABASE_ANON_KEY}`;
  
  const response = ws.connect(url, {}, function (socket) {
    socket.on('open', () => {
      console.log('WebSocket connection opened');
      
      // Subscribe to ride updates
      socket.send(JSON.stringify({
        topic: 'realtime:public:rides',
        event: 'phx_join',
        payload: {},
        ref: '1',
      }));
      
      // Subscribe to driver locations
      socket.send(JSON.stringify({
        topic: 'realtime:public:driver_locations',
        event: 'phx_join',
        payload: {},
        ref: '2',
      }));
      
      wsMessagesSent.add(2);
    });
    
    socket.on('message', (data) => {
      wsMessagesReceived.add(1);
      
      const message = JSON.parse(data);
      check(message, {
        'message has topic': (msg) => msg.topic !== undefined,
        'message has event': (msg) => msg.event !== undefined,
      });
    });
    
    socket.on('error', (e) => {
      console.log('WebSocket error:', e);
      wsConnectionErrors.add(1);
    });
    
    // Simulate driver location updates
    const driverId = `driver-${__VU}`;
    setInterval(() => {
      const locationUpdate = {
        topic: 'realtime:public:driver_locations',
        event: 'INSERT',
        payload: {
          driver_id: driverId,
          coordinates: `POINT(${31.0335 + Math.random() * 0.01} ${-17.8252 + Math.random() * 0.01})`,
          timestamp: new Date().toISOString(),
        },
        ref: Math.random().toString(),
      };
      
      socket.send(JSON.stringify(locationUpdate));
      wsMessagesSent.add(1);
    }, 5000); // Every 5 seconds
    
    // Keep connection alive for test duration
    socket.setTimeout(() => {
      socket.close();
    }, 30000); // 30 seconds per connection
  });
  
  check(response, {
    'WebSocket connection successful': (r) => r && r.status === 101,
  });
}
```

## 📈 **Performance Monitoring and Analysis**

### **1. Performance Monitoring Setup**
```typescript
// monitoring/performance/performanceMonitor.ts
export class PerformanceMonitor {
  private metrics: Map<string, any[]> = new Map();
  private alerts: PerformanceAlert[] = [];

  // Core Web Vitals monitoring
  monitorWebVitals() {
    if (typeof window !== 'undefined') {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          this.recordMetric('lcp', entry.startTime);
          
          if (entry.startTime > 2500) {
            this.triggerAlert('LCP', entry.startTime, 'LCP exceeds 2.5s threshold');
          }
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          this.recordMetric('fid', entry.processingStart - entry.startTime);
          
          if (entry.processingStart - entry.startTime > 100) {
            this.triggerAlert('FID', entry.processingStart - entry.startTime, 'FID exceeds 100ms threshold');
          }
        }
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      new PerformanceObserver((entryList) => {
        let clsValue = 0;
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        
        this.recordMetric('cls', clsValue);
        
        if (clsValue > 0.1) {
          this.triggerAlert('CLS', clsValue, 'CLS exceeds 0.1 threshold');
        }
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  // API response time monitoring
  monitorApiPerformance(endpoint: string, startTime: number, endTime: number) {
    const duration = endTime - startTime;
    this.recordMetric(`api_${endpoint}`, duration);
    
    // Alert if API response time exceeds threshold
    const threshold = this.getApiThreshold(endpoint);
    if (duration > threshold) {
      this.triggerAlert('API_SLOW', duration, `${endpoint} response time exceeds ${threshold}ms`);
    }
  }

  // Database query performance monitoring
  monitorDatabasePerformance(query: string, duration: number) {
    this.recordMetric('db_query', duration);
    
    if (duration > 1000) { // 1 second threshold
      this.triggerAlert('DB_SLOW', duration, `Slow database query: ${query}`);
    }
  }

  // Real-time performance monitoring
  monitorRealtimePerformance(event: string, latency: number) {
    this.recordMetric('realtime_latency', latency);
    
    if (latency > 2000) { // 2 second threshold
      this.triggerAlert('REALTIME_SLOW', latency, `Real-time event ${event} has high latency`);
    }
  }

  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    this.metrics.get(name)!.push({
      value,
      timestamp: Date.now(),
    });
    
    // Keep only last 1000 measurements
    const measurements = this.metrics.get(name)!;
    if (measurements.length > 1000) {
      measurements.shift();
    }
  }

  private triggerAlert(type: string, value: number, message: string) {
    const alert: PerformanceAlert = {
      type,
      value,
      message,
      timestamp: Date.now(),
      severity: this.getAlertSeverity(type, value),
    };
    
    this.alerts.push(alert);
    
    // Send alert to monitoring system
    this.sendAlert(alert);
  }

  private getApiThreshold(endpoint: string): number {
    const thresholds: Record<string, number> = {
      '/auth/verify': 1000,
      '/rides': 2000,
      '/rides/fare-estimate': 1000,
      '/drivers/location': 300,
      default: 500,
    };
    
    return thresholds[endpoint] || thresholds.default;
  }

  private getAlertSeverity(type: string, value: number): 'low' | 'medium' | 'high' | 'critical' {
    switch (type) {
      case 'LCP':
        if (value > 4000) return 'critical';
        if (value > 3000) return 'high';
        return 'medium';
      
      case 'FID':
        if (value > 300) return 'critical';
        if (value > 200) return 'high';
        return 'medium';
      
      case 'API_SLOW':
        if (value > 5000) return 'critical';
        if (value > 3000) return 'high';
        return 'medium';
      
      default:
        return 'medium';
    }
  }

  private async sendAlert(alert: PerformanceAlert) {
    // Send to monitoring service (e.g., DataDog, New Relic)
    try {
      await fetch('/api/monitoring/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(alert),
      });
    } catch (error) {
      console.error('Failed to send performance alert:', error);
    }
  }

  // Get performance summary
  getPerformanceSummary(): PerformanceSummary {
    const summary: PerformanceSummary = {
      webVitals: {
        lcp: this.calculatePercentile('lcp', 75),
        fid: this.calculatePercentile('fid', 75),
        cls: this.calculatePercentile('cls', 75),
      },
      apiPerformance: {
        averageResponseTime: this.calculateAverage('api_*'),
        p95ResponseTime: this.calculatePercentile('api_*', 95),
      },
      realtimePerformance: {
        averageLatency: this.calculateAverage('realtime_latency'),
        p95Latency: this.calculatePercentile('realtime_latency', 95),
      },
      alerts: this.alerts.slice(-10), // Last 10 alerts
    };
    
    return summary;
  }

  private calculatePercentile(metricName: string, percentile: number): number {
    const measurements = this.metrics.get(metricName) || [];
    if (measurements.length === 0) return 0;
    
    const values = measurements.map(m => m.value).sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * values.length) - 1;
    return values[index] || 0;
  }

  private calculateAverage(metricPattern: string): number {
    let totalValue = 0;
    let totalCount = 0;
    
    for (const [name, measurements] of this.metrics.entries()) {
      if (metricPattern.includes('*') ? name.startsWith(metricPattern.replace('*', '')) : name === metricPattern) {
        totalValue += measurements.reduce((sum, m) => sum + m.value, 0);
        totalCount += measurements.length;
      }
    }
    
    return totalCount > 0 ? totalValue / totalCount : 0;
  }
}

interface PerformanceAlert {
  type: string;
  value: number;
  message: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface PerformanceSummary {
  webVitals: {
    lcp: number;
    fid: number;
    cls: number;
  };
  apiPerformance: {
    averageResponseTime: number;
    p95ResponseTime: number;
  };
  realtimePerformance: {
    averageLatency: number;
    p95Latency: number;
  };
  alerts: PerformanceAlert[];
}
```

This comprehensive performance testing framework ensures the Taxicab platform can handle expected loads while maintaining excellent performance across all user interfaces and system components with proper monitoring, alerting, and optimization capabilities.
