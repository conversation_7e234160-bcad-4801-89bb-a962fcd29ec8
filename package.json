{"name": "taxicab-platform", "version": "1.0.0", "description": "Complete ride-hailing platform for Zimbabwe with taxi association integration", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:ci": "turbo run test:ci", "test:e2e": "turbo run test:e2e", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=!@taxicab/docs && changeset publish", "dev:admin": "turbo run dev --filter=@taxicab/admin-dashboard", "dev:passenger": "turbo run dev --filter=@taxicab/passenger-app", "dev:driver": "turbo run dev --filter=@taxicab/driver-app", "build:admin": "turbo run build --filter=@taxicab/admin-dashboard", "build:passenger": "turbo run build --filter=@taxicab/passenger-app", "build:driver": "turbo run build --filter=@taxicab/driver-app", "db:migrate": "supabase db push", "db:reset": "supabase db reset", "db:seed": "supabase db seed", "db:types": "supabase gen types typescript --local > packages/shared-types/src/database.types.ts", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@turbo/gen": "^1.10.12", "@types/node": "^20.5.2", "eslint": "^8.48.0", "prettier": "^3.0.0", "supabase": "^1.100.0", "turbo": "^1.10.12", "typescript": "^5.2.2"}, "packageManager": "npm@9.8.1", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/moversfinder/taxicab.git"}, "author": "Taxicab Platform Team", "license": "MIT", "keywords": ["ride-hailing", "taxi", "zimbabwe", "react", "react-native", "supabase", "typescript", "mobile-app", "web-app"], "homepage": "https://github.com/moversfinder/taxicab#readme", "bugs": {"url": "https://github.com/moversfinder/taxicab/issues"}}