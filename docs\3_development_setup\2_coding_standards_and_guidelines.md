# 2. Coding Standards and Guidelines

## 📋 **Coding Standards Overview**

This document establishes comprehensive coding standards and guidelines for the Taxicab platform development team. These standards ensure code consistency, maintainability, and quality across all applications and team members.

### **Standards Objectives**
- **Consistency**: Uniform code style across all team members and applications
- **Readability**: Clear, self-documenting code that's easy to understand
- **Maintainability**: Code that's easy to modify, extend, and debug
- **Quality**: High-quality code with proper error handling and testing
- **Collaboration**: Standards that facilitate effective team collaboration

## 🎯 **General Coding Principles**

### **1. Code Organization Principles**
- **Single Responsibility**: Each function/class should have one clear purpose
- **DRY (Don't Repeat Yourself)**: Avoid code duplication through reusable components
- **KISS (Keep It Simple, Stupid)**: Write simple, straightforward code
- **YAGNI (You Aren't Gonna Need It)**: Don't implement features until they're needed
- **Separation of Concerns**: Separate business logic, UI, and data access

### **2. Naming Conventions**

#### **Variables and Functions**
```typescript
// Use camelCase for variables and functions
const userProfile = getUserProfile();
const isDriverOnline = checkDriverStatus();

// Use descriptive names
// ❌ Bad
const d = new Date();
const u = users.filter(x => x.active);

// ✅ Good
const currentDate = new Date();
const activeUsers = users.filter(user => user.isActive);

// Boolean variables should be questions
const isLoading = true;
const hasPermission = false;
const canEditProfile = true;
```

#### **Constants and Enums**
```typescript
// Use SCREAMING_SNAKE_CASE for constants
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.taxicab.co.zw';
const DEFAULT_TIMEOUT_MS = 5000;

// Use PascalCase for enums
enum RideStatus {
  REQUESTED = 'requested',
  ACCEPTED = 'accepted',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

enum UserRole {
  PASSENGER = 'passenger',
  DRIVER = 'driver',
  ADMIN = 'admin',
  CORPORATE_ADMIN = 'corporate_admin'
}
```

#### **Types and Interfaces**
```typescript
// Use PascalCase for types and interfaces
interface UserProfile {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  role: UserRole;
}

type RideRequest = {
  pickupLocation: Coordinates;
  destinationLocation: Coordinates;
  serviceType: ServiceType;
  scheduledTime?: Date;
};

// Use descriptive generic type names
interface ApiResponse<TData> {
  data: TData;
  success: boolean;
  message?: string;
}
```

## 🏗️ **TypeScript Standards**

### **1. Type Definitions**
```typescript
// Always define explicit types for function parameters and return values
function calculateFare(
  distance: number,
  serviceType: ServiceType,
  surgeMultiplier: number = 1.0
): number {
  const baseFare = getBaseFare(serviceType);
  return (baseFare + (distance * RATE_PER_KM)) * surgeMultiplier;
}

// Use union types for specific values
type PaymentMethod = 'cash' | 'ecocash' | 'onemoney' | 'card';
type RideStatus = 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';

// Use optional properties appropriately
interface CreateRideRequest {
  pickupAddress: string;
  destinationAddress: string;
  serviceType: ServiceType;
  scheduledTime?: Date; // Optional for immediate rides
  passengerNotes?: string; // Optional additional information
}
```

### **2. Error Handling Standards**
```typescript
// Create custom error classes for different error types
class ValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public endpoint: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Use Result pattern for operations that can fail
type Result<T, E = Error> = {
  success: true;
  data: T;
} | {
  success: false;
  error: E;
};

async function fetchUserProfile(userId: string): Promise<Result<UserProfile>> {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) {
      return { success: false, error: new ApiError(error.message, 400, 'users') };
    }
    
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

### **3. Async/Await Standards**
```typescript
// Always use async/await instead of .then()/.catch()
// ❌ Bad
function getRideHistory(userId: string) {
  return supabase
    .from('rides')
    .select('*')
    .eq('passenger_id', userId)
    .then(response => response.data)
    .catch(error => {
      console.error(error);
      return [];
    });
}

// ✅ Good
async function getRideHistory(userId: string): Promise<Ride[]> {
  try {
    const { data, error } = await supabase
      .from('rides')
      .select('*')
      .eq('passenger_id', userId);
    
    if (error) {
      throw new ApiError(error.message, 400, 'rides');
    }
    
    return data || [];
  } catch (error) {
    console.error('Failed to fetch ride history:', error);
    throw error;
  }
}
```

## ⚛️ **React/React Native Standards**

### **1. Component Structure**
```typescript
// Use functional components with TypeScript
interface RideCardProps {
  ride: Ride;
  onSelect: (rideId: string) => void;
  isSelected?: boolean;
}

export const RideCard: React.FC<RideCardProps> = ({
  ride,
  onSelect,
  isSelected = false
}) => {
  // Hooks at the top
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  
  // Event handlers
  const handleCardPress = useCallback(() => {
    onSelect(ride.id);
  }, [ride.id, onSelect]);
  
  // Early returns for loading/error states
  if (!ride) {
    return <RideCardSkeleton />;
  }
  
  // Main render
  return (
    <Card 
      className={cn("p-4 cursor-pointer", isSelected && "ring-2 ring-primary")}
      onClick={handleCardPress}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-semibold">{ride.destinationAddress}</h3>
          <p className="text-sm text-muted-foreground">
            {formatDate(ride.createdAt)}
          </p>
        </div>
        <Badge variant={getRideStatusVariant(ride.status)}>
          {ride.status}
        </Badge>
      </div>
    </Card>
  );
};
```

### **2. Custom Hooks Standards**
```typescript
// Create reusable custom hooks for common functionality
interface UseRideTrackingOptions {
  rideId: string;
  enabled?: boolean;
}

interface UseRideTrackingReturn {
  ride: Ride | null;
  driverLocation: Coordinates | null;
  isLoading: boolean;
  error: Error | null;
}

export function useRideTracking({
  rideId,
  enabled = true
}: UseRideTrackingOptions): UseRideTrackingReturn {
  const [ride, setRide] = useState<Ride | null>(null);
  const [driverLocation, setDriverLocation] = useState<Coordinates | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    if (!enabled || !rideId) return;
    
    const subscription = supabase
      .channel(`ride:${rideId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'rides',
        filter: `id=eq.${rideId}`
      }, (payload) => {
        setRide(payload.new as Ride);
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [rideId, enabled]);
  
  return { ride, driverLocation, isLoading, error };
}
```

### **3. State Management Standards**
```typescript
// Use Zustand for global state management
interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (phone: string, otp: string) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  clearError: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>((set, get) => ({
  // State
  user: null,
  isLoading: false,
  error: null,
  
  // Actions
  login: async (phone: string, otp: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        phone,
        token: otp,
        type: 'sms'
      });
      
      if (error) throw error;
      
      set({ user: data.user, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },
  
  logout: async () => {
    await supabase.auth.signOut();
    set({ user: null, error: null });
  },
  
  updateProfile: async (updates: Partial<User>) => {
    const { user } = get();
    if (!user) return;
    
    try {
      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id);
      
      if (error) throw error;
      
      set({ user: { ...user, ...updates } });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },
  
  clearError: () => set({ error: null })
}));
```

## 🎨 **Styling Standards (Tailwind CSS + Shadcn UI)**

### **1. Component Styling**
```typescript
// Use cn() utility for conditional classes
import { cn } from '@/lib/utils';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  className?: string;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  className,
  children,
  ...props
}) => {
  return (
    <button
      className={cn(
        // Base styles
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        
        // Variant styles
        {
          'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'primary',
          'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
          'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',
        },
        
        // Size styles
        {
          'h-8 px-3 text-sm': size === 'sm',
          'h-10 px-4': size === 'md',
          'h-12 px-6 text-lg': size === 'lg',
        },
        
        className
      )}
      disabled={isLoading}
      {...props}
    >
      {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {children}
    </button>
  );
};
```

### **2. Responsive Design Standards**
```typescript
// Use mobile-first responsive design
const RideCard = () => {
  return (
    <div className={cn(
      // Mobile styles (default)
      'p-4 rounded-lg border bg-card',
      
      // Tablet styles
      'md:p-6',
      
      // Desktop styles
      'lg:p-8 lg:hover:shadow-lg lg:transition-shadow'
    )}>
      <div className={cn(
        // Mobile: stack vertically
        'flex flex-col space-y-2',
        
        // Tablet and up: horizontal layout
        'md:flex-row md:items-center md:justify-between md:space-y-0'
      )}>
        {/* Content */}
      </div>
    </div>
  );
};
```

## 📝 **Documentation Standards**

### **1. Code Comments**
```typescript
/**
 * Calculates the fare for a ride based on distance, service type, and surge pricing.
 * 
 * @param distance - Distance in kilometers
 * @param serviceType - Type of service (lite, standard, executive, xl)
 * @param surgeMultiplier - Surge pricing multiplier (default: 1.0)
 * @returns The calculated fare in USD
 * 
 * @example
 * ```typescript
 * const fare = calculateFare(5.2, 'standard', 1.5);
 * console.log(fare); // 12.60
 * ```
 */
function calculateFare(
  distance: number,
  serviceType: ServiceType,
  surgeMultiplier: number = 1.0
): number {
  // Get base fare for the service type
  const baseFare = SERVICE_BASE_FARES[serviceType];
  
  // Calculate distance-based fare
  const distanceFare = distance * RATE_PER_KM;
  
  // Apply surge pricing if applicable
  const totalFare = (baseFare + distanceFare) * surgeMultiplier;
  
  return Math.round(totalFare * 100) / 100; // Round to 2 decimal places
}
```

### **2. README Standards**
```markdown
# Component/Module Name

Brief description of what this component/module does.

## Usage

```typescript
import { ComponentName } from './ComponentName';

const Example = () => {
  return (
    <ComponentName
      prop1="value1"
      prop2={value2}
      onAction={handleAction}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| prop1 | string | - | Description of prop1 |
| prop2 | number | 0 | Description of prop2 |
| onAction | function | - | Callback when action occurs |

## Examples

### Basic Usage
[Example code]

### Advanced Usage
[Example code]
```

## ✅ **Code Quality Checklist**

### **Before Committing Code**
- [ ] Code follows naming conventions
- [ ] All functions have proper TypeScript types
- [ ] Error handling is implemented
- [ ] Code is properly commented
- [ ] No console.log statements in production code
- [ ] All imports are organized and unused imports removed
- [ ] Code passes ESLint and Prettier checks
- [ ] Component props are properly typed
- [ ] Custom hooks follow naming conventions (use*)
- [ ] Responsive design is implemented where applicable

### **Code Review Checklist**
- [ ] Code is readable and self-documenting
- [ ] Business logic is separated from UI logic
- [ ] Error cases are handled appropriately
- [ ] Performance considerations are addressed
- [ ] Security best practices are followed
- [ ] Code follows established patterns
- [ ] Tests are included for new functionality
- [ ] Documentation is updated if needed

These coding standards ensure consistent, maintainable, and high-quality code across the entire Taxicab platform development team.
