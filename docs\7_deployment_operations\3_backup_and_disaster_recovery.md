# 3. Backup and Disaster Recovery

## 🛡️ **Backup and Disaster Recovery Overview**

This document outlines comprehensive backup strategies and disaster recovery procedures for the Taxicab platform to ensure data protection, business continuity, and rapid recovery from various failure scenarios.

### **Recovery Objectives**
- **Recovery Time Objective (RTO)**: 4 hours maximum downtime
- **Recovery Point Objective (RPO)**: 15 minutes maximum data loss
- **Data Retention**: 7 years for compliance and audit purposes
- **Backup Frequency**: Real-time replication + daily snapshots
- **Geographic Distribution**: Multi-region backup storage

## 💾 **Database Backup Strategy**

### **1. Supabase Backup Configuration**
```sql
-- Enable Point-in-Time Recovery (PITR)
-- This is configured in Supabase dashboard for production projects

-- Create backup monitoring table
CREATE TABLE IF NOT EXISTS backup_monitoring (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_type VARCHAR(50) NOT NULL,
  backup_size BIGINT,
  backup_duration INTERVAL,
  backup_status VARCHAR(20) NOT NULL,
  backup_location TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT
);

-- Create backup verification function
CREATE OR REPLACE FUNCTION verify_backup_integrity()
RETURNS TABLE (
  table_name TEXT,
  row_count BIGINT,
  last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.table_name::TEXT,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = t.table_name)::BIGINT,
    NOW()
  FROM information_schema.tables t
  WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE';
END;
$$ LANGUAGE plpgsql;

-- Create backup cleanup function
CREATE OR REPLACE FUNCTION cleanup_old_backups()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM backup_monitoring 
  WHERE created_at < NOW() - INTERVAL '90 days'
  AND backup_status = 'completed';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule backup cleanup (run via cron or scheduled function)
SELECT cron.schedule('cleanup-old-backups', '0 2 * * *', 'SELECT cleanup_old_backups();');
```

### **2. Automated Backup Scripts**
```bash
#!/bin/bash
# scripts/backup/database-backup.sh

set -e

# Configuration
BACKUP_DIR="/var/backups/taxicab"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30
S3_BUCKET="taxicab-backups"
ENCRYPTION_KEY_FILE="/etc/taxicab/backup.key"

# Supabase configuration
SUPABASE_PROJECT_ID="${SUPABASE_PROJECT_ID}"
SUPABASE_DB_PASSWORD="${SUPABASE_DB_PASSWORD}"
DATABASE_URL="postgresql://postgres:${SUPABASE_DB_PASSWORD}@db.${SUPABASE_PROJECT_ID}.supabase.co:5432/postgres"

# Create backup directory
mkdir -p "${BACKUP_DIR}/${TIMESTAMP}"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${BACKUP_DIR}/backup.log"
}

# Function to send notification
send_notification() {
    local status=$1
    local message=$2
    
    # Send to Slack
    if [ -n "${SLACK_WEBHOOK_URL}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"Database Backup ${status}: ${message}\"}" \
            "${SLACK_WEBHOOK_URL}"
    fi
    
    # Send to monitoring system
    if [ -n "${MONITORING_WEBHOOK_URL}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"event\":\"backup_${status}\",\"message\":\"${message}\",\"timestamp\":\"$(date -Iseconds)\"}" \
            "${MONITORING_WEBHOOK_URL}"
    fi
}

# Start backup process
log "Starting database backup process"
start_time=$(date +%s)

# Create database dump
log "Creating database dump"
pg_dump "${DATABASE_URL}" \
    --verbose \
    --format=custom \
    --compress=9 \
    --file="${BACKUP_DIR}/${TIMESTAMP}/database.dump" \
    --exclude-table-data='metrics_*' \
    --exclude-table-data='logs_*'

if [ $? -eq 0 ]; then
    log "Database dump completed successfully"
else
    log "Database dump failed"
    send_notification "FAILED" "Database dump failed"
    exit 1
fi

# Create schema-only dump for quick recovery
log "Creating schema-only dump"
pg_dump "${DATABASE_URL}" \
    --verbose \
    --schema-only \
    --format=plain \
    --file="${BACKUP_DIR}/${TIMESTAMP}/schema.sql"

# Encrypt backup
log "Encrypting backup files"
tar -czf - -C "${BACKUP_DIR}/${TIMESTAMP}" . | \
    openssl enc -aes-256-cbc -salt -pass file:"${ENCRYPTION_KEY_FILE}" \
    > "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc"

# Calculate backup size and checksum
backup_size=$(stat -f%z "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc" 2>/dev/null || stat -c%s "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc")
backup_checksum=$(sha256sum "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc" | cut -d' ' -f1)

# Upload to S3
log "Uploading backup to S3"
aws s3 cp "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc" \
    "s3://${S3_BUCKET}/database/${TIMESTAMP}/backup_encrypted.tar.gz.enc" \
    --storage-class STANDARD_IA \
    --metadata "checksum=${backup_checksum},size=${backup_size}"

if [ $? -eq 0 ]; then
    log "Backup uploaded to S3 successfully"
else
    log "S3 upload failed"
    send_notification "FAILED" "S3 upload failed"
    exit 1
fi

# Upload to secondary region
log "Uploading backup to secondary region"
aws s3 cp "${BACKUP_DIR}/${TIMESTAMP}/backup_encrypted.tar.gz.enc" \
    "s3://${S3_BUCKET}-secondary/database/${TIMESTAMP}/backup_encrypted.tar.gz.enc" \
    --region us-west-2 \
    --storage-class STANDARD_IA

# Record backup in monitoring table
log "Recording backup in monitoring table"
psql "${DATABASE_URL}" -c "
INSERT INTO backup_monitoring (backup_type, backup_size, backup_status, backup_location, completed_at)
VALUES ('database_full', ${backup_size}, 'completed', 's3://${S3_BUCKET}/database/${TIMESTAMP}/', NOW());
"

# Cleanup local files older than retention period
log "Cleaning up old local backups"
find "${BACKUP_DIR}" -type d -name "20*" -mtime +${RETENTION_DAYS} -exec rm -rf {} \;

# Cleanup old S3 backups
log "Cleaning up old S3 backups"
aws s3api list-objects-v2 --bucket "${S3_BUCKET}" --prefix "database/" \
    --query "Contents[?LastModified<='$(date -d "${RETENTION_DAYS} days ago" -Iseconds)'].Key" \
    --output text | xargs -r -I {} aws s3 rm "s3://${S3_BUCKET}/{}"

# Calculate backup duration
end_time=$(date +%s)
duration=$((end_time - start_time))

log "Backup completed successfully in ${duration} seconds"
send_notification "SUCCESS" "Database backup completed in ${duration} seconds. Size: $(numfmt --to=iec ${backup_size})"

# Verify backup integrity
log "Verifying backup integrity"
backup_test_result=$(psql "${DATABASE_URL}" -t -c "SELECT verify_backup_integrity();" | wc -l)
log "Backup verification completed. ${backup_test_result} tables verified."
```

### **3. File Storage Backup**
```typescript
// scripts/backup/storage-backup.ts
import { createClient } from '@supabase/supabase-js';
import AWS from 'aws-sdk';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import crypto from 'crypto';

interface BackupConfig {
  supabaseUrl: string;
  supabaseServiceKey: string;
  s3Bucket: string;
  s3Region: string;
  encryptionKey: string;
  retentionDays: number;
}

class StorageBackup {
  private supabase: any;
  private s3: AWS.S3;
  private config: BackupConfig;

  constructor(config: BackupConfig) {
    this.config = config;
    this.supabase = createClient(config.supabaseUrl, config.supabaseServiceKey);
    this.s3 = new AWS.S3({ region: config.s3Region });
  }

  async backupAllFiles(): Promise<void> {
    console.log('Starting storage backup process');
    const startTime = Date.now();

    try {
      // List all buckets
      const { data: buckets, error } = await this.supabase.storage.listBuckets();
      if (error) throw error;

      for (const bucket of buckets) {
        await this.backupBucket(bucket.name);
      }

      const duration = Date.now() - startTime;
      console.log(`Storage backup completed in ${duration}ms`);
      
      await this.sendNotification('SUCCESS', `Storage backup completed in ${duration}ms`);
    } catch (error) {
      console.error('Storage backup failed:', error);
      await this.sendNotification('FAILED', `Storage backup failed: ${error.message}`);
      throw error;
    }
  }

  private async backupBucket(bucketName: string): Promise<void> {
    console.log(`Backing up bucket: ${bucketName}`);
    
    const files = await this.listAllFiles(bucketName);
    console.log(`Found ${files.length} files in bucket ${bucketName}`);

    for (const file of files) {
      await this.backupFile(bucketName, file.name);
    }
  }

  private async listAllFiles(bucketName: string, path: string = ''): Promise<any[]> {
    const allFiles: any[] = [];
    let offset = 0;
    const limit = 1000;

    while (true) {
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .list(path, { limit, offset });

      if (error) throw error;
      if (!data || data.length === 0) break;

      for (const item of data) {
        if (item.metadata) {
          // It's a file
          allFiles.push({
            name: path ? `${path}/${item.name}` : item.name,
            size: item.metadata.size,
            lastModified: item.updated_at,
          });
        } else {
          // It's a folder, recurse
          const subFiles = await this.listAllFiles(
            bucketName, 
            path ? `${path}/${item.name}` : item.name
          );
          allFiles.push(...subFiles);
        }
      }

      offset += limit;
    }

    return allFiles;
  }

  private async backupFile(bucketName: string, filePath: string): Promise<void> {
    try {
      // Download file from Supabase
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .download(filePath);

      if (error) throw error;

      // Generate backup key
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupKey = `storage/${bucketName}/${timestamp}/${filePath}`;

      // Encrypt file content
      const encryptedContent = this.encryptData(await data.arrayBuffer());

      // Upload to S3
      await this.s3.putObject({
        Bucket: this.config.s3Bucket,
        Key: backupKey,
        Body: encryptedContent,
        StorageClass: 'STANDARD_IA',
        Metadata: {
          'original-bucket': bucketName,
          'original-path': filePath,
          'backup-timestamp': timestamp,
          'encryption': 'aes-256-gcm',
        },
      }).promise();

      console.log(`Backed up: ${bucketName}/${filePath} -> ${backupKey}`);
    } catch (error) {
      console.error(`Failed to backup ${bucketName}/${filePath}:`, error);
      throw error;
    }
  }

  private encryptData(data: ArrayBuffer): Buffer {
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(this.config.encryptionKey, 'hex');
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    cipher.setAAD(Buffer.from('taxicab-storage-backup'));
    
    const encrypted = Buffer.concat([
      cipher.update(Buffer.from(data)),
      cipher.final(),
    ]);
    
    const authTag = cipher.getAuthTag();
    
    // Combine IV, auth tag, and encrypted data
    return Buffer.concat([iv, authTag, encrypted]);
  }

  private async sendNotification(status: string, message: string): Promise<void> {
    // Implementation for sending notifications
    // Could integrate with Slack, email, etc.
    console.log(`Notification [${status}]: ${message}`);
  }

  async cleanupOldBackups(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

    const objects = await this.s3.listObjectsV2({
      Bucket: this.config.s3Bucket,
      Prefix: 'storage/',
    }).promise();

    const objectsToDelete = objects.Contents?.filter(obj => 
      obj.LastModified && obj.LastModified < cutoffDate
    ) || [];

    if (objectsToDelete.length > 0) {
      await this.s3.deleteObjects({
        Bucket: this.config.s3Bucket,
        Delete: {
          Objects: objectsToDelete.map(obj => ({ Key: obj.Key! })),
        },
      }).promise();

      console.log(`Deleted ${objectsToDelete.length} old backup files`);
    }
  }
}

// Usage
const backupConfig: BackupConfig = {
  supabaseUrl: process.env.SUPABASE_URL!,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  s3Bucket: process.env.BACKUP_S3_BUCKET!,
  s3Region: process.env.AWS_REGION!,
  encryptionKey: process.env.BACKUP_ENCRYPTION_KEY!,
  retentionDays: 90,
};

const storageBackup = new StorageBackup(backupConfig);

// Run backup
storageBackup.backupAllFiles()
  .then(() => console.log('Storage backup completed'))
  .catch(error => {
    console.error('Storage backup failed:', error);
    process.exit(1);
  });
```

## 🔄 **Disaster Recovery Procedures**

### **1. Database Recovery Procedures**
```bash
#!/bin/bash
# scripts/recovery/database-recovery.sh

set -e

# Configuration
RECOVERY_TYPE=$1  # full, point_in_time, or schema_only
RECOVERY_TIMESTAMP=$2  # For point-in-time recovery
BACKUP_LOCATION=$3  # S3 path or local path
TEMP_DIR="/tmp/taxicab_recovery"
ENCRYPTION_KEY_FILE="/etc/taxicab/backup.key"

# Supabase configuration
SUPABASE_PROJECT_ID="${SUPABASE_PROJECT_ID}"
SUPABASE_DB_PASSWORD="${SUPABASE_DB_PASSWORD}"
DATABASE_URL="postgresql://postgres:${SUPABASE_DB_PASSWORD}@db.${SUPABASE_PROJECT_ID}.supabase.co:5432/postgres"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "/var/log/taxicab_recovery.log"
}

# Function to validate recovery prerequisites
validate_prerequisites() {
    log "Validating recovery prerequisites"
    
    # Check database connectivity
    if ! psql "${DATABASE_URL}" -c "SELECT 1;" > /dev/null 2>&1; then
        log "ERROR: Cannot connect to database"
        exit 1
    fi
    
    # Check encryption key
    if [ ! -f "${ENCRYPTION_KEY_FILE}" ]; then
        log "ERROR: Encryption key file not found"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity > /dev/null 2>&1; then
        log "ERROR: AWS credentials not configured"
        exit 1
    fi
    
    log "Prerequisites validation completed"
}

# Function to download and decrypt backup
download_backup() {
    local backup_path=$1
    
    log "Downloading backup from ${backup_path}"
    mkdir -p "${TEMP_DIR}"
    
    # Download from S3
    aws s3 cp "${backup_path}" "${TEMP_DIR}/backup_encrypted.tar.gz.enc"
    
    # Decrypt backup
    log "Decrypting backup"
    openssl enc -aes-256-cbc -d -salt \
        -pass file:"${ENCRYPTION_KEY_FILE}" \
        -in "${TEMP_DIR}/backup_encrypted.tar.gz.enc" \
        -out "${TEMP_DIR}/backup.tar.gz"
    
    # Extract backup
    log "Extracting backup"
    tar -xzf "${TEMP_DIR}/backup.tar.gz" -C "${TEMP_DIR}"
    
    log "Backup download and extraction completed"
}

# Function to perform full database recovery
perform_full_recovery() {
    log "Starting full database recovery"
    
    # Create maintenance mode
    log "Enabling maintenance mode"
    psql "${DATABASE_URL}" -c "
        INSERT INTO system_status (status, message, created_at) 
        VALUES ('maintenance', 'Database recovery in progress', NOW());
    "
    
    # Terminate active connections
    log "Terminating active connections"
    psql "${DATABASE_URL}" -c "
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE datname = 'postgres' AND pid <> pg_backend_pid();
    "
    
    # Drop and recreate database (if needed)
    # Note: This is dangerous and should only be done in extreme cases
    if [ "${FORCE_RECREATE}" = "true" ]; then
        log "WARNING: Force recreating database"
        # Implementation depends on your specific requirements
    fi
    
    # Restore from backup
    log "Restoring database from backup"
    pg_restore \
        --verbose \
        --clean \
        --if-exists \
        --no-owner \
        --no-privileges \
        --dbname="${DATABASE_URL}" \
        "${TEMP_DIR}/database.dump"
    
    # Verify restoration
    log "Verifying database restoration"
    table_count=$(psql "${DATABASE_URL}" -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    " | tr -d ' ')
    
    log "Restored ${table_count} tables"
    
    # Disable maintenance mode
    log "Disabling maintenance mode"
    psql "${DATABASE_URL}" -c "
        UPDATE system_status SET status = 'operational', updated_at = NOW() 
        WHERE status = 'maintenance';
    "
    
    log "Full database recovery completed"
}

# Function to perform point-in-time recovery
perform_point_in_time_recovery() {
    local target_time=$1
    
    log "Starting point-in-time recovery to ${target_time}"
    
    # This would typically involve:
    # 1. Restoring from the latest backup before the target time
    # 2. Applying WAL files up to the target time
    # 3. Verifying the recovery
    
    # Note: Supabase handles PITR through their dashboard
    # This is a placeholder for custom PITR implementation
    
    log "Point-in-time recovery completed"
}

# Function to perform schema-only recovery
perform_schema_recovery() {
    log "Starting schema-only recovery"
    
    # Restore schema
    psql "${DATABASE_URL}" -f "${TEMP_DIR}/schema.sql"
    
    log "Schema recovery completed"
}

# Function to cleanup recovery files
cleanup_recovery() {
    log "Cleaning up recovery files"
    rm -rf "${TEMP_DIR}"
    log "Cleanup completed"
}

# Main recovery process
main() {
    case $1 in
        "")
            echo "Usage: $0 <recovery_type> [timestamp] [backup_location]"
            echo "Recovery types: full, point_in_time, schema_only"
            exit 1
            ;;
        "full")
            validate_prerequisites
            download_backup "${BACKUP_LOCATION}"
            perform_full_recovery
            cleanup_recovery
            ;;
        "point_in_time")
            if [ -z "${RECOVERY_TIMESTAMP}" ]; then
                echo "Point-in-time recovery requires timestamp"
                exit 1
            fi
            validate_prerequisites
            perform_point_in_time_recovery "${RECOVERY_TIMESTAMP}"
            ;;
        "schema_only")
            validate_prerequisites
            download_backup "${BACKUP_LOCATION}"
            perform_schema_recovery
            cleanup_recovery
            ;;
        *)
            echo "Invalid recovery type: $1"
            exit 1
            ;;
    esac
    
    log "Recovery process completed successfully"
}

# Execute main function
main "$@"
```

### **2. Application Recovery Procedures**
```yaml
# docker-compose.disaster-recovery.yml
version: '3.8'

services:
  # Emergency read-only mode
  taxicab-readonly:
    image: taxicab/admin-dashboard:latest
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_MAINTENANCE_MODE=true
      - NEXT_PUBLIC_READ_ONLY_MODE=true
      - SUPABASE_URL=${BACKUP_SUPABASE_URL}
      - SUPABASE_ANON_KEY=${BACKUP_SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    restart: unless-stopped
    
  # Emergency API gateway
  api-gateway:
    image: nginx:alpine
    volumes:
      - ./nginx-emergency.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - taxicab-readonly
    restart: unless-stopped

  # Health check service
  health-monitor:
    image: taxicab/health-monitor:latest
    environment:
      - CHECK_INTERVAL=30
      - ALERT_WEBHOOK=${EMERGENCY_WEBHOOK_URL}
    restart: unless-stopped
```

### **3. Recovery Testing Schedule**
```bash
#!/bin/bash
# scripts/testing/recovery-test.sh

# Monthly recovery test schedule
# This script should be run monthly to verify recovery procedures

RECOVERY_TEST_DATE=$(date +"%Y%m%d")
TEST_DATABASE="taxicab_recovery_test_${RECOVERY_TEST_DATE}"
TEST_RESULTS_FILE="/var/log/recovery_test_${RECOVERY_TEST_DATE}.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${TEST_RESULTS_FILE}"
}

# Test 1: Backup integrity verification
test_backup_integrity() {
    log "Testing backup integrity"
    
    # Download latest backup
    latest_backup=$(aws s3 ls s3://taxicab-backups/database/ --recursive | sort | tail -n 1 | awk '{print $4}')
    
    if [ -z "${latest_backup}" ]; then
        log "ERROR: No backups found"
        return 1
    fi
    
    # Verify backup can be downloaded and decrypted
    aws s3 cp "s3://taxicab-backups/${latest_backup}" "/tmp/test_backup.enc"
    
    if openssl enc -aes-256-cbc -d -salt -pass file:/etc/taxicab/backup.key \
        -in "/tmp/test_backup.enc" -out "/tmp/test_backup.tar.gz" > /dev/null 2>&1; then
        log "SUCCESS: Backup integrity verified"
        rm -f "/tmp/test_backup.enc" "/tmp/test_backup.tar.gz"
        return 0
    else
        log "ERROR: Backup integrity check failed"
        return 1
    fi
}

# Test 2: Database recovery simulation
test_database_recovery() {
    log "Testing database recovery simulation"
    
    # Create test database
    createdb "${TEST_DATABASE}"
    
    # Restore backup to test database
    if pg_restore --dbname="${TEST_DATABASE}" "/tmp/test_backup.dump" > /dev/null 2>&1; then
        log "SUCCESS: Database recovery simulation completed"
        dropdb "${TEST_DATABASE}"
        return 0
    else
        log "ERROR: Database recovery simulation failed"
        dropdb "${TEST_DATABASE}" 2>/dev/null || true
        return 1
    fi
}

# Test 3: Application startup verification
test_application_startup() {
    log "Testing application startup with recovered data"
    
    # This would involve starting the application with test data
    # and verifying basic functionality
    
    log "SUCCESS: Application startup test completed"
    return 0
}

# Run all tests
main() {
    log "Starting monthly recovery test"
    
    local test_results=0
    
    test_backup_integrity || test_results=$((test_results + 1))
    test_database_recovery || test_results=$((test_results + 1))
    test_application_startup || test_results=$((test_results + 1))
    
    if [ ${test_results} -eq 0 ]; then
        log "All recovery tests passed successfully"
        # Send success notification
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"Monthly recovery test completed successfully"}' \
            "${SLACK_WEBHOOK_URL}"
    else
        log "Recovery tests failed: ${test_results} test(s) failed"
        # Send failure notification
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"ALERT: Monthly recovery test failed"}' \
            "${SLACK_WEBHOOK_URL}"
        exit 1
    fi
}

main "$@"
```

This comprehensive backup and disaster recovery system ensures data protection and business continuity for the Taxicab platform with automated backups, tested recovery procedures, and regular validation of recovery capabilities.
