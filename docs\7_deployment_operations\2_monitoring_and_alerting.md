# 2. Monitoring and Alerting

## 📊 **Monitoring Overview**

Comprehensive monitoring and alerting for the Taxicab platform ensures high availability, performance optimization, and proactive issue resolution. This system monitors application performance, infrastructure health, business metrics, and user experience across all platform components.

### **Monitoring Strategy**
- **Application Performance Monitoring (APM)**: Track application metrics and performance
- **Infrastructure Monitoring**: Monitor servers, databases, and network resources
- **Real-User Monitoring (RUM)**: Track actual user experience and interactions
- **Business Metrics**: Monitor key business indicators and ride-hailing metrics
- **Security Monitoring**: Detect and respond to security threats

## 🔍 **Application Performance Monitoring**

### **1. Supabase Monitoring Configuration**
```sql
-- Database performance monitoring views
CREATE OR REPLACE VIEW monitoring.database_performance AS
SELECT 
  schemaname,
  tablename,
  attname,
  n_distinct,
  correlation,
  most_common_vals,
  most_common_freqs
FROM pg_stats 
WHERE schemaname = 'public';

-- Active connections monitoring
CREATE OR REPLACE VIEW monitoring.active_connections AS
SELECT 
  pid,
  usename,
  application_name,
  client_addr,
  backend_start,
  state,
  query_start,
  LEFT(query, 100) as query_preview
FROM pg_stat_activity 
WHERE state != 'idle'
ORDER BY backend_start DESC;

-- Slow query monitoring
CREATE OR REPLACE VIEW monitoring.slow_queries AS
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows,
  100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 1000 -- queries taking more than 1 second
ORDER BY mean_time DESC;

-- Table size monitoring
CREATE OR REPLACE VIEW monitoring.table_sizes AS
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
  pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;
```

### **2. Application Metrics Collection**
```typescript
// packages/monitoring/src/metrics.ts
import { createClient } from '@supabase/supabase-js';

export class MetricsCollector {
  private supabase: any;
  private metricsBuffer: Map<string, any[]> = new Map();
  private flushInterval: NodeJS.Timeout;

  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Flush metrics every 30 seconds
    this.flushInterval = setInterval(() => {
      this.flushMetrics();
    }, 30000);
  }

  // Track API response times
  trackApiResponse(endpoint: string, method: string, statusCode: number, responseTime: number) {
    this.addMetric('api_responses', {
      endpoint,
      method,
      status_code: statusCode,
      response_time: responseTime,
      timestamp: new Date().toISOString(),
    });
  }

  // Track ride events
  trackRideEvent(rideId: string, event: string, metadata?: any) {
    this.addMetric('ride_events', {
      ride_id: rideId,
      event,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }

  // Track user actions
  trackUserAction(userId: string, action: string, userType: string, metadata?: any) {
    this.addMetric('user_actions', {
      user_id: userId,
      action,
      user_type: userType,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }

  // Track business metrics
  trackBusinessMetric(metric: string, value: number, dimensions?: Record<string, any>) {
    this.addMetric('business_metrics', {
      metric,
      value,
      dimensions,
      timestamp: new Date().toISOString(),
    });
  }

  // Track errors
  trackError(error: Error, context?: any) {
    this.addMetric('errors', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    });
  }

  private addMetric(table: string, data: any) {
    if (!this.metricsBuffer.has(table)) {
      this.metricsBuffer.set(table, []);
    }
    this.metricsBuffer.get(table)!.push(data);
  }

  private async flushMetrics() {
    for (const [table, metrics] of this.metricsBuffer.entries()) {
      if (metrics.length > 0) {
        try {
          await this.supabase
            .from(`metrics_${table}`)
            .insert(metrics);
          
          this.metricsBuffer.set(table, []);
        } catch (error) {
          console.error(`Failed to flush metrics for ${table}:`, error);
        }
      }
    }
  }

  destroy() {
    clearInterval(this.flushInterval);
    this.flushMetrics(); // Final flush
  }
}

// Singleton instance
export const metricsCollector = new MetricsCollector();

// Express middleware for API monitoring
export const apiMonitoringMiddleware = (req: any, res: any, next: any) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    metricsCollector.trackApiResponse(
      req.route?.path || req.path,
      req.method,
      res.statusCode,
      responseTime
    );
  });
  
  next();
};
```

### **3. Real-Time Monitoring Dashboard**
```typescript
// apps/admin-dashboard/src/components/monitoring/MonitoringDashboard.tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { AlertTriangle, Activity, Users, Car } from 'lucide-react';

interface MetricData {
  timestamp: string;
  value: number;
}

interface SystemHealth {
  api_response_time: number;
  active_users: number;
  active_drivers: number;
  active_rides: number;
  error_rate: number;
  database_connections: number;
}

export const MonitoringDashboard: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [responseTimeData, setResponseTimeData] = useState<MetricData[]>([]);
  const [userActivityData, setUserActivityData] = useState<MetricData[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        // Fetch current system health
        const healthResponse = await fetch('/api/monitoring/health');
        const health = await healthResponse.json();
        setSystemHealth(health);

        // Fetch time series data
        const responseTimeResponse = await fetch('/api/monitoring/response-times?period=1h');
        const responseTimeData = await responseTimeResponse.json();
        setResponseTimeData(responseTimeData);

        const userActivityResponse = await fetch('/api/monitoring/user-activity?period=1h');
        const userActivity = await userActivityResponse.json();
        setUserActivityData(userActivity);

        // Fetch active alerts
        const alertsResponse = await fetch('/api/monitoring/alerts');
        const alerts = await alertsResponse.json();
        setAlerts(alerts);
      } catch (error) {
        console.error('Failed to fetch monitoring data:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getHealthStatus = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'critical';
    if (value >= thresholds.warning) return 'warning';
    return 'healthy';
  };

  if (!systemHealth) {
    return <div className="flex items-center justify-center h-64">Loading monitoring data...</div>;
  }

  return (
    <div className="space-y-6">
      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Response Time</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemHealth.api_response_time}ms</div>
            <p className={`text-xs ${
              getHealthStatus(systemHealth.api_response_time, { warning: 500, critical: 1000 }) === 'healthy' 
                ? 'text-green-600' 
                : getHealthStatus(systemHealth.api_response_time, { warning: 500, critical: 1000 }) === 'warning'
                ? 'text-yellow-600'
                : 'text-red-600'
            }`}>
              {getHealthStatus(systemHealth.api_response_time, { warning: 500, critical: 1000 })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemHealth.active_users}</div>
            <p className="text-xs text-muted-foreground">
              {systemHealth.active_drivers} drivers online
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Rides</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemHealth.active_rides}</div>
            <p className="text-xs text-muted-foreground">
              In progress rides
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(systemHealth.error_rate * 100).toFixed(2)}%</div>
            <p className={`text-xs ${
              systemHealth.error_rate < 0.01 ? 'text-green-600' : 
              systemHealth.error_rate < 0.05 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {systemHealth.error_rate < 0.01 ? 'Healthy' : 
               systemHealth.error_rate < 0.05 ? 'Warning' : 'Critical'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Active Alerts ({alerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alerts.map((alert, index) => (
                <div key={index} className={`p-3 rounded-lg border ${
                  alert.severity === 'critical' ? 'border-red-200 bg-red-50' :
                  alert.severity === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                  'border-blue-200 bg-blue-50'
                }`}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{alert.title}</h4>
                      <p className="text-sm text-muted-foreground">{alert.description}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                      alert.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {alert.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>API Response Times</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={responseTimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userActivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#10b981" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
```

## 🚨 **Alerting System**

### **1. Alert Configuration**
```typescript
// packages/monitoring/src/alerting.ts
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals';
  threshold: number;
  duration: number; // in seconds
  severity: 'low' | 'medium' | 'high' | 'critical';
  channels: string[]; // slack, email, sms, webhook
  enabled: boolean;
}

export const alertRules: AlertRule[] = [
  {
    id: 'high_api_response_time',
    name: 'High API Response Time',
    description: 'API response time is above acceptable threshold',
    metric: 'api_response_time_avg',
    condition: 'greater_than',
    threshold: 1000, // 1 second
    duration: 300, // 5 minutes
    severity: 'high',
    channels: ['slack', 'email'],
    enabled: true,
  },
  {
    id: 'high_error_rate',
    name: 'High Error Rate',
    description: 'Error rate is above acceptable threshold',
    metric: 'error_rate',
    condition: 'greater_than',
    threshold: 0.05, // 5%
    duration: 180, // 3 minutes
    severity: 'critical',
    channels: ['slack', 'email', 'sms'],
    enabled: true,
  },
  {
    id: 'low_driver_availability',
    name: 'Low Driver Availability',
    description: 'Number of online drivers is below minimum threshold',
    metric: 'active_drivers',
    condition: 'less_than',
    threshold: 10,
    duration: 600, // 10 minutes
    severity: 'medium',
    channels: ['slack'],
    enabled: true,
  },
  {
    id: 'database_connection_limit',
    name: 'Database Connection Limit',
    description: 'Database connections approaching limit',
    metric: 'database_connections',
    condition: 'greater_than',
    threshold: 80, // 80% of limit
    duration: 120, // 2 minutes
    severity: 'high',
    channels: ['slack', 'email'],
    enabled: true,
  },
  {
    id: 'failed_ride_rate',
    name: 'High Failed Ride Rate',
    description: 'Rate of failed rides is above threshold',
    metric: 'failed_ride_rate',
    condition: 'greater_than',
    threshold: 0.1, // 10%
    duration: 900, // 15 minutes
    severity: 'high',
    channels: ['slack', 'email'],
    enabled: true,
  },
];

export class AlertManager {
  private activeAlerts: Map<string, any> = new Map();
  private alertHistory: any[] = [];

  async evaluateAlerts() {
    for (const rule of alertRules) {
      if (!rule.enabled) continue;

      try {
        const currentValue = await this.getMetricValue(rule.metric);
        const isTriggered = this.evaluateCondition(currentValue, rule.condition, rule.threshold);

        if (isTriggered) {
          await this.handleTriggeredAlert(rule, currentValue);
        } else {
          await this.handleResolvedAlert(rule);
        }
      } catch (error) {
        console.error(`Failed to evaluate alert rule ${rule.id}:`, error);
      }
    }
  }

  private async getMetricValue(metric: string): Promise<number> {
    // Implementation depends on your metrics storage
    // This could query Supabase, external monitoring service, etc.
    const response = await fetch(`/api/monitoring/metrics/${metric}`);
    const data = await response.json();
    return data.value;
  }

  private evaluateCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'greater_than':
        return value > threshold;
      case 'less_than':
        return value < threshold;
      case 'equals':
        return value === threshold;
      case 'not_equals':
        return value !== threshold;
      default:
        return false;
    }
  }

  private async handleTriggeredAlert(rule: AlertRule, currentValue: number) {
    const alertKey = rule.id;
    const existingAlert = this.activeAlerts.get(alertKey);

    if (!existingAlert) {
      // New alert
      const alert = {
        id: alertKey,
        rule,
        triggeredAt: new Date(),
        currentValue,
        notificationsSent: 0,
      };

      this.activeAlerts.set(alertKey, alert);
      await this.sendAlert(alert);
    } else {
      // Update existing alert
      existingAlert.currentValue = currentValue;
      existingAlert.lastUpdated = new Date();

      // Send escalation if needed
      const timeSinceTriggered = Date.now() - existingAlert.triggeredAt.getTime();
      if (timeSinceTriggered > 30 * 60 * 1000 && existingAlert.notificationsSent < 3) {
        // Escalate after 30 minutes, max 3 escalations
        await this.sendAlert(existingAlert, true);
      }
    }
  }

  private async handleResolvedAlert(rule: AlertRule) {
    const alertKey = rule.id;
    const existingAlert = this.activeAlerts.get(alertKey);

    if (existingAlert) {
      // Alert resolved
      existingAlert.resolvedAt = new Date();
      this.alertHistory.push(existingAlert);
      this.activeAlerts.delete(alertKey);

      await this.sendResolutionNotification(existingAlert);
    }
  }

  private async sendAlert(alert: any, isEscalation: boolean = false) {
    const message = this.formatAlertMessage(alert, isEscalation);

    for (const channel of alert.rule.channels) {
      try {
        await this.sendToChannel(channel, message, alert.rule.severity);
        alert.notificationsSent++;
      } catch (error) {
        console.error(`Failed to send alert to ${channel}:`, error);
      }
    }
  }

  private async sendResolutionNotification(alert: any) {
    const message = this.formatResolutionMessage(alert);

    for (const channel of alert.rule.channels) {
      try {
        await this.sendToChannel(channel, message, 'resolved');
      } catch (error) {
        console.error(`Failed to send resolution to ${channel}:`, error);
      }
    }
  }

  private formatAlertMessage(alert: any, isEscalation: boolean): string {
    const prefix = isEscalation ? '🚨 ESCALATION' : '⚠️ ALERT';
    return `${prefix}: ${alert.rule.name}
    
Description: ${alert.rule.description}
Current Value: ${alert.currentValue}
Threshold: ${alert.rule.threshold}
Severity: ${alert.rule.severity.toUpperCase()}
Triggered: ${alert.triggeredAt.toISOString()}

Dashboard: https://admin.taxicab.co.zw/monitoring`;
  }

  private formatResolutionMessage(alert: any): string {
    const duration = alert.resolvedAt.getTime() - alert.triggeredAt.getTime();
    const durationMinutes = Math.round(duration / 60000);

    return `✅ RESOLVED: ${alert.rule.name}

Alert was active for ${durationMinutes} minutes
Resolved: ${alert.resolvedAt.toISOString()}`;
  }

  private async sendToChannel(channel: string, message: string, severity: string) {
    switch (channel) {
      case 'slack':
        await this.sendSlackNotification(message, severity);
        break;
      case 'email':
        await this.sendEmailNotification(message, severity);
        break;
      case 'sms':
        await this.sendSMSNotification(message, severity);
        break;
      case 'webhook':
        await this.sendWebhookNotification(message, severity);
        break;
    }
  }

  private async sendSlackNotification(message: string, severity: string) {
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!webhookUrl) return;

    const color = {
      low: '#36a64f',
      medium: '#ff9500',
      high: '#ff0000',
      critical: '#8b0000',
      resolved: '#36a64f',
    }[severity] || '#36a64f';

    await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        attachments: [{
          color,
          text: message,
          ts: Math.floor(Date.now() / 1000),
        }],
      }),
    });
  }

  private async sendEmailNotification(message: string, severity: string) {
    // Implementation for email notifications
    // Could use SendGrid, AWS SES, etc.
  }

  private async sendSMSNotification(message: string, severity: string) {
    // Implementation for SMS notifications
    // Could use Twilio, AWS SNS, etc.
  }

  private async sendWebhookNotification(message: string, severity: string) {
    // Implementation for webhook notifications
    // For integration with external systems
  }
}

// Initialize alert manager
export const alertManager = new AlertManager();

// Run alert evaluation every minute
setInterval(() => {
  alertManager.evaluateAlerts();
}, 60000);
```

This comprehensive monitoring and alerting system provides real-time visibility into the Taxicab platform's health, performance, and business metrics with proactive alerting and escalation procedures to ensure optimal system operation.
