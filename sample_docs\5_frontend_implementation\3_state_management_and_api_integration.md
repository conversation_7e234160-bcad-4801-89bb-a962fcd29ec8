# 3. State Management and API Integration

## 🔄 **State Management Overview**

This document provides comprehensive implementation guidelines for state management using Redux Toolkit and API integration patterns for the UniversalWallet frontend applications, ensuring efficient data flow, caching, and synchronization.

## 🏪 **Redux Store Configuration**

### **Store Setup**
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

import authSlice from './slices/authSlice';
import accountSlice from './slices/accountSlice';
import transactionSlice from './slices/transactionSlice';
import businessSlice from './slices/businessSlice';
import agentSlice from './slices/agentSlice';
import notificationSlice from './slices/notificationSlice';
import { apiSlice } from './api/apiSlice';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // Only persist auth state
  blacklist: ['api'], // Don't persist API cache
};

const rootReducer = combineReducers({
  auth: authSlice,
  accounts: accountSlice,
  transactions: transactionSlice,
  business: businessSlice,
  agent: agentSlice,
  notifications: notificationSlice,
  api: apiSlice.reducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(apiSlice.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### **API Slice with RTK Query**
```typescript
// store/api/apiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';

const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1',
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.accessToken;
    
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    
    headers.set('content-type', 'application/json');
    headers.set('x-client-type', 'web');
    
    return headers;
  },
});

const baseQueryWithReauth = async (args: any, api: any, extraOptions: any) => {
  let result = await baseQuery(args, api, extraOptions);
  
  if (result.error && result.error.status === 401) {
    // Try to refresh token
    const refreshResult = await baseQuery(
      {
        url: '/auth/refresh',
        method: 'POST',
        body: {
          refreshToken: (api.getState() as RootState).auth.refreshToken,
        },
      },
      api,
      extraOptions
    );
    
    if (refreshResult.data) {
      // Store new token
      api.dispatch(authSlice.actions.setTokens(refreshResult.data));
      
      // Retry original query
      result = await baseQuery(args, api, extraOptions);
    } else {
      // Refresh failed, logout user
      api.dispatch(authSlice.actions.logout());
    }
  }
  
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'User',
    'Account',
    'Transaction',
    'Business',
    'Agent',
    'Invoice',
    'BulkPayment',
  ],
  endpoints: (builder) => ({}),
});
```

---

## 🔐 **Authentication Slice**

### **Auth State Management**
```typescript
// store/slices/authSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authApi } from '../api/authApi';

interface User {
  userId: string;
  phoneNumber: string;
  email?: string;
  userType: 'personal' | 'business' | 'agent' | 'admin';
  kycLevel: 'basic' | 'enhanced' | 'business' | 'agent';
  status: 'active' | 'suspended' | 'pending';
  profile?: {
    firstName: string;
    lastName: string;
    profilePictureUrl?: string;
  };
}

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastActivity: number;
}

const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  lastActivity: Date.now(),
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (userData: {
    phoneNumber: string;
    pin: string;
    userType: string;
  }, { rejectWithValue }) => {
    try {
      const response = await authApi.register(userData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed');
    }
  }
);

export const verifyOtp = createAsyncThunk(
  'auth/verifyOtp',
  async (otpData: {
    phoneNumber: string;
    otp: string;
    verificationToken: string;
  }, { rejectWithValue }) => {
    try {
      const response = await authApi.verifyOtp(otpData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'OTP verification failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const response = await authApi.refreshToken(state.auth.refreshToken!);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Token refresh failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLastActivity: (state) => {
      state.lastActivity = Date.now();
    },
    setTokens: (state, action: PayloadAction<{
      accessToken: string;
      refreshToken: string;
    }>) => {
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.lastActivity = Date.now();
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Registration
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        // Registration successful, but user needs to verify OTP
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // OTP Verification
      .addCase(verifyOtp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOtp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.lastActivity = Date.now();
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Token Refresh
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.lastActivity = Date.now();
      })
      .addCase(refreshToken.rejected, (state) => {
        // Token refresh failed, logout user
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
      });
  },
});

export const { logout, clearError, updateLastActivity, setTokens, updateUser } = authSlice.actions;
export default authSlice.reducer;
```

---

## 💰 **Transaction Management**

### **Transaction Slice**
```typescript
// store/slices/transactionSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { transactionApi } from '../api/transactionApi';

interface Transaction {
  transactionId: string;
  referenceNumber: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  fee: number;
  totalAmount: number;
  currency: string;
  recipient?: {
    phone: string;
    name: string;
  };
  description?: string;
  createdAt: string;
  completedAt?: string;
}

interface TransactionState {
  transactions: Transaction[];
  recentTransactions: Transaction[];
  currentTransaction: Transaction | null;
  isLoading: boolean;
  isCreating: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

const initialState: TransactionState = {
  transactions: [],
  recentTransactions: [],
  currentTransaction: null,
  isLoading: false,
  isCreating: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
  },
};

export const createTransfer = createAsyncThunk(
  'transactions/createTransfer',
  async (transferData: {
    recipientPhone: string;
    amount: number;
    sourceAccountId: string;
    description?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionApi.createTransfer(transferData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Transfer failed');
    }
  }
);

export const fetchTransactions = createAsyncThunk(
  'transactions/fetchTransactions',
  async (params: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await transactionApi.getTransactions(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch transactions');
    }
  }
);

export const fetchRecentTransactions = createAsyncThunk(
  'transactions/fetchRecentTransactions',
  async (params: { limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await transactionApi.getTransactions({ 
        ...params, 
        limit: params.limit || 5 
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch recent transactions');
    }
  }
);

export const fetchTransactionDetails = createAsyncThunk(
  'transactions/fetchTransactionDetails',
  async (transactionId: string, { rejectWithValue }) => {
    try {
      const response = await transactionApi.getTransactionDetails(transactionId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch transaction details');
    }
  }
);

const transactionSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentTransaction: (state) => {
      state.currentTransaction = null;
    },
    updateTransactionStatus: (state, action) => {
      const { transactionId, status } = action.payload;
      
      // Update in transactions list
      const transactionIndex = state.transactions.findIndex(
        t => t.transactionId === transactionId
      );
      if (transactionIndex !== -1) {
        state.transactions[transactionIndex].status = status;
      }
      
      // Update in recent transactions
      const recentIndex = state.recentTransactions.findIndex(
        t => t.transactionId === transactionId
      );
      if (recentIndex !== -1) {
        state.recentTransactions[recentIndex].status = status;
      }
      
      // Update current transaction
      if (state.currentTransaction?.transactionId === transactionId) {
        state.currentTransaction.status = status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Create Transfer
      .addCase(createTransfer.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createTransfer.fulfilled, (state, action) => {
        state.isCreating = false;
        state.transactions.unshift(action.payload);
        state.recentTransactions.unshift(action.payload);
        
        // Keep only latest 5 recent transactions
        if (state.recentTransactions.length > 5) {
          state.recentTransactions = state.recentTransactions.slice(0, 5);
        }
      })
      .addCase(createTransfer.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      
      // Fetch Transactions
      .addCase(fetchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = action.payload.transactions;
        state.pagination = {
          page: action.payload.pagination.currentPage,
          limit: action.payload.pagination.limit,
          total: action.payload.pagination.totalRecords,
          hasMore: action.payload.pagination.hasNext,
        };
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Recent Transactions
      .addCase(fetchRecentTransactions.fulfilled, (state, action) => {
        state.recentTransactions = action.payload.transactions;
      })
      
      // Fetch Transaction Details
      .addCase(fetchTransactionDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactionDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTransaction = action.payload;
      })
      .addCase(fetchTransactionDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearCurrentTransaction, updateTransactionStatus } = transactionSlice.actions;
export default transactionSlice.reducer;
```

---

## 🌐 **API Service Layer**

### **Base API Service**
```typescript
// services/api/baseApi.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../../store';
import { logout, refreshToken } from '../../store/slices/authSlice';

class BaseApiService {
  private api: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
  }> = [];

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const token = state.auth.accessToken;

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        config.headers['X-Client-Type'] = 'web';
        config.headers['X-Request-ID'] = this.generateRequestId();

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // If already refreshing, queue the request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.api(originalRequest);
            }).catch((err) => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            await store.dispatch(refreshToken()).unwrap();
            this.processQueue(null);
            
            const state = store.getState();
            const newToken = state.auth.accessToken;
            
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.api(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError);
            store.dispatch(logout());
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private processQueue(error: any) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        const state = store.getState();
        resolve(state.auth.accessToken);
      }
    });

    this.failedQueue = [];
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url, config);
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data, config);
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data, config);
  }

  public async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.patch<T>(url, data, config);
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url, config);
  }
}

export const baseApi = new BaseApiService();
```

### **Transaction API Service**
```typescript
// services/api/transactionApi.ts
import { baseApi } from './baseApi';

export interface TransferRequest {
  recipientPhone: string;
  amount: number;
  sourceAccountId: string;
  description?: string;
}

export interface TransactionFilters {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

class TransactionApiService {
  async createTransfer(data: TransferRequest) {
    return baseApi.post('/transactions/transfer', data);
  }

  async confirmTransfer(transactionId: string, pin: string) {
    return baseApi.post(`/transactions/${transactionId}/confirm`, { pin });
  }

  async getTransactions(filters: TransactionFilters = {}) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    return baseApi.get(`/transactions?${params.toString()}`);
  }

  async getTransactionDetails(transactionId: string) {
    return baseApi.get(`/transactions/${transactionId}`);
  }

  async cancelTransaction(transactionId: string) {
    return baseApi.post(`/transactions/${transactionId}/cancel`);
  }

  async getTransactionReceipt(transactionId: string) {
    return baseApi.get(`/transactions/${transactionId}/receipt`, {
      responseType: 'blob',
    });
  }

  async searchTransactions(query: string, filters: TransactionFilters = {}) {
    return baseApi.get('/transactions/search', {
      params: { q: query, ...filters },
    });
  }
}

export const transactionApi = new TransactionApiService();
```

**This comprehensive state management and API integration implementation provides efficient data flow, caching, error handling, and synchronization for the UniversalWallet frontend applications.** 🔄
