# 1. Development Environment Setup

## 🛠️ **Development Environment Overview**

This guide provides step-by-step instructions for setting up a complete development environment for the Taxicab platform. The setup includes all necessary tools, dependencies, and configurations for frontend, backend, and mobile development.

### **Environment Requirements**
- **Operating System**: Windows 10/11, macOS 10.15+, or Ubuntu 20.04+
- **Node.js**: Version 18.x or higher
- **Git**: Version 2.30 or higher
- **Code Editor**: Visual Studio Code (recommended)
- **Mobile Development**: Android Studio and/or Xcode

## 📋 **Prerequisites Installation**

### **1. Node.js and npm Setup**
```bash
# Install Node.js 18.x using Node Version Manager (recommended)
# For Windows (using Chocolatey)
choco install nodejs --version=18.17.0

# For macOS (using Homebrew)
brew install node@18

# For Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 9.x.x or higher
```

### **2. Git Configuration**
```bash
# Install Git (if not already installed)
# Windows: Download from https://git-scm.com/download/win
# macOS: git --version (will prompt to install if needed)
# Ubuntu: sudo apt-get install git

# Configure Git with your details
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main

# Configure Git for better collaboration
git config --global pull.rebase false
git config --global core.autocrlf input  # For macOS/Linux
git config --global core.autocrlf true   # For Windows
```

### **3. Visual Studio Code Setup**
```bash
# Download and install VS Code from https://code.visualstudio.com/

# Install essential extensions via command line
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension bradlc.vscode-tailwindcss
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-eslint
code --install-extension ms-vscode.vscode-json
code --install-extension ms-vscode.vscode-react-native
code --install-extension supabase.supabase-vscode
code --install-extension GitHub.copilot
```

## 🏗️ **Project Structure Setup**

### **1. Repository Cloning and Structure**
```bash
# Clone the repository
git clone https://github.com/your-org/taxicab-platform.git
cd taxicab-platform

# Expected project structure
taxicab-platform/
├── apps/
│   ├── admin-dashboard/          # React + Shadcn UI admin dashboard
│   ├── passenger-app/            # React Native passenger app
│   └── driver-app/               # React Native driver app
├── packages/
│   ├── shared-types/             # Shared TypeScript types
│   ├── ui-components/            # Shared UI components
│   ├── api-client/               # Supabase API client
│   └── utils/                    # Shared utilities
├── supabase/
│   ├── migrations/               # Database migrations
│   ├── functions/                # Edge functions
│   └── config/                   # Supabase configuration
├── docs/                         # Project documentation
├── scripts/                      # Build and deployment scripts
├── .github/                      # GitHub Actions workflows
├── package.json                  # Root package.json for monorepo
├── turbo.json                    # Turborepo configuration
└── README.md                     # Project README
```

### **2. Monorepo Setup with Turborepo**
```bash
# Install Turborepo globally
npm install -g turbo

# Initialize the monorepo (if starting fresh)
npx create-turbo@latest taxicab-platform --package-manager npm

# Install all dependencies
npm install

# Verify turbo setup
turbo --version
```

## 🔧 **Frontend Development Setup**

### **1. Admin Dashboard (React + Shadcn UI)**
```bash
# Navigate to admin dashboard
cd apps/admin-dashboard

# Install dependencies
npm install

# Install Shadcn UI
npx shadcn-ui@latest init

# Configure Shadcn UI (select options)
# - TypeScript: Yes
# - Style: Default
# - Base color: Slate
# - CSS variables: Yes

# Install essential Shadcn components
npx shadcn-ui@latest add button
npx shadcn-ui@latest add input
npx shadcn-ui@latest add card
npx shadcn-ui@latest add table
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add form
npx shadcn-ui@latest add toast
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add navigation-menu

# Install additional dependencies
npm install @tanstack/react-query zustand react-router-dom
npm install @supabase/supabase-js
npm install lucide-react date-fns
npm install -D @types/node
```

### **2. Mobile Apps Setup (React Native)**
```bash
# Install React Native CLI
npm install -g @react-native-community/cli

# For iOS development (macOS only)
sudo gem install cocoapods
cd ios && pod install && cd ..

# For Android development
# Download and install Android Studio
# Set up Android SDK and emulator

# Install React Native dependencies
cd apps/passenger-app
npm install

# Install React Native specific packages
npm install @react-navigation/native @react-navigation/stack
npm install react-native-screens react-native-safe-area-context
npm install react-native-maps react-native-geolocation-service
npm install @react-native-async-storage/async-storage
npm install react-native-keychain
npm install @react-native-firebase/app @react-native-firebase/messaging

# For iOS (run in ios/ directory)
cd ios && pod install && cd ..
```

## 🗄️ **Supabase Development Setup**

### **1. Supabase CLI Installation**
```bash
# Install Supabase CLI
npm install -g supabase

# Verify installation
supabase --version

# Login to Supabase (optional for local development)
supabase login
```

### **2. Local Supabase Development**
```bash
# Initialize Supabase in project
cd supabase
supabase init

# Start local Supabase stack
supabase start

# This will start:
# - PostgreSQL database
# - Supabase API
# - Supabase Dashboard
# - Supabase Auth
# - Supabase Storage
# - Supabase Edge Functions

# Note the local URLs and keys provided
# API URL: http://localhost:54321
# DB URL: postgresql://postgres:postgres@localhost:54322/postgres
# Studio URL: http://localhost:54323
# Inbucket URL: http://localhost:54324
# anon key: [provided key]
# service_role key: [provided key]
```

### **3. Environment Variables Setup**
```bash
# Create environment files for each app

# apps/admin-dashboard/.env.local
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_anon_key_here
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key
VITE_APP_ENV=development

# apps/passenger-app/.env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_anon_key_here
GOOGLE_MAPS_API_KEY=your_google_maps_key
APP_ENV=development

# apps/driver-app/.env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_anon_key_here
GOOGLE_MAPS_API_KEY=your_google_maps_key
APP_ENV=development

# supabase/.env (for Edge Functions)
GOOGLE_MAPS_API_KEY=your_google_maps_key
ECOCASH_API_KEY=your_ecocash_key
ONEMONEY_API_KEY=your_onemoney_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
```

## 🔨 **Development Tools Configuration**

### **1. TypeScript Configuration**
```json
// Root tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["packages/*/src"],
      "@ui/*": ["packages/ui-components/src/*"],
      "@types/*": ["packages/shared-types/src/*"],
      "@utils/*": ["packages/utils/src/*"]
    }
  },
  "include": [
    "apps/**/*",
    "packages/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}
```

### **2. ESLint Configuration**
```json
// .eslintrc.json
{
  "root": true,
  "extends": [
    "@react-native-community",
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint", "react", "react-hooks"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn"
  },
  "env": {
    "browser": true,
    "es6": true,
    "node": true
  }
}
```

### **3. Prettier Configuration**
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

## 🚀 **Development Scripts Setup**

### **1. Root Package.json Scripts**
```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "clean": "turbo run clean",
    "db:start": "cd supabase && supabase start",
    "db:stop": "cd supabase && supabase stop",
    "db:reset": "cd supabase && supabase db reset",
    "db:migrate": "cd supabase && supabase db push",
    "functions:serve": "cd supabase && supabase functions serve",
    "setup": "npm install && npm run db:start"
  }
}
```

### **2. Turborepo Configuration**
```json
// turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", "build/**", ".next/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "outputs": []
    },
    "type-check": {
      "dependsOn": ["^build"],
      "outputs": []
    },
    "test": {
      "dependsOn": ["^build"],
      "outputs": []
    },
    "clean": {
      "cache": false
    }
  }
}
```

## ✅ **Development Environment Verification**

### **1. Verification Checklist**
```bash
# 1. Verify Node.js and npm
node --version && npm --version

# 2. Verify Git configuration
git config --list

# 3. Verify Supabase CLI
supabase --version

# 4. Start local development
npm run setup

# 5. Verify all apps start correctly
npm run dev

# 6. Check Supabase local instance
# Visit http://localhost:54323 (Supabase Studio)

# 7. Verify mobile development (if applicable)
# Android: npx react-native run-android
# iOS: npx react-native run-ios
```

### **2. Common Issues and Solutions**

#### **Node.js Version Issues**
```bash
# Use Node Version Manager to switch versions
nvm install 18
nvm use 18
```

#### **Port Conflicts**
```bash
# Check what's running on ports
lsof -i :3000  # Frontend dev server
lsof -i :54321 # Supabase API
lsof -i :54323 # Supabase Studio

# Kill processes if needed
kill -9 <PID>
```

#### **Supabase Connection Issues**
```bash
# Reset Supabase local instance
supabase stop
supabase start
```

This development environment setup ensures all team members have a consistent, fully-functional development environment for building the Taxicab platform.
