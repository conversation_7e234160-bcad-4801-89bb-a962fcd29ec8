# 1. Production Deployment

## 🚀 **Production Deployment Overview**

This document provides comprehensive guidelines for production deployment of the UniversalWallet platform, covering infrastructure setup, deployment strategies, security configurations, and operational procedures for enterprise-scale financial services.

## 🏗️ **Production Infrastructure Architecture**

### **Cloud Infrastructure Setup (AWS)**
```yaml
# terraform/production/main.tf
provider "aws" {
  region = "af-south-1" # Cape Town region for Zimbabwe proximity
}

# VPC Configuration
resource "aws_vpc" "universalwallet_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "universalwallet-production-vpc"
    Environment = "production"
    Project     = "universalwallet"
  }
}

# Availability Zones
data "aws_availability_zones" "available" {
  state = "available"
}

# Public Subnets (Load Balancers, NAT Gateways)
resource "aws_subnet" "public" {
  count             = 3
  vpc_id            = aws_vpc.universalwallet_vpc.id
  cidr_block        = "10.0.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name = "universalwallet-public-subnet-${count.index + 1}"
    Type = "public"
  }
}

# Private Subnets (Application Servers)
resource "aws_subnet" "private" {
  count             = 3
  vpc_id            = aws_vpc.universalwallet_vpc.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "universalwallet-private-subnet-${count.index + 1}"
    Type = "private"
  }
}

# Database Subnets
resource "aws_subnet" "database" {
  count             = 3
  vpc_id            = aws_vpc.universalwallet_vpc.id
  cidr_block        = "10.0.${count.index + 20}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "universalwallet-database-subnet-${count.index + 1}"
    Type = "database"
  }
}
```

### **EKS Cluster Configuration**
```yaml
# terraform/production/eks.tf
resource "aws_eks_cluster" "universalwallet" {
  name     = "universalwallet-production"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "1.28"

  vpc_config {
    subnet_ids              = concat(aws_subnet.private[*].id, aws_subnet.public[*].id)
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = ["0.0.0.0/0"]
  }

  encryption_config {
    provider {
      key_arn = aws_kms_key.eks.arn
    }
    resources = ["secrets"]
  }

  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
    aws_iam_role_policy_attachment.eks_vpc_resource_controller,
  ]

  tags = {
    Environment = "production"
    Project     = "universalwallet"
  }
}

# Node Groups
resource "aws_eks_node_group" "application" {
  cluster_name    = aws_eks_cluster.universalwallet.name
  node_group_name = "application-nodes"
  node_role_arn   = aws_iam_role.eks_node_group.arn
  subnet_ids      = aws_subnet.private[*].id

  capacity_type  = "ON_DEMAND"
  instance_types = ["m5.xlarge"]

  scaling_config {
    desired_size = 6
    max_size     = 12
    min_size     = 3
  }

  update_config {
    max_unavailable = 1
  }

  labels = {
    role = "application"
  }

  tags = {
    Environment = "production"
    NodeType    = "application"
  }
}

resource "aws_eks_node_group" "database" {
  cluster_name    = aws_eks_cluster.universalwallet.name
  node_group_name = "database-nodes"
  node_role_arn   = aws_iam_role.eks_node_group.arn
  subnet_ids      = aws_subnet.private[*].id

  capacity_type  = "ON_DEMAND"
  instance_types = ["r5.2xlarge"]

  scaling_config {
    desired_size = 3
    max_size     = 6
    min_size     = 3
  }

  labels = {
    role = "database"
  }

  taints {
    key    = "database"
    value  = "true"
    effect = "NO_SCHEDULE"
  }

  tags = {
    Environment = "production"
    NodeType    = "database"
  }
}
```

---

## 🗄️ **Database Production Setup**

### **PostgreSQL High Availability Configuration**
```yaml
# k8s/database/postgresql-ha.yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: universalwallet-postgres
  namespace: database
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "500"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      work_mem: "4MB"
      min_wal_size: "1GB"
      max_wal_size: "4GB"
      max_worker_processes: "8"
      max_parallel_workers_per_gather: "4"
      max_parallel_workers: "8"
      max_parallel_maintenance_workers: "4"

  bootstrap:
    initdb:
      database: universalwallet
      owner: universalwallet
      secret:
        name: postgres-credentials

  storage:
    size: 500Gi
    storageClass: gp3-encrypted

  monitoring:
    enabled: true

  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://universalwallet-backups/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "7d"
      data:
        retention: "30d"

  nodeMaintenanceWindow:
    inProgress: false
    reusePVC: true
```

### **Redis Cluster Configuration**
```yaml
# k8s/database/redis-cluster.yaml
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: universalwallet-redis
  namespace: database
spec:
  clusterSize: 6
  clusterVersion: v7
  persistenceEnabled: true
  
  redisExporter:
    enabled: true
    image: quay.io/opstree/redis-exporter:latest

  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 100Gi
        storageClassName: gp3-encrypted

  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

  securityContext:
    runAsUser: 1000
    fsGroup: 1000

  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - redis-cluster
        topologyKey: kubernetes.io/hostname
```

---

## 🚀 **Application Deployment Configuration**

### **Backend Service Deployment**
```yaml
# k8s/backend/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: universalwallet-backend
  namespace: application
  labels:
    app: universalwallet-backend
    version: v1.0.0
spec:
  replicas: 6
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: universalwallet-backend
  template:
    metadata:
      labels:
        app: universalwallet-backend
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: universalwallet-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: backend
        image: universalwallet/backend:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: password
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: encryption-key
              key: key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: temp
          mountPath: /tmp
      volumes:
      - name: logs
        emptyDir: {}
      - name: temp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - universalwallet-backend
              topologyKey: kubernetes.io/hostname
```

### **Frontend Web Portal Deployment**
```yaml
# k8s/frontend/web-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: universalwallet-web
  namespace: application
spec:
  replicas: 4
  selector:
    matchLabels:
      app: universalwallet-web
  template:
    metadata:
      labels:
        app: universalwallet-web
    spec:
      containers:
      - name: web
        image: universalwallet/web:1.0.0
        ports:
        - containerPort: 80
        env:
        - name: REACT_APP_API_URL
          value: "https://api.universalwallet.co.zw/api/v1"
        - name: REACT_APP_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
```

---

## 🔒 **Security and Compliance Configuration**

### **Network Policies**
```yaml
# k8s/security/network-policies.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: application
spec:
  podSelector:
    matchLabels:
      app: universalwallet-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: universalwallet-web
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
```

### **Pod Security Standards**
```yaml
# k8s/security/pod-security-policy.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: application
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

---

## 📊 **Monitoring and Observability**

### **Prometheus Configuration**
```yaml
# k8s/monitoring/prometheus.yaml
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: universalwallet-prometheus
  namespace: monitoring
spec:
  replicas: 2
  retention: 30d
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 500Gi
        storageClassName: gp3-encrypted
  
  serviceMonitorSelector:
    matchLabels:
      app: universalwallet
  
  ruleSelector:
    matchLabels:
      app: universalwallet
  
  resources:
    requests:
      memory: "2Gi"
      cpu: "1000m"
    limits:
      memory: "4Gi"
      cpu: "2000m"

  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 1000
```

### **Grafana Dashboard Configuration**
```yaml
# k8s/monitoring/grafana.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-password
        - name: GF_DATABASE_TYPE
          value: postgres
        - name: GF_DATABASE_HOST
          valueFrom:
            secretKeyRef:
              name: grafana-db-credentials
              key: host
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-dashboards
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-pvc
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
```

---

## 🚀 **Deployment Pipeline**

### **Production Deployment Script**
```bash
#!/bin/bash
# scripts/deploy-production.sh

set -euo pipefail

# Configuration
NAMESPACE="application"
RELEASE_VERSION="${1:-latest}"
CLUSTER_NAME="universalwallet-production"
REGION="af-south-1"

echo "🚀 Starting production deployment for version: ${RELEASE_VERSION}"

# Pre-deployment checks
echo "📋 Running pre-deployment checks..."
./scripts/pre-deployment-checks.sh

# Update kubeconfig
echo "🔧 Updating kubeconfig..."
aws eks update-kubeconfig --region ${REGION} --name ${CLUSTER_NAME}

# Database migrations
echo "🗄️ Running database migrations..."
kubectl apply -f k8s/jobs/migration-job.yaml
kubectl wait --for=condition=complete job/database-migration --timeout=600s

# Deploy backend services
echo "🔧 Deploying backend services..."
helm upgrade --install universalwallet-backend ./helm/backend \
  --namespace ${NAMESPACE} \
  --set image.tag=${RELEASE_VERSION} \
  --set replicaCount=6 \
  --values helm/backend/values-production.yaml \
  --wait --timeout=600s

# Deploy frontend services
echo "🌐 Deploying frontend services..."
helm upgrade --install universalwallet-web ./helm/web \
  --namespace ${NAMESPACE} \
  --set image.tag=${RELEASE_VERSION} \
  --set replicaCount=4 \
  --values helm/web/values-production.yaml \
  --wait --timeout=300s

# Health checks
echo "🏥 Running health checks..."
./scripts/health-checks.sh

# Smoke tests
echo "🧪 Running smoke tests..."
./scripts/smoke-tests.sh

echo "✅ Production deployment completed successfully!"
```

**This comprehensive production deployment configuration ensures enterprise-level reliability, security, and scalability for the UniversalWallet platform in a production environment.** 🚀
