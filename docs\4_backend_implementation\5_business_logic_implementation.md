# 5. Business Logic Implementation

## 🏢 **Business Logic Overview**

The Taxicab platform's business logic encompasses ride matching algorithms, fare calculations, driver earnings management, taxi association integration, and corporate client features. This document covers the implementation of core business rules and processes.

### **Business Logic Principles**
- **Fair Matching**: Equitable ride distribution among available drivers
- **Transparent Pricing**: Clear fare calculations with surge pricing
- **Commission Management**: Accurate earnings distribution between platform, associations, and drivers
- **Quality Control**: Rating systems and performance monitoring
- **Compliance**: Adherence to local transportation regulations

## 🚗 **Ride Matching Algorithm**

### **1. Driver Matching Service**
```typescript
// packages/api-client/src/services/driverMatchingService.ts
export interface DriverMatchingCriteria {
  pickup_location: Coordinates;
  service_type: string;
  max_distance_km: number;
  max_drivers: number;
  preferred_associations?: string[];
}

export interface MatchedDriver {
  driver_id: string;
  distance_km: number;
  rating: number;
  total_rides: number;
  vehicle_info: {
    make: string;
    model: string;
    color: string;
    license_plate: string;
    category: string;
  };
  association_name?: string;
  estimated_arrival_minutes: number;
}

export class DriverMatchingService {
  /**
   * Find and rank available drivers for a ride request
   */
  static async findAvailableDrivers(
    criteria: DriverMatchingCriteria
  ): Promise<MatchedDriver[]> {
    try {
      // Get nearby drivers using database function
      const { data: nearbyDrivers, error } = await supabase
        .rpc('find_nearby_drivers', {
          pickup_point: `POINT(${criteria.pickup_location.longitude} ${criteria.pickup_location.latitude})`,
          radius_km: criteria.max_distance_km,
          service_category: this.getVehicleCategoryForService(criteria.service_type),
          max_results: criteria.max_drivers * 2, // Get more for filtering
        });

      if (error) {
        throw new Error(`Driver search failed: ${error.message}`);
      }

      if (!nearbyDrivers || nearbyDrivers.length === 0) {
        return [];
      }

      // Apply business rules and ranking
      const rankedDrivers = await this.rankDrivers(nearbyDrivers, criteria);
      
      return rankedDrivers.slice(0, criteria.max_drivers);
    } catch (error) {
      console.error('Error finding available drivers:', error);
      throw error;
    }
  }

  /**
   * Rank drivers based on business rules
   */
  private static async rankDrivers(
    drivers: any[],
    criteria: DriverMatchingCriteria
  ): Promise<MatchedDriver[]> {
    const rankedDrivers: MatchedDriver[] = [];

    for (const driver of drivers) {
      // Get additional driver information
      const driverDetails = await this.getDriverDetails(driver.driver_id);
      
      if (!driverDetails || !this.isDriverEligible(driverDetails)) {
        continue;
      }

      // Calculate ranking score
      const score = this.calculateDriverScore(driver, driverDetails, criteria);
      
      const matchedDriver: MatchedDriver = {
        driver_id: driver.driver_id,
        distance_km: driver.distance_km,
        rating: driver.driver_rating,
        total_rides: driverDetails.total_rides,
        vehicle_info: driver.vehicle_info,
        association_name: driverDetails.association_name,
        estimated_arrival_minutes: Math.ceil(driver.distance_km * 2), // Rough estimate
        score, // Internal scoring for sorting
      };

      rankedDrivers.push(matchedDriver);
    }

    // Sort by score (highest first)
    return rankedDrivers.sort((a, b) => (b as any).score - (a as any).score);
  }

  /**
   * Calculate driver ranking score
   */
  private static calculateDriverScore(
    driver: any,
    driverDetails: any,
    criteria: DriverMatchingCriteria
  ): number {
    let score = 0;

    // Distance factor (closer is better) - 40% weight
    const maxDistance = criteria.max_distance_km;
    const distanceScore = Math.max(0, (maxDistance - driver.distance_km) / maxDistance);
    score += distanceScore * 40;

    // Rating factor - 30% weight
    const ratingScore = (driver.driver_rating || 0) / 5;
    score += ratingScore * 30;

    // Experience factor (total rides) - 20% weight
    const experienceScore = Math.min(1, (driverDetails.total_rides || 0) / 100);
    score += experienceScore * 20;

    // Association preference - 10% weight
    if (criteria.preferred_associations?.includes(driverDetails.association_id)) {
      score += 10;
    }

    // Bonus for highly rated drivers
    if (driver.driver_rating >= 4.5) {
      score += 5;
    }

    // Penalty for drivers with low acceptance rate
    if (driverDetails.acceptance_rate < 0.8) {
      score -= 10;
    }

    return Math.max(0, score);
  }

  /**
   * Check if driver is eligible for ride requests
   */
  private static isDriverEligible(driverDetails: any): boolean {
    return (
      driverDetails.status === 'approved' &&
      driverDetails.is_online &&
      driverDetails.vehicle_active &&
      !driverDetails.is_suspended &&
      driverDetails.last_location_update > new Date(Date.now() - 5 * 60 * 1000) // Active within 5 minutes
    );
  }

  /**
   * Get detailed driver information
   */
  private static async getDriverDetails(driverId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('drivers')
        .select(`
          *,
          association:taxi_associations(name),
          vehicle:vehicles(*)
        `)
        .eq('id', driverId)
        .single();

      if (error) {
        console.error('Error fetching driver details:', error);
        return null;
      }

      return {
        ...data,
        association_name: data.association?.name,
        vehicle_active: data.vehicle?.is_active,
      };
    } catch (error) {
      console.error('Error in getDriverDetails:', error);
      return null;
    }
  }

  private static getVehicleCategoryForService(serviceType: string): string {
    const mapping = {
      'movers_ride': 'standard',
      'movers_exec': 'executive',
      'movers_xl': 'xl',
      'movers_cruiser': 'standard',
      'movers_express': 'lite',
    };
    
    return mapping[serviceType as keyof typeof mapping] || 'standard';
  }
}
```

### **2. Ride Request Distribution**
```typescript
// packages/api-client/src/services/rideDistributionService.ts
export class RideDistributionService {
  /**
   * Distribute ride request to matched drivers
   */
  static async distributeRideRequest(
    rideId: string,
    matchedDrivers: MatchedDriver[]
  ): Promise<void> {
    try {
      if (matchedDrivers.length === 0) {
        await this.handleNoDriversAvailable(rideId);
        return;
      }

      // Send requests in batches to avoid overwhelming drivers
      const batchSize = 3;
      const batches = this.createDriverBatches(matchedDrivers, batchSize);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const timeout = i === 0 ? 30000 : 20000; // First batch gets 30s, others 20s

        const accepted = await this.sendRideRequestBatch(rideId, batch, timeout);
        
        if (accepted) {
          // Ride was accepted, cancel remaining requests
          await this.cancelPendingRequests(rideId, batches.slice(i + 1).flat());
          return;
        }
      }

      // No driver accepted the ride
      await this.handleRideRejection(rideId);
    } catch (error) {
      console.error('Error distributing ride request:', error);
      throw error;
    }
  }

  /**
   * Send ride request to a batch of drivers
   */
  private static async sendRideRequestBatch(
    rideId: string,
    drivers: MatchedDriver[],
    timeoutMs: number
  ): Promise<boolean> {
    try {
      // Send notifications to all drivers in batch
      const notificationPromises = drivers.map(driver =>
        this.sendRideRequestNotification(rideId, driver)
      );

      await Promise.all(notificationPromises);

      // Wait for acceptance or timeout
      return await this.waitForRideAcceptance(rideId, timeoutMs);
    } catch (error) {
      console.error('Error sending ride request batch:', error);
      return false;
    }
  }

  /**
   * Send ride request notification to driver
   */
  private static async sendRideRequestNotification(
    rideId: string,
    driver: MatchedDriver
  ): Promise<void> {
    try {
      // Get ride details
      const { data: ride, error } = await supabase
        .from('rides')
        .select('*')
        .eq('id', rideId)
        .single();

      if (error || !ride) {
        throw new Error('Ride not found');
      }

      // Get driver's user ID
      const { data: driverData, error: driverError } = await supabase
        .from('drivers')
        .select('user_id')
        .eq('id', driver.driver_id)
        .single();

      if (driverError || !driverData) {
        throw new Error('Driver not found');
      }

      // Send notification
      await supabase
        .from('notifications')
        .insert({
          user_id: driverData.user_id,
          type: 'ride_request',
          title: 'New Ride Request',
          message: `Ride request: ${ride.pickup_address} → ${ride.destination_address}`,
          data: {
            ride_id: rideId,
            pickup_address: ride.pickup_address,
            destination_address: ride.destination_address,
            estimated_fare: ride.estimated_fare,
            distance_km: ride.distance_km,
            service_type: ride.service_type,
            estimated_arrival: driver.estimated_arrival_minutes,
          },
        });
    } catch (error) {
      console.error('Error sending ride request notification:', error);
      // Don't throw error to avoid stopping the batch
    }
  }

  /**
   * Wait for ride acceptance with timeout
   */
  private static async waitForRideAcceptance(
    rideId: string,
    timeoutMs: number
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        cleanup();
        resolve(false);
      }, timeoutMs);

      const channel = supabase
        .channel(`ride-acceptance:${rideId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'rides',
            filter: `id=eq.${rideId}`,
          },
          (payload) => {
            if (payload.new.status === 'accepted') {
              cleanup();
              resolve(true);
            }
          }
        )
        .subscribe();

      const cleanup = () => {
        clearTimeout(timeout);
        supabase.removeChannel(channel);
      };
    });
  }

  private static createDriverBatches(
    drivers: MatchedDriver[],
    batchSize: number
  ): MatchedDriver[][] {
    const batches: MatchedDriver[][] = [];
    for (let i = 0; i < drivers.length; i += batchSize) {
      batches.push(drivers.slice(i, i + batchSize));
    }
    return batches;
  }

  private static async handleNoDriversAvailable(rideId: string): Promise<void> {
    await supabase
      .from('rides')
      .update({
        status: 'cancelled',
        cancellation_reason: 'No drivers available in your area',
        cancelled_at: new Date().toISOString(),
      })
      .eq('id', rideId);
  }

  private static async handleRideRejection(rideId: string): Promise<void> {
    await supabase
      .from('rides')
      .update({
        status: 'cancelled',
        cancellation_reason: 'No drivers accepted the ride request',
        cancelled_at: new Date().toISOString(),
      })
      .eq('id', rideId);
  }

  private static async cancelPendingRequests(
    rideId: string,
    remainingDrivers: MatchedDriver[]
  ): Promise<void> {
    // Implementation to cancel notifications for remaining drivers
    // This could involve updating a ride_requests table or sending cancellation notifications
  }
}
```

## 💰 **Fare Calculation and Pricing**

### **1. Dynamic Pricing Engine**
```typescript
// packages/api-client/src/services/pricingService.ts
export interface PricingFactors {
  base_fare: number;
  rate_per_km: number;
  rate_per_minute: number;
  surge_multiplier: number;
  service_fee: number;
  taxes: number;
}

export interface FareBreakdown {
  base_fare: number;
  distance_fare: number;
  time_fare: number;
  service_fee: number;
  surge_amount: number;
  taxes: number;
  subtotal: number;
  total_fare: number;
}

export class PricingService {
  /**
   * Calculate comprehensive fare with breakdown
   */
  static async calculateFare(
    distance_km: number,
    duration_minutes: number,
    service_type: string,
    pickup_time?: Date,
    special_events?: string[]
  ): Promise<FareBreakdown> {
    try {
      // Get base pricing for service type
      const basePricing = this.getBasePricing(service_type);
      
      // Calculate surge multiplier
      const surgeMultiplier = await this.calculateSurgeMultiplier(
        pickup_time || new Date(),
        special_events
      );

      // Calculate fare components
      const baseFare = basePricing.base_fare;
      const distanceFare = distance_km * basePricing.rate_per_km;
      const timeFare = duration_minutes * basePricing.rate_per_minute;
      const serviceFee = basePricing.service_fee;

      // Calculate subtotal before surge
      const subtotal = baseFare + distanceFare + timeFare + serviceFee;
      
      // Apply surge pricing
      const surgeAmount = subtotal * (surgeMultiplier - 1);
      
      // Calculate taxes (15% VAT in Zimbabwe)
      const taxableAmount = subtotal + surgeAmount;
      const taxes = taxableAmount * 0.15;
      
      // Final total
      const totalFare = taxableAmount + taxes;

      return {
        base_fare: baseFare,
        distance_fare: distanceFare,
        time_fare: timeFare,
        service_fee: serviceFee,
        surge_amount: surgeAmount,
        taxes: taxes,
        subtotal: subtotal,
        total_fare: Math.round(totalFare * 100) / 100, // Round to 2 decimal places
      };
    } catch (error) {
      console.error('Error calculating fare:', error);
      throw error;
    }
  }

  /**
   * Calculate surge multiplier based on demand and time
   */
  private static async calculateSurgeMultiplier(
    pickupTime: Date,
    specialEvents?: string[]
  ): Promise<number> {
    let surgeMultiplier = 1.0;

    // Time-based surge pricing
    const hour = pickupTime.getHours();
    const dayOfWeek = pickupTime.getDay();

    // Peak hours surge (6-9 AM, 5-8 PM on weekdays)
    if (dayOfWeek >= 1 && dayOfWeek <= 5) { // Monday to Friday
      if ((hour >= 6 && hour <= 9) || (hour >= 17 && hour <= 20)) {
        surgeMultiplier = Math.max(surgeMultiplier, 1.3);
      }
    }

    // Weekend evening surge (Friday/Saturday 8 PM - 2 AM)
    if ((dayOfWeek === 5 && hour >= 20) || (dayOfWeek === 6 && (hour >= 20 || hour <= 2))) {
      surgeMultiplier = Math.max(surgeMultiplier, 1.5);
    }

    // Special events surge
    if (specialEvents && specialEvents.length > 0) {
      surgeMultiplier = Math.max(surgeMultiplier, 2.0);
    }

    // Real-time demand surge (check current demand vs supply)
    const demandSurge = await this.calculateDemandSurge();
    surgeMultiplier = Math.max(surgeMultiplier, demandSurge);

    // Cap surge at 3.0x
    return Math.min(surgeMultiplier, 3.0);
  }

  /**
   * Calculate demand-based surge pricing
   */
  private static async calculateDemandSurge(): Promise<number> {
    try {
      // Get current ride requests vs available drivers
      const { data: stats, error } = await supabase
        .rpc('get_demand_supply_ratio');

      if (error || !stats) {
        return 1.0; // Default to no surge if calculation fails
      }

      const { pending_rides, available_drivers } = stats;
      
      if (available_drivers === 0) {
        return 2.5; // High surge when no drivers available
      }

      const ratio = pending_rides / available_drivers;
      
      if (ratio >= 3) return 2.0;      // Very high demand
      if (ratio >= 2) return 1.7;      // High demand
      if (ratio >= 1.5) return 1.4;    // Moderate demand
      if (ratio >= 1) return 1.2;      // Slight demand
      
      return 1.0; // No surge
    } catch (error) {
      console.error('Error calculating demand surge:', error);
      return 1.0;
    }
  }

  /**
   * Get base pricing for service type
   */
  private static getBasePricing(serviceType: string): PricingFactors {
    const pricingTable: Record<string, PricingFactors> = {
      'movers_ride': {
        base_fare: 2.00,
        rate_per_km: 0.50,
        rate_per_minute: 0.10,
        surge_multiplier: 1.0,
        service_fee: 0.50,
        taxes: 0.15,
      },
      'movers_exec': {
        base_fare: 3.00,
        rate_per_km: 0.75,
        rate_per_minute: 0.15,
        surge_multiplier: 1.0,
        service_fee: 0.75,
        taxes: 0.15,
      },
      'movers_xl': {
        base_fare: 2.50,
        rate_per_km: 0.60,
        rate_per_minute: 0.12,
        surge_multiplier: 1.0,
        service_fee: 0.60,
        taxes: 0.15,
      },
      'movers_cruiser': {
        base_fare: 2.00,
        rate_per_km: 0.45,
        rate_per_minute: 0.08,
        surge_multiplier: 1.0,
        service_fee: 0.40,
        taxes: 0.15,
      },
      'movers_express': {
        base_fare: 1.50,
        rate_per_km: 0.40,
        rate_per_minute: 0.08,
        surge_multiplier: 1.0,
        service_fee: 0.30,
        taxes: 0.15,
      },
    };

    return pricingTable[serviceType] || pricingTable['movers_ride'];
  }
}
```

## 🏢 **Commission and Earnings Management**

### **1. Earnings Distribution Service**
```typescript
// packages/api-client/src/services/earningsService.ts
export interface EarningsBreakdown {
  gross_amount: number;
  platform_commission: number;
  association_commission: number;
  net_earnings: number;
  commission_rate: number;
  association_rate: number;
}

export class EarningsService {
  /**
   * Calculate driver earnings after commissions
   */
  static async calculateDriverEarnings(
    rideId: string,
    grossAmount: number
  ): Promise<EarningsBreakdown> {
    try {
      // Get ride and driver information
      const { data: ride, error: rideError } = await supabase
        .from('rides')
        .select(`
          *,
          driver:drivers(
            id,
            association_id,
            association:taxi_associations(commission_rate)
          )
        `)
        .eq('id', rideId)
        .single();

      if (rideError || !ride) {
        throw new Error('Ride not found');
      }

      // Get commission rates
      const platformRate = await this.getPlatformCommissionRate(ride.driver.id);
      const associationRate = ride.driver.association?.commission_rate || 0;

      // Calculate commissions
      const platformCommission = grossAmount * (platformRate / 100);
      const associationCommission = grossAmount * (associationRate / 100);
      const netEarnings = grossAmount - platformCommission - associationCommission;

      const breakdown: EarningsBreakdown = {
        gross_amount: grossAmount,
        platform_commission: platformCommission,
        association_commission: associationCommission,
        net_earnings: Math.max(0, netEarnings), // Ensure non-negative
        commission_rate: platformRate,
        association_rate: associationRate,
      };

      // Record earnings in database
      await this.recordDriverEarnings(ride.driver.id, rideId, breakdown);

      return breakdown;
    } catch (error) {
      console.error('Error calculating driver earnings:', error);
      throw error;
    }
  }

  /**
   * Get platform commission rate for driver (may vary based on performance)
   */
  private static async getPlatformCommissionRate(driverId: string): Promise<number> {
    try {
      // Get driver performance metrics
      const { data: driver, error } = await supabase
        .from('drivers')
        .select('rating, total_rides')
        .eq('id', driverId)
        .single();

      if (error || !driver) {
        return 20; // Default 20% commission
      }

      // Performance-based commission rates
      if (driver.rating >= 4.8 && driver.total_rides >= 100) {
        return 15; // Premium drivers get lower commission
      } else if (driver.rating >= 4.5 && driver.total_rides >= 50) {
        return 17; // Good drivers get reduced commission
      } else if (driver.total_rides < 10) {
        return 25; // New drivers pay higher commission initially
      }

      return 20; // Standard commission rate
    } catch (error) {
      console.error('Error getting commission rate:', error);
      return 20;
    }
  }

  /**
   * Record driver earnings in database
   */
  private static async recordDriverEarnings(
    driverId: string,
    rideId: string,
    breakdown: EarningsBreakdown
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('driver_earnings')
        .insert({
          driver_id: driverId,
          ride_id: rideId,
          gross_amount: breakdown.gross_amount,
          platform_commission: breakdown.platform_commission,
          association_commission: breakdown.association_commission,
          net_earnings: breakdown.net_earnings,
          commission_rate: breakdown.commission_rate,
        });

      if (error) {
        throw new Error(`Failed to record earnings: ${error.message}`);
      }

      // Update driver's total earnings
      await this.updateDriverTotalEarnings(driverId, breakdown.net_earnings);
    } catch (error) {
      console.error('Error recording driver earnings:', error);
      throw error;
    }
  }

  /**
   * Update driver's total earnings counter
   */
  private static async updateDriverTotalEarnings(
    driverId: string,
    additionalEarnings: number
  ): Promise<void> {
    try {
      const { error } = await supabase
        .rpc('increment_driver_earnings', {
          driver_id: driverId,
          amount: additionalEarnings,
        });

      if (error) {
        console.error('Error updating total earnings:', error);
      }
    } catch (error) {
      console.error('Error in updateDriverTotalEarnings:', error);
    }
  }

  /**
   * Get driver earnings summary for a period
   */
  static async getDriverEarningsSummary(
    driverId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any> {
    try {
      const { data, error } = await supabase
        .rpc('get_driver_earnings_summary', {
          driver_id: driverId,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        });

      if (error) {
        throw new Error(`Failed to get earnings summary: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error getting earnings summary:', error);
      throw error;
    }
  }
}
```

This comprehensive business logic implementation provides robust algorithms for ride matching, dynamic pricing, and earnings management that ensure fair and efficient platform operations while maintaining profitability for all stakeholders.
