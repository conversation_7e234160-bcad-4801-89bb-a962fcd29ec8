# 3. Version Control and Workflow

## 🔄 **Version Control Overview**

This document establishes the comprehensive version control strategy and development workflow for the UniversalWallet platform, ensuring efficient collaboration, code quality, and release management across all development teams.

## 🌳 **Git Branching Strategy**

### **GitFlow Workflow**

#### **Branch Types and Purposes**
```
main (production)
├── develop (integration)
│   ├── feature/user-authentication
│   ├── feature/transaction-processing
│   ├── feature/agent-dashboard
│   └── feature/business-portal
├── release/v1.0.0
├── hotfix/critical-security-patch
└── support/v0.9.x
```

#### **Branch Naming Conventions**
- **Feature branches**: `feature/JIRA-123-short-description`
- **Bugfix branches**: `bugfix/JIRA-456-fix-description`
- **Release branches**: `release/v1.0.0`
- **Hotfix branches**: `hotfix/v1.0.1-critical-fix`
- **Support branches**: `support/v1.0.x`

### **Branch Lifecycle**

#### **Feature Development Flow**
```bash
# 1. Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/JIRA-123-user-authentication

# 2. Work on feature with regular commits
git add .
git commit -m "feat(auth): implement JWT token validation

- Add JWT token validation middleware
- Implement token refresh mechanism
- Add comprehensive error handling

Closes JIRA-123"

# 3. Keep feature branch updated
git checkout develop
git pull origin develop
git checkout feature/JIRA-123-user-authentication
git rebase develop

# 4. Push feature branch
git push origin feature/JIRA-123-user-authentication

# 5. Create Pull Request to develop
# 6. After review and approval, merge to develop
# 7. Delete feature branch
git branch -d feature/JIRA-123-user-authentication
git push origin --delete feature/JIRA-123-user-authentication
```

#### **Release Flow**
```bash
# 1. Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# 2. Finalize release (version bumps, documentation)
npm version 1.0.0
git add .
git commit -m "chore(release): prepare v1.0.0"

# 3. Merge to main and tag
git checkout main
git merge release/v1.0.0
git tag -a v1.0.0 -m "Release version 1.0.0"

# 4. Merge back to develop
git checkout develop
git merge release/v1.0.0

# 5. Push all changes
git push origin main develop --tags

# 6. Delete release branch
git branch -d release/v1.0.0
git push origin --delete release/v1.0.0
```

---

## 📝 **Commit Message Standards**

### **Conventional Commits Format**
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### **Commit Types**
- **feat**: New feature for the user
- **fix**: Bug fix for the user
- **docs**: Documentation changes
- **style**: Code style changes (formatting, missing semicolons, etc.)
- **refactor**: Code refactoring without changing functionality
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **chore**: Maintenance tasks, dependency updates
- **ci**: CI/CD pipeline changes
- **build**: Build system or external dependency changes

### **Commit Message Examples**
```bash
# Feature commit
git commit -m "feat(auth): add biometric authentication support

- Implement fingerprint authentication for mobile app
- Add face recognition for supported devices
- Include fallback to PIN authentication
- Update security documentation

Closes JIRA-234"

# Bug fix commit
git commit -m "fix(transaction): resolve duplicate transaction issue

- Add transaction idempotency check
- Implement proper error handling for network timeouts
- Update transaction status validation

Fixes JIRA-567"

# Breaking change commit
git commit -m "feat(api): update authentication endpoint

BREAKING CHANGE: Authentication endpoint now requires
additional security headers for enhanced protection.

Migration guide:
- Add X-Device-ID header to all auth requests
- Include X-App-Version header with app version

Closes JIRA-890"
```

---

## 🔍 **Code Review Process**

### **Pull Request Requirements**

#### **PR Template**
```markdown
## Description
Brief description of changes made in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance testing (if applicable)

## Security Checklist
- [ ] No sensitive data exposed in logs
- [ ] Input validation implemented
- [ ] Authentication/authorization checks in place
- [ ] SQL injection prevention measures

## Screenshots (if applicable)
Add screenshots or GIFs to demonstrate changes.

## Related Issues
Closes #123
Related to #456

## Deployment Notes
Any special deployment considerations or database migrations required.
```

#### **Review Checklist**
```markdown
### Code Quality
- [ ] Code follows established coding standards
- [ ] Functions and classes have single responsibility
- [ ] Code is well-documented with clear comments
- [ ] No code duplication (DRY principle)
- [ ] Error handling is comprehensive

### Security
- [ ] Input validation is implemented
- [ ] No sensitive data in logs or comments
- [ ] Authentication and authorization checks
- [ ] SQL injection prevention
- [ ] XSS prevention measures

### Performance
- [ ] Database queries are optimized
- [ ] No N+1 query problems
- [ ] Appropriate caching implemented
- [ ] Memory usage is reasonable

### Testing
- [ ] Unit tests cover new functionality
- [ ] Integration tests are updated
- [ ] Edge cases are tested
- [ ] Test coverage meets minimum requirements (80%)

### Documentation
- [ ] API documentation updated
- [ ] README files updated if needed
- [ ] Inline code documentation
- [ ] Architecture decisions documented
```

### **Review Process Flow**
```
1. Developer creates PR
   ↓
2. Automated checks run (CI/CD)
   ↓
3. Code review assignment (2 reviewers minimum)
   ↓
4. Reviewers provide feedback
   ↓
5. Developer addresses feedback
   ↓
6. Re-review if significant changes
   ↓
7. Approval from all reviewers
   ↓
8. Merge to target branch
   ↓
9. Automated deployment (if applicable)
```

---

## 🤖 **Automated Workflows**

### **GitHub Actions Workflows**

#### **CI/CD Pipeline (.github/workflows/ci.yml)**
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: universalwallet_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: Run tests
      run: mvn clean test
    
    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Maven Tests
        path: target/surefire-reports/*.xml
        reporter: java-junit
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: target/site/jacoco/jacoco.xml

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/maven@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    
    - name: Run OWASP Dependency Check
      run: |
        mvn org.owasp:dependency-check-maven:check
    
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: target/dependency-check-report.sarif

  build-and-deploy:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t universalwallet:${{ github.sha }} .
    
    - name: Deploy to staging
      if: github.ref == 'refs/heads/develop'
      run: |
        # Deploy to staging environment
        echo "Deploying to staging..."
    
    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        # Deploy to production environment
        echo "Deploying to production..."
```

#### **Frontend Workflow (.github/workflows/frontend.yml)**
```yaml
name: Frontend CI/CD

on:
  push:
    branches: [ main, develop ]
    paths: [ 'frontend/**' ]
  pull_request:
    branches: [ main, develop ]
    paths: [ 'frontend/**' ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
    
    - name: Run linting
      working-directory: frontend
      run: npm run lint
    
    - name: Run type checking
      working-directory: frontend
      run: npm run type-check
    
    - name: Run unit tests
      working-directory: frontend
      run: npm run test:coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        directory: frontend/coverage
    
    - name: Build application
      working-directory: frontend
      run: npm run build
    
    - name: Run E2E tests
      working-directory: frontend
      run: npm run test:e2e
```

---

## 🏷️ **Release Management**

### **Semantic Versioning**
```
MAJOR.MINOR.PATCH

Examples:
- 1.0.0 - Initial release
- 1.1.0 - New features added
- 1.1.1 - Bug fixes
- 2.0.0 - Breaking changes
```

### **Release Process**

#### **Release Checklist**
```markdown
### Pre-Release
- [ ] All features for release are merged to develop
- [ ] All tests are passing
- [ ] Security scan completed
- [ ] Performance testing completed
- [ ] Documentation updated
- [ ] Release notes prepared

### Release Creation
- [ ] Create release branch from develop
- [ ] Update version numbers
- [ ] Update CHANGELOG.md
- [ ] Create release PR to main
- [ ] Get approval from tech lead and product owner

### Post-Release
- [ ] Tag release in Git
- [ ] Deploy to production
- [ ] Merge release branch back to develop
- [ ] Create GitHub release with notes
- [ ] Notify stakeholders
- [ ] Monitor production for issues
```

#### **Hotfix Process**
```bash
# 1. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/v1.0.1-critical-security-fix

# 2. Make necessary fixes
git add .
git commit -m "fix(security): patch critical vulnerability

- Fix SQL injection in user query
- Add input sanitization
- Update security tests

Fixes SECURITY-001"

# 3. Merge to main and develop
git checkout main
git merge hotfix/v1.0.1-critical-security-fix
git tag -a v1.0.1 -m "Hotfix version 1.0.1"

git checkout develop
git merge hotfix/v1.0.1-critical-security-fix

# 4. Deploy immediately
git push origin main develop --tags
```

---

## 📊 **Git Hooks and Automation**

### **Pre-commit Hooks**
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Run linting
npm run lint
if [ $? -ne 0 ]; then
  echo "Linting failed. Please fix errors before committing."
  exit 1
fi

# Run unit tests
npm run test:unit
if [ $? -ne 0 ]; then
  echo "Unit tests failed. Please fix tests before committing."
  exit 1
fi

# Check commit message format
commit_regex='^(feat|fix|docs|style|refactor|perf|test|chore|ci|build)(\(.+\))?: .{1,50}'
commit_message=$(cat .git/COMMIT_EDITMSG)

if ! echo "$commit_message" | grep -qE "$commit_regex"; then
  echo "Invalid commit message format. Please use conventional commits."
  exit 1
fi
```

### **Pre-push Hooks**
```bash
#!/bin/sh
# .git/hooks/pre-push

# Run integration tests
npm run test:integration
if [ $? -ne 0 ]; then
  echo "Integration tests failed. Please fix before pushing."
  exit 1
fi

# Security scan
npm audit --audit-level high
if [ $? -ne 0 ]; then
  echo "Security vulnerabilities found. Please fix before pushing."
  exit 1
fi
```

**This comprehensive version control and workflow strategy ensures efficient, secure, and high-quality development practices across the entire UniversalWallet platform.** 🔄
