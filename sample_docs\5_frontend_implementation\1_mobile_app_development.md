# 1. Mobile App Development

## 📱 **Mobile App Overview**

This document provides comprehensive implementation guidelines for the UniversalWallet React Native mobile application, covering architecture, components, navigation, state management, and platform-specific features for both iOS and Android.

## 🏗️ **React Native Architecture**

### **Project Structure**
```
src/
├── components/           # Reusable UI components
│   ├── common/          # Common components (Button, Input, etc.)
│   ├── forms/           # Form components
│   ├── cards/           # Card components
│   └── modals/          # Modal components
├── screens/             # Screen components
│   ├── auth/            # Authentication screens
│   ├── dashboard/       # Dashboard screens
│   ├── transactions/    # Transaction screens
│   ├── accounts/        # Account management screens
│   ├── profile/         # Profile screens
│   └── settings/        # Settings screens
├── navigation/          # Navigation configuration
├── services/            # API services and external integrations
├── store/               # Redux store configuration
│   ├── slices/          # Redux Toolkit slices
│   └── middleware/      # Custom middleware
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
├── constants/           # App constants
├── assets/              # Images, fonts, etc.
└── styles/              # Global styles and themes
```

### **App Entry Point**
```typescript
// App.tsx
import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StatusBar } from 'expo-status-bar';

import { store, persistor } from './src/store';
import { AppNavigator } from './src/navigation/AppNavigator';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { AuthProvider } from './src/contexts/AuthContext';
import { NotificationProvider } from './src/contexts/NotificationContext';
import { LoadingScreen } from './src/components/common/LoadingScreen';
import { ErrorBoundary } from './src/components/common/ErrorBoundary';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize app services
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize crash reporting
      // Initialize analytics
      // Initialize push notifications
      // Initialize biometric authentication
    } catch (error) {
      console.error('App initialization failed:', error);
    }
  };

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <PersistGate loading={<LoadingScreen />} persistor={persistor}>
          <SafeAreaProvider>
            <GestureHandlerRootView style={{ flex: 1 }}>
              <ThemeProvider>
                <AuthProvider>
                  <NotificationProvider>
                    <NavigationContainer>
                      <AppNavigator />
                      <StatusBar style="auto" />
                    </NavigationContainer>
                  </NotificationProvider>
                </AuthProvider>
              </ThemeProvider>
            </GestureHandlerRootView>
          </SafeAreaProvider>
        </PersistGate>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
```

---

## 🧭 **Navigation Implementation**

### **Navigation Structure**
```typescript
// navigation/AppNavigator.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useSelector } from 'react-redux';

import { AuthNavigator } from './AuthNavigator';
import { MainTabNavigator } from './MainTabNavigator';
import { RootState } from '../store';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  TransactionDetails: { transactionId: string };
  QRScanner: undefined;
  BiometricSetup: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {isAuthenticated ? (
        <Stack.Screen name="Main" component={MainTabNavigator} />
      ) : (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      )}
      <Stack.Screen 
        name="TransactionDetails" 
        component={TransactionDetailsScreen}
        options={{ presentation: 'modal' }}
      />
      <Stack.Screen 
        name="QRScanner" 
        component={QRScannerScreen}
        options={{ presentation: 'fullScreenModal' }}
      />
    </Stack.Navigator>
  );
};

// navigation/MainTabNavigator.tsx
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import { DashboardScreen } from '../screens/dashboard/DashboardScreen';
import { TransactionsScreen } from '../screens/transactions/TransactionsScreen';
import { AccountsScreen } from '../screens/accounts/AccountsScreen';
import { ProfileScreen } from '../screens/profile/ProfileScreen';

export type MainTabParamList = {
  Dashboard: undefined;
  Transactions: undefined;
  Accounts: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

export const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Transactions':
              iconName = focused ? 'swap-horizontal' : 'swap-horizontal-outline';
              break;
            case 'Accounts':
              iconName = focused ? 'wallet' : 'wallet-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'circle';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Transactions" component={TransactionsScreen} />
      <Tab.Screen name="Accounts" component={AccountsScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};
```

---

## 🔐 **Authentication Screens**

### **Login Screen**
```typescript
// screens/auth/LoginScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Formik } from 'formik';
import * as Yup from 'yup';
import * as LocalAuthentication from 'expo-local-authentication';

import { AppDispatch, RootState } from '../../store';
import { loginUser, loginWithBiometrics } from '../../store/slices/authSlice';
import { Button } from '../../components/common/Button';
import { TextInput } from '../../components/common/TextInput';
import { PinInput } from '../../components/common/PinInput';
import { BiometricButton } from '../../components/auth/BiometricButton';

const loginSchema = Yup.object().shape({
  phoneNumber: Yup.string()
    .matches(/^\+263[0-9]{9}$/, 'Invalid phone number format')
    .required('Phone number is required'),
  pin: Yup.string()
    .length(4, 'PIN must be 4 digits')
    .matches(/^\d+$/, 'PIN must contain only numbers')
    .required('PIN is required'),
});

interface LoginFormValues {
  phoneNumber: string;
  pin: string;
}

export const LoginScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    setBiometricAvailable(hasHardware && isEnrolled);
  };

  const handleLogin = async (values: LoginFormValues) => {
    try {
      await dispatch(loginUser(values)).unwrap();
    } catch (error) {
      Alert.alert('Login Failed', error as string);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate with biometrics',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        await dispatch(loginWithBiometrics()).unwrap();
      }
    } catch (error) {
      Alert.alert('Biometric Authentication Failed', error as string);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        <Text style={styles.title}>Welcome Back</Text>
        <Text style={styles.subtitle}>Sign in to your UniversalWallet</Text>

        <Formik
          initialValues={{ phoneNumber: '', pin: '' }}
          validationSchema={loginSchema}
          onSubmit={handleLogin}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
            <View style={styles.form}>
              <TextInput
                label="Phone Number"
                placeholder="+************"
                value={values.phoneNumber}
                onChangeText={handleChange('phoneNumber')}
                onBlur={handleBlur('phoneNumber')}
                error={touched.phoneNumber && errors.phoneNumber}
                keyboardType="phone-pad"
                autoCapitalize="none"
              />

              <PinInput
                label="PIN"
                value={values.pin}
                onChangeText={handleChange('pin')}
                error={touched.pin && errors.pin}
                secureTextEntry
              />

              <Button
                title="Sign In"
                onPress={handleSubmit}
                loading={isLoading}
                style={styles.loginButton}
              />

              {biometricAvailable && (
                <BiometricButton
                  onPress={handleBiometricLogin}
                  style={styles.biometricButton}
                />
              )}
            </View>
          )}
        </Formik>

        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: '#666',
  },
  form: {
    marginBottom: 20,
  },
  loginButton: {
    marginTop: 20,
  },
  biometricButton: {
    marginTop: 16,
  },
  errorText: {
    color: '#ff3b30',
    textAlign: 'center',
    marginTop: 10,
  },
});
```

### **Registration Screen**
```typescript
// screens/auth/RegistrationScreen.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { AppDispatch, RootState } from '../../store';
import { registerUser } from '../../store/slices/authSlice';
import { Button } from '../../components/common/Button';
import { TextInput } from '../../components/common/TextInput';
import { PinInput } from '../../components/common/PinInput';
import { Checkbox } from '../../components/common/Checkbox';

const registrationSchema = Yup.object().shape({
  phoneNumber: Yup.string()
    .matches(/^\+263[0-9]{9}$/, 'Invalid phone number format')
    .required('Phone number is required'),
  pin: Yup.string()
    .length(4, 'PIN must be 4 digits')
    .matches(/^\d+$/, 'PIN must contain only numbers')
    .required('PIN is required'),
  confirmPin: Yup.string()
    .oneOf([Yup.ref('pin')], 'PINs must match')
    .required('Please confirm your PIN'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
});

interface RegistrationFormValues {
  phoneNumber: string;
  pin: string;
  confirmPin: string;
  termsAccepted: boolean;
}

export const RegistrationScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);

  const handleRegistration = async (values: RegistrationFormValues) => {
    try {
      await dispatch(registerUser({
        phoneNumber: values.phoneNumber,
        pin: values.pin,
        userType: 'personal',
      })).unwrap();
      
      Alert.alert(
        'Registration Successful',
        'Please check your phone for the OTP verification code.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Registration Failed', error as string);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        <Text style={styles.title}>Create Account</Text>
        <Text style={styles.subtitle}>Join UniversalWallet today</Text>

        <Formik
          initialValues={{
            phoneNumber: '',
            pin: '',
            confirmPin: '',
            termsAccepted: false,
          }}
          validationSchema={registrationSchema}
          onSubmit={handleRegistration}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors, touched, setFieldValue }) => (
            <View style={styles.form}>
              <TextInput
                label="Phone Number"
                placeholder="+************"
                value={values.phoneNumber}
                onChangeText={handleChange('phoneNumber')}
                onBlur={handleBlur('phoneNumber')}
                error={touched.phoneNumber && errors.phoneNumber}
                keyboardType="phone-pad"
                autoCapitalize="none"
              />

              <PinInput
                label="Create PIN"
                value={values.pin}
                onChangeText={handleChange('pin')}
                error={touched.pin && errors.pin}
                secureTextEntry
              />

              <PinInput
                label="Confirm PIN"
                value={values.confirmPin}
                onChangeText={handleChange('confirmPin')}
                error={touched.confirmPin && errors.confirmPin}
                secureTextEntry
              />

              <Checkbox
                label="I accept the Terms and Conditions and Privacy Policy"
                value={values.termsAccepted}
                onValueChange={(value) => setFieldValue('termsAccepted', value)}
                error={touched.termsAccepted && errors.termsAccepted}
              />

              <Button
                title="Create Account"
                onPress={handleSubmit}
                loading={isLoading}
                style={styles.registerButton}
              />
            </View>
          )}
        </Formik>

        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 20,
    paddingTop: 60,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: '#666',
  },
  form: {
    marginBottom: 20,
  },
  registerButton: {
    marginTop: 20,
  },
  errorText: {
    color: '#ff3b30',
    textAlign: 'center',
    marginTop: 10,
  },
});
```

---

## 🏠 **Dashboard Implementation**

### **Dashboard Screen**
```typescript
// screens/dashboard/DashboardScreen.tsx
import React, { useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';

import { AppDispatch, RootState } from '../../store';
import { fetchAccountBalances } from '../../store/slices/accountSlice';
import { fetchRecentTransactions } from '../../store/slices/transactionSlice';
import { BalanceCard } from '../../components/dashboard/BalanceCard';
import { QuickActions } from '../../components/dashboard/QuickActions';
import { RecentTransactions } from '../../components/dashboard/RecentTransactions';
import { Header } from '../../components/common/Header';

export const DashboardScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { balances, isLoading: balancesLoading } = useSelector((state: RootState) => state.accounts);
  const { recentTransactions, isLoading: transactionsLoading } = useSelector(
    (state: RootState) => state.transactions
  );

  const [refreshing, setRefreshing] = React.useState(false);

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchAccountBalances()).unwrap(),
        dispatch(fetchRecentTransactions({ limit: 5 })).unwrap(),
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to load dashboard data');
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <View style={styles.container}>
      <Header
        title={`${getGreeting()}, ${user?.profile?.firstName || 'User'}`}
        showNotifications
        showProfile
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <BalanceCard
          totalBalance={balances?.totalBalance || 0}
          currency={balances?.currency || 'ZWG'}
          accounts={balances?.accounts || []}
          loading={balancesLoading}
        />

        <QuickActions />

        <RecentTransactions
          transactions={recentTransactions}
          loading={transactionsLoading}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});
```

**This comprehensive mobile app implementation provides a robust, user-friendly React Native application with enterprise-level features, security, and performance optimization for the UniversalWallet platform.** 📱
