﻿taxicab: The Complete Platform Blueprint
1.0 Executive Summary & Vision
1.1. Platform Name: taxicab
1.2. Vision: To be Zimbabwe's leading digital platform for on-demand transportation, logistics, and personal services, connecting individual and corporate clients with a trusted network of drivers and service providers through a seamless, reliable, and user-friendly ecosystem.
1.3. Mission: To build a robust and user-friendly platform that seamlessly connects users with a wide range of services. We will leverage cutting-edge technology,   fosters economic growth, and simplifies daily life.
1.4. Core Strategy: Modular platform
taxicab will be built as a modular  platform. This will be managed via Feature Flags, allowing the administration to enable or disable specific service modules in different regions or for different client tiers. This ensures maximum flexibility for strategic rollouts and market testing.
1.5. Target Audience:
* Urban Commuters: Individuals in cities like Harare and Bulawayo needing reliable daily transport.
* Corporate Clients: Businesses requiring transportation for employees, clients, and executives.
* Small & Medium Enterprises (SMEs): Businesses needing efficient courier and delivery services.
* Families & Parents: For school runs, shopping trips, and other family transport needs.
* Busy Individuals: Professionals who need assistance with personal errands.
* Homeowners & Renters: For refuse collection and moving services.
2.0 User Personas
2.1. <PERSON><PERSON>, the Urban Professional:
* Needs: Reliable transport to work and meetings. Values punctuality and safety. Occasionally needs a courier.
* Pain Points: Unreliable public transport, difficulty with parking, and needing a seamless booking and payment experience.
2.2. Mrs. Moyo, the Small Business Owner:
* Needs: A cost-effective way to deliver her bakery products across the city.
* Pain Points: High delivery costs from independent drivers, no real-time tracking, and difficulty managing orders.
2.3. Mr. Chen, the Corporate Administrator:
* Needs: A central platform to manage employee transport, requiring detailed invoicing and reporting.
* Pain Points: Managing multiple taxi receipts, lack of control over transport costs, and ensuring employee safety.
2.4. Mai Chisamba, the Mom on the Go:
* Needs: A safe service for the daily school run and help with grocery shopping errands.
* Pain Points: Concerns about child safety with unknown drivers and the inconvenience of running errands.
3.0 Design Language & UI/UX Principles
Inspired by the clean, professional aesthetic of rideflow.weblogik.co.zw, the entire taxicab ecosystem will adhere to the following principles:
* Clarity & Simplicity: Interfaces will be intuitive and uncluttered.
* Trust & Professionalism: The design will project reliability and security.
* Consistency: A unified design language across all platforms.
3.1. Color Palette:
* 
* Accent Teal (Action & Success): #00BFA5
* Neutral Background: #F5F5F5
* Text Color: #212121
* White Space: #FFFFFF
* Error/Alert Red: #D32F2F
3.2. Typography:
* Primary Font: Inter
* Headings: Poppins (Bold/Semi-Bold)
3.3. UI Elements:
* Cards: Information organized on cards with soft shadows and rounded corners.
* Buttons: Solid-filled primary buttons, outlined secondary buttons.
* Icons: A consistent set of professional, line-based icons.
4.0 Platform Components & Core Services
taxicab consists of   interconnected components, managed from a central backend.
4.1. Platform Components:
1. Marketing & Client onboaring Site

3. Client Mobile App (iOS, Android)
4. Driver Mobile App (iOS, Android)
 
6. Central Admin Dashboard
4.2. Core Service Modules (Managed via Feature Flags):
* General Taxi Service (MoversRide): On-demand and scheduled taxi services with various vehicle options.
* Corporate Transport: A dedicated web portal for business clients to manage employee travel and billing.
* Small Package & Courier (MoversExpress): On-demand delivery for small packages with real-time tracking.
* Personal Errands (MoversErrands): A task-based service for errands like grocery shopping.
* School & Work Runs (MoversCruiser): A scheduled service with vetted drivers for safe, recurring commutes.
*  
A key innovation for accessibility, allowing users to interact with the platform naturally in local languages.
* Languages: 
* Functionality: Users can book rides, get fare estimates,  
* Example: User says, "Ndiri kuda kuenda kuJoina City," and the system initiates a ride booking to Joina City.
6.0 Detailed User Journeys & Process Flows
6.1. Marketing & Client Management Site
Purpose: The public face of taxicab. Attracts new clients and allows existing clients to manage their services via the web.
User Journey 6.1.1: New Ordinary Client Signup
1. User lands on the homepage. Sees hero banner: "Your World, Moved. Sign up for free."
2. CLICK: User clicks "Sign Up" button.
3. SCREEN: Signup Page.
4. User selects "Personal Account".
5. FORM: User enters Full Name, Phone Number, Email, and Password.
6. User checks "I agree to the Terms of Service".
7. CLICK: User clicks "Create Account".
8. PROCESS: System sends an OTP to the provided phone number.
9. SCREEN: OTP Verification Page. "Enter the 6-digit code sent to +263...".
10. User enters OTP.
11. SUCCESS: "Welcome, [Name]! Your account is ready. Download our app to get started." Screen shows App Store and Google Play buttons.
12. EMAIL: User receives a welcome email.
User Journey 6.1.2: New Commercial Client Signup
1. User lands on homepage. Clicks on the "For Business" link in the main navigation.
2. SCREEN: Corporate Solutions Page. Details benefits like centralized billing and employee management.
3. CLICK: User clicks "Register Your Business".
4. SCREEN: Signup Page, "Corporate Account" is pre-selected.
5. FORM (Step 1 - Company Details): User enters Company Name, Industry, Company Address, Number of Employees. Clicks "Next".
6. FORM (Step 2 - Admin Details): User enters their Full Name, Work Email, Job Title, Phone Number, and creates a Password.
7. CLICK: User clicks "Submit for Verification".
8. SUCCESS: "Thank you! Your application has been submitted. A taxicab representative will contact you within 24 hours to complete verification."
9. ADMIN NOTIFICATION: Admin dashboard receives a new corporate signup request.
User Journey 6.1.3: Existing Client Web Management
1. User clicks "Login" on the marketing site.
2. SCREEN: Login page.
3. User enters Email and Password. Clicks "Login".
4. SCREEN: Client Dashboard
   * Personal Client: Sees a dashboard with Ride History, Payment Methods, Saved Locations, Profile Settings.
   * Corporate Client: Sees a dashboard with Overview, Employee Management, Travel Policies, Billing & Invoices, Reports.
5. Corporate Admin adds an Employee:
   * CLICK: Clicks on "Employee Management" -> "Add Employee".
   * FORM: Enters Employee Name, Email, Phone Number, Spending Limit.
   * CLICK: Clicks "Send Invite".
   * PROCESS: Employee receives an email to join the corporate account and set their password.
6.2. Drivers Association & Onboarding Site
Purpose: A dedicated portal to build a strong driver community, handle onboarding, and manage driver relations.
User Journey 6.2.1: Individual Driver Onboarding
1. Prospective driver lands on the Drivers Association site.
2. CLICK: Clicks "Join as a Driver".
3. SCREEN: Registration page. User selects "Individual Driver".
4. FORM (Multi-step):
   * Step 1: Personal Info: Name, Phone, Email, Address, National ID Number.
   * Step 2: Vehicle Info: Make, Model, Year, License Plate, Vehicle Insurance Details.
   * Step 3: Document Upload: Driver's License, Vehicle Registration Book, Proof of Address, Profile Photo.
   * Step 4: Association Membership: Choose Ordinary Member (access to app) or Participant (access + voting rights, small fee).
5. CLICK: Clicks "Submit Application".
6. SUCCESS: "Application received. We will review your documents and contact you for a vehicle inspection."
7. ADMIN NOTIFICATION: New driver application appears in the admin dashboard for verification.
User Journey 6.2.2: Transport Company Onboarding
1. Owner lands on the Drivers Association site. Clicks "Join as a Driver".
2. SCREEN: Registration page. User selects "Transport Company".
3. FORM (Multi-step):
   * Step 1: Company Info: Company Name, Registration Number, Address, Admin Contact Person, Admin Email, Admin Phone.
   * Step 2: Fleet Upload: An interface to add multiple vehicles and their respective drivers' details. Can upload via a CSV template for large fleets.
   * Step 3: Document Upload: Company Registration Docs, Tax Clearance, Admin's ID.
4. CLICK: Clicks "Submit Application".
5. SUCCESS & ADMIN NOTIFICATION: Same as individual driver flow.
6.3. Driver Mobile App
UI: Bottom navigation with four tabs: Home, Share Profile, Post Ride, Community.
User Journey 6.3.1: Accepting a Ride
1. Driver logs in. SCREEN: Home (Offline). Map view is visible. A large toggle button says "You're Offline".
2. CLICK: Driver taps the toggle. It slides to "You're Online" and turns Accent Teal. The app now actively searches for rides.
3. INCOMING REQUEST: A full-screen overlay appears.
   * Loud, distinct notification sound plays.
   * Shows: Pickup Location (e.g., "Avondale"), Distance to Pickup (e.g., "1.2 km"), Destination (e.g., "Eastgate"), Estimated Fare.
   * A countdown timer (e.g., 20 seconds) shows how long they have to accept.
   * Large "Accept" and "Decline" buttons.
4. CLICK: Driver taps "Accept".
5. SCREEN: Navigation View.
   * The overlay disappears. The map now shows a route to the pickup location.
   * A bottom panel shows the client's name ("Tendai") and a button to "Call Client".
   * CLICK: Driver taps "Arrived".
   * PROCESS: Client receives a notification: "Your driver has arrived."
   * CLICK: Driver taps "Start Trip".
   * SCREEN: Navigation View (to Destination). The map now shows the route to the client's destination.
   * CLICK: Driver taps "End Trip".
   * SCREEN: Trip Summary. Shows Fare, Duration, Distance.
   * CLICK: Driver taps "Confirm Payment" (assuming cash/local payment).
   * SCREEN: Rate Client. Driver can give a 1-5 star rating.
   * SCREEN: Returns to Home (Online), ready for the next trip.
User Journey 6.3.2: Share Profile
1. Driver is on any screen. CLICK: Taps the "Share Profile" tab in the bottom navigation.
2. SCREEN: Share Profile.
   * Shows the driver's own profile summary (Photo, Name, Car, Rating).
   * A single input field: "Enter Client's Phone Number".
   * A button: "Send Profile".
3. INPUT: Driver types the client's number (e.g., 0771234567).
4. CLICK: Driver taps "Send Profile".
5. SUCCESS POPUP: "Profile sent to +263771234567".
6. BACKEND PROCESS:
   * Receives the request.
   * Generates a unique, short URL for the driver's web profile.
   * Uses an SMS API (e.g., Twilio) to send an SMS: "Your taxicab driver, [Driver's Name], has shared their profile with you. View it or save them for later: [short-url]".
   * The SMS also contains a deep link to open the profile directly in the taxicab client app if installed.
User Journey 6.3.3: Post a Ride
1. Driver wants to book a trip for a client who hailed them on the street.
2. CLICK: Taps the "Post Ride" tab.
3. SCREEN: Post Ride Form. Fields: Client Name, Client Phone, Pickup Location (pre-filled with current location), Destination.
4. PATH A: Manual Entry: Driver types all the details in the form.
5. PATH B: AI Voice Entry:
   * CLICK: Driver taps a microphone icon next to the "Destination" field.
   * SCREEN: A voice recording interface appears. "Listening..."
   * DRIVER SPEAKS: "Ndiri kuda kuendesa client kuJoina City." (I want to take a client to Joina City).
   * CLICK: Driver taps "Stop".
   * PROCESS: Audio is sent to the backend AI service. The text is transcribed, and the NLP model extracts the destination "Joina City".
   * UPDATE: The "Destination" field on the form is automatically filled with "Joina City".
6. CLICK: Driver taps "Calculate Fare & Post Ride".
7. PROCESS: The trip is now active in the system, just as if the client had booked it. It's logged for earnings and safety tracking. The fare is shown, and the trip starts.
User Journey 6.3.4: Community Chat
1. CLICK: Driver taps the "Community" tab.
2. SCREEN: Chat Channels.
   * A list of chat groups is shown, segmented by city: # Harare, # Bulawayo, # Mutare.
3. CLICK: Driver taps on # Harare.
4. SCREEN: Harare Chat Room.
   * Looks like a standard WhatsApp/Telegram chat interface.
   * Shows a stream of messages from other drivers in Harare.
   * A text input field at the bottom to type a message.
   * INPUT: Driver types "Traffic is heavy along Samora Machel Ave." and hits "Send".
   * PROCESS: The message appears in the chat for all drivers in the Harare channel.
6.4. Client Mobile App
User Journey 6.4.1: Viewing a Shared Driver Profile
1. Client receives an SMS from taxicab.
2. CLICK: Client taps the link in the SMS.
3. PATH A: App not installed: A mobile-friendly web page opens, displaying the driver's profile: Photo, Name, Rating, Vehicle, and a button to "Download taxicab App".
4. PATH B: App installed: The taxicab app opens directly to the driver's profile screen.
5. SCREEN: Driver Profile.
   * Shows all driver details.
   * A prominent button: "Save to Favourites" and "Book This Driver" (if they are online).
6. CLICK: Client taps "Save to Favourites".
7. SUCCESS POPUP: "[Driver's Name] has been added to your favourite drivers."
User Journey 6.4.2: Booking with a Favourite Driver
1. Client opens the app.
2. SCREEN: Home. Enters destination.
3. SCREEN: Ride Options Panel. Above the standard vehicle types ("MoversLite", etc.), there is a new section: "Favourite Drivers".
4. It shows a list of saved drivers who are currently online and nearby.
5. CLICK: Client taps on their favourite driver, "John Doe".
6. PROCESS: The ride request is sent exclusively to John Doe for 30 seconds. If he doesn't accept, the request is then sent to the general pool of nearby drivers.
User Journey 6.4.3: Standard Ride Booking
1. Client opens app. App automatically detects current location as pickup point.
2. INPUT: User types destination ("Eastgate Mall") in the "Where to?" search bar.
3. SCREEN: Map updates to show route. A bottom panel slides up with vehicle options (MoversRide, MoversExec) and corresponding fare estimates.
4. CLICK: User selects "MoversRide".
5. CLICK: User taps the "Confirm MoversRide" button.
6. SCREEN: "Finding you a ride..." overlay appears.
7. PROCESS: A driver accepts. The screen updates to show driver's name, photo, vehicle details, and ETA on the map.
8. NOTIFICATION: Client receives a push notification when the driver arrives.
9. During the trip, client can see the real-time location on the map.
10. Trip ends. SCREEN: "Trip Complete" view shows final fare.
11. CLICK: Client chooses payment method and pays.
12. SCREEN: "Rate Your Driver" screen appears. Client gives a star rating and optional comment.
13. CLICK: Client taps "Submit". Returns to the home screen.
6.5. WhatsApp Chatbot
User Journey 6.5.1: Booking a Ride via WhatsApp
1. User sends "Hi" to the taxicab WhatsApp number.
2. CHATBOT: Responds with a welcome message and a menu of options (e.g., 1. Book a Ride, 2. Fare Estimate, 3. Track a Ride).
3. User replies "1".
4. CHATBOT: "Great! Please share your current location, or type the address where we should pick you up."
5. User shares their location via WhatsApp's location feature.
6. CHATBOT: "Got it. Where would you like to go?"
7. User types "Eastgate".
8. CHATBOT: "A trip to Eastgate will be approximately $X. Shall I confirm your booking?" (Provides quick reply buttons: "Yes, Confirm" / "No, Cancel").
9. User taps "Yes, Confirm".
10. CHATBOT: "Searching for a driver... Found! [Driver's Name] in a [Vehicle] is on the way. You can track them here: [link]".
6.6. Central Admin Dashboard
User Journey 6.6.1: Managing Feature Flags
1. Admin logs in. Navigates to Settings -> Feature Management.
2. SCREEN: Feature Flag Matrix.
3. The table shows services as rows and regions as columns, with toggle switches.
4. Admin wants to launch "MoversBin" in Bulawayo.
5. CLICK: Admin finds the "MoversBin" row and flips the toggle under the "Bulawayo" column from OFF to ON.
6. SUCCESS: The UI updates, and the change is saved instantly to the Firestore database.
7. EFFECT: The next time users in Bulawayo open their app, the "MoversBin" service option will be visible.
User Journey 6.6.2: Verifying a New Driver
1. NOTIFICATION: Admin sees a notification for a "New Driver Application" on the dashboard.
2. CLICK: Admin clicks the notification or navigates to Drivers -> Pending Applications.
3. SCREEN: A list of pending applications is displayed. Admin clicks on the top one.
4. SCREEN: Driver Verification Page.
   * Shows all submitted driver information (Personal, Vehicle).
   * Displays uploaded documents (License, Registration) for viewing in a modal window.
5. ACTION: Admin reviews all documents for validity.
6. CLICK: Admin clicks a dropdown menu for "Application Status" and selects "Approved" (or "Rejected" or "More Info Needed").
7. CLICK: Admin clicks "Save Changes".
8. PROCESS:
   * If Approved, the driver's status changes from "pending" to "active". They receive an SMS and email: "Congratulations! Your taxicab application is approved. You can now log in to the driver app and go online."
   * If Rejected, they receive a notification with the reason.
7.0 Technical Architecture & Non-Functional Requirements
7.1. Technology Stack Summary:
* Backend: Go (Golang) on GCP, using a microservices architecture.
* APIs: RESTful APIs (Gin Gonic) and gRPC.
* Database: Firestore (primary data), Cloud SQL (relational data).
* Frontend Apps: SwiftUI (iOS), Kotlin (Android).
* Web Platforms: Vue.js (Nuxt.js for SSR).
* AI & Comms: Google Cloud AI (Speech-to-Text, NLP), Twilio (SMS), WebSockets (Chat).
* Hosting: Google Cloud Platform (GCP).
7.2. Non-Functional Requirements:
* Performance: Fast and responsive, even on low-end devices and with poor connectivity.
* Scalability: Must handle a large number of concurrent users and transactions.
* Security: All user data encrypted at rest and in transit.
* Reliability: High uptime (e.g., 99.9%) and 24/7 availability.
* Offline Functionality: Apps should cache essential data for offline viewing.
8.0 Technical Implementation Plan & Code Samples
This section provides a more detailed technical overview and sample code to illustrate the implementation.
8.1. System Architecture Diagram
  +-----------------+      +-----------------+      +-----------------+
 |   Client Apps   |      |  Web Platforms  |      |  WhatsApp Bot   |
 | (iOS, Android)  |      | (react.js)        |      | (Twilio)        |
 +-----------------+      +-----------------+      +-----------------+
       |                      |                      |
       | (HTTPS/REST)         | (HTTPS/REST)         | (Webhook)
       |                      |                      |
 +-------------------------------------------------------------+
 |               Backend (Go on GCP) - Microservices           |
 | +-----------+ +-----------+ +-----------+ +-----------+     |
 | | User Svc  | | Ride Svc  | | Driver Svc| | AI Svc    | ... |
 | +-----------+ +-----------+ +-----------+ +-----------+     |
 +-------------------------------------------------------------+
       | (gRPC/REST)          | (gRPC/REST)
       |                      |
 +-----------------+      +-----------------+      +-----------------+
 |  Database (GCP) |      | 3rd Party APIs  |      |  Monitoring     |
 | (Firestore/SQL) |      | (Payments, Maps)|      | (Stackdriver)   |
 +-----------------+      +-----------------+      +-----------------+

 