# 2. Authentication and Security

## 🔐 **Authentication Implementation Overview**

The Taxicab platform implements a comprehensive authentication and security system using Supabase Auth with phone-based verification, multi-factor authentication, and role-based access control. This document covers the complete implementation of secure authentication flows.

### **Security Principles**
- **Phone-First Authentication**: Primary authentication via phone number and OTP
- **Multi-Factor Authentication**: Additional security for sensitive operations
- **Role-Based Access Control**: Granular permissions based on user roles
- **Session Management**: Secure token handling with automatic refresh
- **Audit Logging**: Complete authentication event tracking

## 📱 **Phone Authentication Implementation**

### **1. Supabase Auth Configuration**
```typescript
// supabase/config/auth.ts
export const authConfig = {
  // Site URLs for redirect handling
  site_url: process.env.SITE_URL || 'http://localhost:3000',
  redirect_urls: [
    'http://localhost:3000/auth/callback',
    'https://admin.taxicab.co.zw/auth/callback',
    'taxicab://auth/callback', // Mobile deep link
  ],
  
  // JWT settings
  jwt_expiry: 3600, // 1 hour
  refresh_token_rotation_enabled: true,
  
  // Phone auth settings
  enable_phone_signup: true,
  enable_phone_confirmations: true,
  
  // SMS provider settings (Twilio)
  sms_provider: 'twilio',
  sms_max_frequency: 60, // 1 minute between SMS
  sms_otp_exp: 300, // 5 minutes OTP expiry
  sms_otp_length: 6,
  
  // Security settings
  security_captcha_enabled: true,
  security_captcha_provider: 'hcaptcha',
  password_min_length: 8,
  
  // Session settings
  sessions_timebox: 86400, // 24 hours
  sessions_inactivity_timeout: 3600, // 1 hour
};
```

### **2. Phone Authentication Service**
```typescript
// packages/api-client/src/auth/phoneAuth.ts
import { createClient } from '@supabase/supabase-js';
import type { AuthResponse, AuthOtpResponse } from '@supabase/supabase-js';

export class PhoneAuthService {
  private supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_ANON_KEY!
  );

  /**
   * Send OTP to phone number for authentication
   */
  async sendOTP(phone: string, isSignUp: boolean = false): Promise<AuthOtpResponse> {
    try {
      // Validate phone number format
      if (!this.isValidPhoneNumber(phone)) {
        throw new Error('Invalid phone number format');
      }

      const { data, error } = await this.supabase.auth.signInWithOtp({
        phone: this.formatPhoneNumber(phone),
        options: {
          shouldCreateUser: isSignUp,
          data: {
            phone: this.formatPhoneNumber(phone),
          }
        }
      });

      if (error) {
        throw new AuthenticationError(error.message, 'OTP_SEND_FAILED');
      }

      return data;
    } catch (error) {
      console.error('Failed to send OTP:', error);
      throw error;
    }
  }

  /**
   * Verify OTP and complete authentication
   */
  async verifyOTP(
    phone: string, 
    token: string, 
    userDetails?: Partial<UserProfile>
  ): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.verifyOtp({
        phone: this.formatPhoneNumber(phone),
        token: token.trim(),
        type: 'sms'
      });

      if (error) {
        throw new AuthenticationError(error.message, 'OTP_VERIFICATION_FAILED');
      }

      // If this is a new user, create profile
      if (data.user && userDetails) {
        await this.createUserProfile(data.user.id, {
          phone: this.formatPhoneNumber(phone),
          ...userDetails
        });
      }

      return data;
    } catch (error) {
      console.error('Failed to verify OTP:', error);
      throw error;
    }
  }

  /**
   * Create user profile after successful authentication
   */
  private async createUserProfile(
    userId: string, 
    profile: Partial<UserProfile>
  ): Promise<void> {
    const { error } = await this.supabase
      .from('users')
      .insert({
        id: userId,
        phone: profile.phone,
        full_name: profile.full_name,
        email: profile.email,
        role: profile.role || 'passenger',
        is_verified: true,
      });

    if (error) {
      console.error('Failed to create user profile:', error);
      throw new Error('Failed to create user profile');
    }
  }

  /**
   * Validate phone number format (international format)
   */
  private isValidPhoneNumber(phone: string): boolean {
    // E.164 format: +[country code][number]
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Format phone number to E.164 standard
   */
  private formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters except +
    let formatted = phone.replace(/[^\d+]/g, '');
    
    // Add + if not present
    if (!formatted.startsWith('+')) {
      // Assume Zimbabwe number if no country code
      if (formatted.startsWith('0')) {
        formatted = '+263' + formatted.substring(1);
      } else if (formatted.length === 9) {
        formatted = '+263' + formatted;
      } else {
        formatted = '+' + formatted;
      }
    }
    
    return formatted;
  }
}

// Custom error class for authentication errors
export class AuthenticationError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}
```

### **3. Authentication Hook Implementation**
```typescript
// packages/api-client/src/hooks/useAuth.ts
import { useState, useEffect, useCallback } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { PhoneAuthService } from '../auth/phoneAuth';
import { supabase } from '../supabase';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

interface AuthActions {
  sendOTP: (phone: string, isSignUp?: boolean) => Promise<void>;
  verifyOTP: (phone: string, token: string, userDetails?: Partial<UserProfile>) => Promise<void>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
}

export function useAuth(): AuthState & AuthActions {
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
  });

  const phoneAuth = new PhoneAuthService();

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setState(prev => ({
        ...prev,
        session,
        user: session?.user ?? null,
        loading: false,
      }));
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setState(prev => ({
          ...prev,
          session,
          user: session?.user ?? null,
          loading: false,
        }));

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in:', session?.user?.id);
            break;
          case 'SIGNED_OUT':
            console.log('User signed out');
            break;
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed');
            break;
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const sendOTP = useCallback(async (phone: string, isSignUp = false) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      await phoneAuth.sendOTP(phone, isSignUp);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: (error as Error).message,
        loading: false,
      }));
      throw error;
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [phoneAuth]);

  const verifyOTP = useCallback(async (
    phone: string, 
    token: string, 
    userDetails?: Partial<UserProfile>
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const { session, user } = await phoneAuth.verifyOTP(phone, token, userDetails);
      setState(prev => ({
        ...prev,
        session,
        user,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: (error as Error).message,
        loading: false,
      }));
      throw error;
    }
  }, [phoneAuth]);

  const signOut = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      await supabase.auth.signOut();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: (error as Error).message,
        loading: false,
      }));
      throw error;
    }
  }, []);

  const refreshSession = useCallback(async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      
      setState(prev => ({
        ...prev,
        session: data.session,
        user: data.user,
      }));
    } catch (error) {
      console.error('Failed to refresh session:', error);
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    sendOTP,
    verifyOTP,
    signOut,
    refreshSession,
    clearError,
  };
}
```

## 🔒 **Multi-Factor Authentication**

### **1. 2FA Implementation**
```typescript
// packages/api-client/src/auth/twoFactorAuth.ts
export class TwoFactorAuthService {
  private supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_ANON_KEY!
  );

  /**
   * Send 2FA code for sensitive operations
   */
  async send2FACode(operation: string): Promise<string> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await this.supabase.functions.invoke('send-2fa-code', {
        body: {
          user_id: user.id,
          operation,
          phone: user.phone,
        }
      });

      if (error) {
        throw new Error(`Failed to send 2FA code: ${error.message}`);
      }

      return data.challenge_id;
    } catch (error) {
      console.error('Failed to send 2FA code:', error);
      throw error;
    }
  }

  /**
   * Verify 2FA code
   */
  async verify2FACode(challengeId: string, code: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase.functions.invoke('verify-2fa-code', {
        body: {
          challenge_id: challengeId,
          code: code.trim(),
        }
      });

      if (error) {
        throw new Error(`2FA verification failed: ${error.message}`);
      }

      return data.verified;
    } catch (error) {
      console.error('Failed to verify 2FA code:', error);
      throw error;
    }
  }

  /**
   * Check if operation requires 2FA
   */
  requiresTwoFactor(operation: string): boolean {
    const sensitiveOperations = [
      'driver_approval',
      'payment_processing',
      'user_suspension',
      'data_export',
      'system_configuration',
    ];

    return sensitiveOperations.includes(operation);
  }

  private async getCurrentUser() {
    const { data: { user } } = await this.supabase.auth.getUser();
    return user;
  }
}
```

### **2. 2FA Edge Function**
```typescript
// supabase/functions/send-2fa-code/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TwoFARequest {
  user_id: string;
  operation: string;
  phone: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { user_id, operation, phone }: TwoFARequest = await req.json();
    
    // Generate 6-digit code
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const challengeId = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Store challenge in database
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    await supabase
      .from('two_factor_challenges')
      .insert({
        id: challengeId,
        user_id,
        operation,
        code,
        expires_at: expiresAt.toISOString(),
        is_used: false,
      });

    // Send SMS via Twilio
    await sendSMS(phone, `Your Taxicab security code is: ${code}. Valid for 5 minutes.`);

    return new Response(
      JSON.stringify({ challenge_id: challengeId }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});

async function sendSMS(phone: string, message: string): Promise<void> {
  const accountSid = Deno.env.get('TWILIO_ACCOUNT_SID');
  const authToken = Deno.env.get('TWILIO_AUTH_TOKEN');
  const fromNumber = Deno.env.get('TWILIO_PHONE_NUMBER');

  const response = await fetch(
    `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`,
    {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${accountSid}:${authToken}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        From: fromNumber!,
        To: phone,
        Body: message,
      }),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to send SMS');
  }
}
```

## 👤 **Role-Based Access Control**

### **1. Role Management System**
```typescript
// packages/api-client/src/auth/roleManager.ts
export enum UserRole {
  PASSENGER = 'passenger',
  DRIVER = 'driver',
  ADMIN = 'admin',
  CORPORATE_ADMIN = 'corporate_admin',
}

export enum Permission {
  // Ride permissions
  CREATE_RIDE = 'create_ride',
  VIEW_OWN_RIDES = 'view_own_rides',
  VIEW_ALL_RIDES = 'view_all_rides',
  CANCEL_RIDE = 'cancel_ride',
  
  // Driver permissions
  ACCEPT_RIDES = 'accept_rides',
  UPDATE_LOCATION = 'update_location',
  VIEW_EARNINGS = 'view_earnings',
  
  // Admin permissions
  MANAGE_USERS = 'manage_users',
  MANAGE_DRIVERS = 'manage_drivers',
  VIEW_ANALYTICS = 'view_analytics',
  SYSTEM_CONFIG = 'system_config',
  
  // Corporate permissions
  MANAGE_EMPLOYEES = 'manage_employees',
  VIEW_CORPORATE_RIDES = 'view_corporate_rides',
  MANAGE_BILLING = 'manage_billing',
}

const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.PASSENGER]: [
    Permission.CREATE_RIDE,
    Permission.VIEW_OWN_RIDES,
    Permission.CANCEL_RIDE,
  ],
  
  [UserRole.DRIVER]: [
    Permission.ACCEPT_RIDES,
    Permission.UPDATE_LOCATION,
    Permission.VIEW_EARNINGS,
    Permission.VIEW_OWN_RIDES,
  ],
  
  [UserRole.ADMIN]: [
    Permission.MANAGE_USERS,
    Permission.MANAGE_DRIVERS,
    Permission.VIEW_ALL_RIDES,
    Permission.VIEW_ANALYTICS,
    Permission.SYSTEM_CONFIG,
  ],
  
  [UserRole.CORPORATE_ADMIN]: [
    Permission.MANAGE_EMPLOYEES,
    Permission.VIEW_CORPORATE_RIDES,
    Permission.MANAGE_BILLING,
    Permission.CREATE_RIDE,
  ],
};

export class RoleManager {
  /**
   * Check if user has specific permission
   */
  static hasPermission(userRole: UserRole, permission: Permission): boolean {
    const rolePermissions = ROLE_PERMISSIONS[userRole];
    return rolePermissions.includes(permission);
  }

  /**
   * Check if user can access resource
   */
  static canAccessResource(
    userRole: UserRole,
    resource: string,
    action: string
  ): boolean {
    const permissionKey = `${action}_${resource}` as Permission;
    return this.hasPermission(userRole, permissionKey);
  }

  /**
   * Get all permissions for a role
   */
  static getRolePermissions(userRole: UserRole): Permission[] {
    return ROLE_PERMISSIONS[userRole] || [];
  }

  /**
   * Validate role transition (e.g., passenger becoming driver)
   */
  static canTransitionRole(
    currentRole: UserRole,
    targetRole: UserRole
  ): boolean {
    // Define allowed role transitions
    const allowedTransitions: Record<UserRole, UserRole[]> = {
      [UserRole.PASSENGER]: [UserRole.DRIVER, UserRole.CORPORATE_ADMIN],
      [UserRole.DRIVER]: [UserRole.PASSENGER],
      [UserRole.CORPORATE_ADMIN]: [UserRole.PASSENGER],
      [UserRole.ADMIN]: [], // Admins cannot change roles
    };

    return allowedTransitions[currentRole]?.includes(targetRole) || false;
  }
}
```

### **2. Permission Middleware**
```typescript
// packages/api-client/src/middleware/authMiddleware.ts
import { RoleManager, Permission, UserRole } from '../auth/roleManager';

export function requirePermission(permission: Permission) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user; // Populated by auth middleware
      
      if (!user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const hasPermission = RoleManager.hasPermission(user.role as UserRole, permission);
      
      if (!hasPermission) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          required_permission: permission 
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({ error: 'Authorization check failed' });
    }
  };
}

export function requireRole(allowedRoles: UserRole[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      if (!allowedRoles.includes(user.role as UserRole)) {
        return res.status(403).json({ 
          error: 'Insufficient role permissions',
          required_roles: allowedRoles 
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({ error: 'Role check failed' });
    }
  };
}
```

This comprehensive authentication and security implementation provides robust protection for the Taxicab platform with phone-based authentication, multi-factor security, and granular role-based access control.
