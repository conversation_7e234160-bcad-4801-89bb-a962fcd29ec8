# 2. End-to-End Testing

## 🎯 **E2E Testing Overview**

End-to-end testing for the Taxicab platform validates complete user workflows across all applications - admin dashboard, passenger mobile app, and driver mobile app. These tests simulate real user interactions to ensure the entire system works as expected from the user's perspective.

### **E2E Testing Principles**
- **User-Centric**: Test from the user's perspective, not technical implementation
- **Cross-Platform**: Validate functionality across web and mobile platforms
- **Real Scenarios**: Use realistic data and workflows
- **Performance Aware**: Monitor performance during E2E tests
- **Reliable**: Minimize flaky tests with proper waits and assertions

## 🛠️ **Testing Framework Setup**

### **1. Playwright Configuration for Web**
```typescript
// tests/e2e/playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### **2. Detox Configuration for Mobile**
```json
// .detoxrc.js
module.exports = {
  testRunner: 'jest',
  runnerConfig: 'tests/e2e/jest.config.js',
  skipLegacyWorkersInjection: true,
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Debug-iphonesimulator/TaxicabPassenger.app',
      build: 'xcodebuild -workspace ios/TaxicabPassenger.xcworkspace -scheme TaxicabPassenger -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build'
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',
      build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug'
    }
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 14'
      }
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_4_API_30'
      }
    }
  },
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug'
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug'
    }
  }
};
```

### **3. Test Utilities and Page Objects**
```typescript
// tests/e2e/utils/testUtils.ts
import { Page, expect } from '@playwright/test';

export class TestUtils {
  constructor(private page: Page) {}

  async waitForLoadingToFinish() {
    await this.page.waitForSelector('[data-testid="loading-spinner"]', { 
      state: 'hidden',
      timeout: 10000 
    });
  }

  async fillPhoneNumber(phone: string) {
    await this.page.fill('[data-testid="phone-input"]', phone);
  }

  async fillOTP(otp: string) {
    await this.page.fill('[data-testid="otp-input"]', otp);
  }

  async clickButton(testId: string) {
    await this.page.click(`[data-testid="${testId}"]`);
  }

  async expectToastMessage(message: string) {
    await expect(this.page.locator('[data-testid="toast"]')).toContainText(message);
  }

  async expectPageTitle(title: string) {
    await expect(this.page).toHaveTitle(title);
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png` });
  }

  async mockGeolocation(latitude: number, longitude: number) {
    await this.page.context().grantPermissions(['geolocation']);
    await this.page.context().setGeolocation({ latitude, longitude });
  }

  async interceptApiCall(url: string, response: any) {
    await this.page.route(url, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(response),
      });
    });
  }
}

// Page Object Model for Admin Dashboard
export class AdminDashboardPage {
  constructor(private page: Page, private utils: TestUtils) {}

  async navigate() {
    await this.page.goto('/admin');
  }

  async login(phone: string, otp: string = '123456') {
    await this.utils.fillPhoneNumber(phone);
    await this.utils.clickButton('send-otp-button');
    await this.utils.waitForLoadingToFinish();
    
    await this.utils.fillOTP(otp);
    await this.utils.clickButton('verify-otp-button');
    await this.utils.waitForLoadingToFinish();
  }

  async navigateToDrivers() {
    await this.page.click('[data-testid="nav-drivers"]');
    await this.utils.waitForLoadingToFinish();
  }

  async approveDriver(driverName: string) {
    const driverRow = this.page.locator(`[data-testid="driver-row"]:has-text("${driverName}")`);
    await driverRow.locator('[data-testid="driver-actions"]').click();
    await this.page.click('[data-testid="approve-driver"]');
    await this.utils.expectToastMessage('Driver approved successfully');
  }

  async viewRideDetails(rideId: string) {
    await this.page.click(`[data-testid="ride-${rideId}"]`);
    await this.utils.waitForLoadingToFinish();
  }

  async expectDriverCount(count: number) {
    const driverRows = this.page.locator('[data-testid="driver-row"]');
    await expect(driverRows).toHaveCount(count);
  }

  async expectRideStatus(rideId: string, status: string) {
    const statusElement = this.page.locator(`[data-testid="ride-${rideId}-status"]`);
    await expect(statusElement).toContainText(status);
  }
}
```

## 🚗 **Admin Dashboard E2E Tests**

### **1. Driver Management Workflow**
```typescript
// tests/e2e/admin/driverManagement.spec.ts
import { test, expect } from '@playwright/test';
import { AdminDashboardPage, TestUtils } from '../utils/testUtils';

test.describe('Driver Management', () => {
  let adminPage: AdminDashboardPage;
  let utils: TestUtils;

  test.beforeEach(async ({ page }) => {
    utils = new TestUtils(page);
    adminPage = new AdminDashboardPage(page, utils);
    
    // Mock admin user authentication
    await utils.interceptApiCall('**/auth/verify', {
      user: { id: 'admin-1', role: 'admin' },
      session: { access_token: 'mock-token' }
    });
  });

  test('should approve pending driver application', async () => {
    // Navigate to admin dashboard
    await adminPage.navigate();
    await adminPage.login('+263771234567');

    // Navigate to drivers section
    await adminPage.navigateToDrivers();

    // Mock pending driver data
    await utils.interceptApiCall('**/drivers*', {
      data: [{
        id: 'driver-1',
        user: { full_name: 'John Doe', phone: '+263771234568' },
        vehicle: { make: 'Toyota', model: 'Corolla', license_plate: 'ABC-123' },
        status: 'pending',
        license_number: 'DL123456',
        created_at: new Date().toISOString()
      }]
    });

    // Verify pending driver is displayed
    await expect(page.locator('[data-testid="driver-row"]')).toContainText('John Doe');
    await expect(page.locator('[data-testid="driver-status"]')).toContainText('Pending');

    // Approve driver
    await adminPage.approveDriver('John Doe');

    // Verify approval success
    await utils.expectToastMessage('Driver approved successfully');
  });

  test('should view and filter driver list', async () => {
    await adminPage.navigate();
    await adminPage.login('+263771234567');
    await adminPage.navigateToDrivers();

    // Mock multiple drivers
    await utils.interceptApiCall('**/drivers*', {
      data: [
        { id: 'driver-1', user: { full_name: 'John Doe' }, status: 'approved' },
        { id: 'driver-2', user: { full_name: 'Jane Smith' }, status: 'pending' },
        { id: 'driver-3', user: { full_name: 'Bob Wilson' }, status: 'suspended' }
      ]
    });

    // Verify all drivers are displayed
    await adminPage.expectDriverCount(3);

    // Filter by status
    await page.selectOption('[data-testid="status-filter"]', 'approved');
    await utils.waitForLoadingToFinish();

    // Verify filtered results
    await expect(page.locator('[data-testid="driver-row"]')).toHaveCount(1);
    await expect(page.locator('[data-testid="driver-row"]')).toContainText('John Doe');
  });

  test('should handle driver document verification', async () => {
    await adminPage.navigate();
    await adminPage.login('+263771234567');
    await adminPage.navigateToDrivers();

    // Click on driver to view details
    await page.click('[data-testid="driver-row"]:first-child');
    await utils.waitForLoadingToFinish();

    // Verify document sections are present
    await expect(page.locator('[data-testid="license-document"]')).toBeVisible();
    await expect(page.locator('[data-testid="vehicle-registration"]')).toBeVisible();
    await expect(page.locator('[data-testid="insurance-document"]')).toBeVisible();

    // Verify document
    await page.click('[data-testid="verify-license"]');
    await utils.expectToastMessage('Document verified successfully');
  });
});
```

### **2. Ride Monitoring Tests**
```typescript
// tests/e2e/admin/rideMonitoring.spec.ts
test.describe('Ride Monitoring', () => {
  test('should display real-time ride updates', async ({ page }) => {
    const utils = new TestUtils(page);
    const adminPage = new AdminDashboardPage(page, utils);

    await adminPage.navigate();
    await adminPage.login('+263771234567');

    // Navigate to rides section
    await page.click('[data-testid="nav-rides"]');
    await utils.waitForLoadingToFinish();

    // Mock active rides
    await utils.interceptApiCall('**/rides*', {
      data: [{
        id: 'ride-1',
        pickup_address: '123 Main St',
        destination_address: '456 Oak Ave',
        status: 'in_progress',
        passenger: { full_name: 'Alice Johnson' },
        driver: { user: { full_name: 'Bob Driver' } },
        estimated_fare: 15.50
      }]
    });

    // Verify ride is displayed
    await expect(page.locator('[data-testid="ride-row"]')).toContainText('Alice Johnson');
    await adminPage.expectRideStatus('ride-1', 'In Progress');

    // Simulate real-time status update
    await utils.interceptApiCall('**/rides/ride-1', {
      id: 'ride-1',
      status: 'completed',
      final_fare: 16.00
    });

    // Trigger status update (this would normally come via WebSocket)
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('ride-updated', {
        detail: { id: 'ride-1', status: 'completed' }
      }));
    });

    // Verify status update
    await adminPage.expectRideStatus('ride-1', 'Completed');
  });

  test('should handle ride cancellation', async ({ page }) => {
    const utils = new TestUtils(page);
    const adminPage = new AdminDashboardPage(page, utils);

    await adminPage.navigate();
    await adminPage.login('+263771234567');
    await page.click('[data-testid="nav-rides"]');

    // View ride details
    await adminPage.viewRideDetails('ride-1');

    // Cancel ride (admin action)
    await page.click('[data-testid="cancel-ride"]');
    await page.fill('[data-testid="cancellation-reason"]', 'Safety concern');
    await page.click('[data-testid="confirm-cancellation"]');

    await utils.expectToastMessage('Ride cancelled successfully');
  });
});
```

## 📱 **Mobile App E2E Tests**

### **1. Passenger App Tests**
```typescript
// tests/e2e/mobile/passengerApp.e2e.ts
import { device, element, by, expect as detoxExpect } from 'detox';

describe('Passenger App E2E', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Authentication Flow', () => {
    it('should complete phone authentication', async () => {
      // Enter phone number
      await element(by.id('phone-input')).typeText('+263771234567');
      await element(by.id('send-otp-button')).tap();

      // Wait for OTP screen
      await detoxExpect(element(by.id('otp-input'))).toBeVisible();

      // Enter OTP
      await element(by.id('otp-input')).typeText('123456');
      await element(by.id('verify-otp-button')).tap();

      // Verify successful login
      await detoxExpect(element(by.id('home-screen'))).toBeVisible();
    });
  });

  describe('Ride Booking Flow', () => {
    beforeEach(async () => {
      // Complete authentication first
      await authenticateUser();
    });

    it('should book a ride successfully', async () => {
      // Navigate to booking screen
      await element(by.id('book-ride-tab')).tap();

      // Set pickup location
      await element(by.id('pickup-input')).typeText('123 Main Street, Harare');
      await element(by.id('pickup-suggestion-0')).tap();

      // Set destination
      await element(by.id('destination-input')).typeText('456 Oak Avenue, Harare');
      await element(by.id('destination-suggestion-0')).tap();

      // Select service type
      await element(by.id('service-movers-ride')).tap();

      // Verify fare estimate
      await detoxExpect(element(by.id('fare-estimate'))).toBeVisible();

      // Book ride
      await element(by.id('book-ride-button')).tap();

      // Verify booking confirmation
      await detoxExpect(element(by.id('ride-booked-message'))).toBeVisible();
      await detoxExpect(element(by.text('Looking for nearby drivers...'))).toBeVisible();
    });

    it('should handle ride cancellation', async () => {
      // Book a ride first
      await bookTestRide();

      // Cancel ride
      await element(by.id('cancel-ride-button')).tap();
      await element(by.id('confirm-cancellation')).tap();

      // Verify cancellation
      await detoxExpect(element(by.text('Ride cancelled'))).toBeVisible();
    });

    it('should track ride in real-time', async () => {
      // Book a ride and wait for driver acceptance
      await bookTestRide();
      await simulateDriverAcceptance();

      // Verify tracking screen
      await detoxExpect(element(by.id('ride-tracking-screen'))).toBeVisible();
      await detoxExpect(element(by.id('driver-info'))).toBeVisible();
      await detoxExpect(element(by.id('ride-map'))).toBeVisible();

      // Simulate driver arrival
      await simulateDriverArrival();
      await detoxExpect(element(by.text('Driver has arrived'))).toBeVisible();

      // Simulate ride start
      await simulateRideStart();
      await detoxExpect(element(by.text('Trip in progress'))).toBeVisible();

      // Simulate ride completion
      await simulateRideCompletion();
      await detoxExpect(element(by.id('ride-completed-screen'))).toBeVisible();
    });
  });

  describe('Ride History', () => {
    it('should display ride history', async () => {
      await authenticateUser();
      await element(by.id('history-tab')).tap();

      // Verify history screen
      await detoxExpect(element(by.id('ride-history-list'))).toBeVisible();
      
      // Verify ride items
      await detoxExpect(element(by.id('ride-item-0'))).toBeVisible();
      
      // Tap on ride for details
      await element(by.id('ride-item-0')).tap();
      await detoxExpect(element(by.id('ride-details-screen'))).toBeVisible();
    });
  });

  // Helper functions
  async function authenticateUser() {
    await element(by.id('phone-input')).typeText('+263771234567');
    await element(by.id('send-otp-button')).tap();
    await element(by.id('otp-input')).typeText('123456');
    await element(by.id('verify-otp-button')).tap();
    await detoxExpect(element(by.id('home-screen'))).toBeVisible();
  }

  async function bookTestRide() {
    await element(by.id('book-ride-tab')).tap();
    await element(by.id('pickup-input')).typeText('Test Pickup');
    await element(by.id('destination-input')).typeText('Test Destination');
    await element(by.id('book-ride-button')).tap();
  }

  async function simulateDriverAcceptance() {
    // Simulate WebSocket message for driver acceptance
    await device.sendUserNotification({
      trigger: {
        type: 'push',
      },
      title: 'Driver Found',
      body: 'John is on the way to pick you up',
      payload: {
        type: 'ride_accepted',
        ride_id: 'test-ride-1'
      }
    });
  }

  async function simulateDriverArrival() {
    await device.sendUserNotification({
      trigger: { type: 'push' },
      title: 'Driver Arrived',
      body: 'Your driver has arrived at the pickup location',
      payload: { type: 'driver_arrived', ride_id: 'test-ride-1' }
    });
  }

  async function simulateRideStart() {
    await device.sendUserNotification({
      trigger: { type: 'push' },
      title: 'Trip Started',
      body: 'Your trip has started',
      payload: { type: 'trip_started', ride_id: 'test-ride-1' }
    });
  }

  async function simulateRideCompletion() {
    await device.sendUserNotification({
      trigger: { type: 'push' },
      title: 'Trip Completed',
      body: 'You have arrived at your destination',
      payload: { type: 'trip_completed', ride_id: 'test-ride-1' }
    });
  }
});
```

### **2. Driver App Tests**
```typescript
// tests/e2e/mobile/driverApp.e2e.ts
describe('Driver App E2E', () => {
  describe('Driver Dashboard', () => {
    it('should toggle online/offline status', async () => {
      await authenticateDriver();
      
      // Verify initial offline status
      await detoxExpect(element(by.id('online-toggle'))).toHaveToggleValue(false);
      
      // Go online
      await element(by.id('online-toggle')).tap();
      await detoxExpect(element(by.text('You are now online'))).toBeVisible();
      
      // Go offline
      await element(by.id('online-toggle')).tap();
      await detoxExpect(element(by.text('You are now offline'))).toBeVisible();
    });

    it('should receive and accept ride requests', async () => {
      await authenticateDriver();
      await goOnline();

      // Simulate incoming ride request
      await simulateRideRequest();
      
      // Verify ride request modal
      await detoxExpect(element(by.id('ride-request-modal'))).toBeVisible();
      await detoxExpect(element(by.id('pickup-address'))).toContainText('123 Main Street');
      await detoxExpect(element(by.id('estimated-fare'))).toBeVisible();

      // Accept ride
      await element(by.id('accept-ride-button')).tap();
      
      // Verify navigation to ride screen
      await detoxExpect(element(by.id('active-ride-screen'))).toBeVisible();
    });

    it('should complete ride workflow', async () => {
      await authenticateDriver();
      await acceptTestRide();

      // Navigate to pickup
      await element(by.id('navigate-to-pickup')).tap();
      
      // Arrive at pickup
      await element(by.id('arrived-at-pickup')).tap();
      await detoxExpect(element(by.text('Passenger notified'))).toBeVisible();

      // Start trip
      await element(by.id('start-trip-button')).tap();
      await detoxExpect(element(by.id('trip-in-progress'))).toBeVisible();

      // Complete trip
      await element(by.id('complete-trip-button')).tap();
      await element(by.id('final-fare-input')).typeText('15.50');
      await element(by.id('confirm-completion')).tap();

      // Verify completion
      await detoxExpect(element(by.text('Trip completed successfully'))).toBeVisible();
    });
  });

  describe('Driver Chat', () => {
    it('should send and receive chat messages', async () => {
      await authenticateDriver();
      await element(by.id('chat-tab')).tap();

      // Send message
      await element(by.id('message-input')).typeText('Hello everyone!');
      await element(by.id('send-message-button')).tap();

      // Verify message appears
      await detoxExpect(element(by.text('Hello everyone!'))).toBeVisible();

      // Simulate receiving message
      await simulateIncomingChatMessage('Hi there!', 'Other Driver');
      await detoxExpect(element(by.text('Hi there!'))).toBeVisible();
    });
  });

  // Helper functions
  async function authenticateDriver() {
    await element(by.id('phone-input')).typeText('+263771234568');
    await element(by.id('send-otp-button')).tap();
    await element(by.id('otp-input')).typeText('123456');
    await element(by.id('verify-otp-button')).tap();
  }

  async function goOnline() {
    await element(by.id('online-toggle')).tap();
  }

  async function simulateRideRequest() {
    await device.sendUserNotification({
      trigger: { type: 'push' },
      title: 'New Ride Request',
      body: 'Pickup: 123 Main Street',
      payload: {
        type: 'ride_request',
        ride_id: 'test-ride-1',
        pickup_address: '123 Main Street',
        destination_address: '456 Oak Avenue',
        estimated_fare: 12.50
      }
    });
  }

  async function acceptTestRide() {
    await goOnline();
    await simulateRideRequest();
    await element(by.id('accept-ride-button')).tap();
  }

  async function simulateIncomingChatMessage(message: string, sender: string) {
    await device.sendUserNotification({
      trigger: { type: 'push' },
      title: `${sender} sent a message`,
      body: message,
      payload: {
        type: 'chat_message',
        message,
        sender
      }
    });
  }
});
```

This comprehensive end-to-end testing framework ensures all user workflows function correctly across the entire Taxicab platform with proper mobile and web testing coverage, real-time feature validation, and cross-platform compatibility verification.
