# 2. Mobile App Development

## 📱 **Mobile App Overview**

The Taxicab platform includes two React Native mobile applications: a passenger app for booking rides and a driver app for accepting and managing rides. Both apps are built with TypeScript, React Navigation, and integrate seamlessly with the Supabase backend.

### **Mobile Architecture Principles**
- **Cross-Platform Compatibility**: Single codebase for iOS and Android
- **Native Performance**: Optimized for mobile performance and user experience
- **Offline Capability**: Core functionality available without internet connection
- **Real-Time Updates**: Live location tracking and ride status updates
- **Security First**: Secure authentication and data handling

## 🏗️ **Project Structure**

### **1. Passenger App Structure**
```
apps/passenger-app/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── common/         # Common components
│   │   ├── forms/          # Form components
│   │   ├── maps/           # Map-related components
│   │   └── ui/             # UI components
│   ├── screens/            # Screen components
│   │   ├── auth/           # Authentication screens
│   │   ├── booking/        # Ride booking screens
│   │   ├── history/        # Ride history screens
│   │   ├── profile/        # Profile management screens
│   │   └── support/        # Support screens
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API and external services
│   ├── stores/             # State management
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript definitions
│   └── assets/             # Images, fonts, etc.
├── android/                # Android-specific code
├── ios/                    # iOS-specific code
├── package.json
└── metro.config.js
```

### **2. Driver App Structure**
```
apps/driver-app/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   │   ├── auth/           # Authentication screens
│   │   ├── rides/          # Ride management screens
│   │   ├── earnings/       # Earnings and analytics
│   │   ├── profile/        # Profile and vehicle management
│   │   ├── chat/           # Driver community chat
│   │   └── support/        # Support screens
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API and location services
│   ├── stores/             # State management
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript definitions
│   └── assets/             # Images, fonts, etc.
├── android/                # Android-specific code
├── ios/                    # iOS-specific code
├── package.json
└── metro.config.js
```

### **3. Dependencies Configuration**
```json
// package.json (shared dependencies)
{
  "dependencies": {
    "react": "18.2.0",
    "react-native": "0.72.6",
    "@react-navigation/native": "^6.1.0",
    "@react-navigation/stack": "^6.3.0",
    "@react-navigation/bottom-tabs": "^6.5.0",
    "@supabase/supabase-js": "^2.38.0",
    "react-native-maps": "^1.8.0",
    "react-native-geolocation-service": "^5.3.0",
    "@react-native-async-storage/async-storage": "^1.19.0",
    "react-native-keychain": "^8.1.0",
    "@react-native-firebase/app": "^18.6.0",
    "@react-native-firebase/messaging": "^18.6.0",
    "react-native-permissions": "^3.10.0",
    "react-native-vector-icons": "^10.0.0",
    "zustand": "^4.4.0",
    "@tanstack/react-query": "^5.0.0",
    "react-hook-form": "^7.47.0",
    "zod": "^3.22.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-native": "^0.72.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "metro-react-native-babel-preset": "^0.77.0",
    "typescript": "^5.0.0"
  }
}
```

## 🚗 **Passenger App Implementation**

### **1. Authentication Flow**
```typescript
// src/screens/auth/PhoneAuthScreen.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

export const PhoneAuthScreen: React.FC = () => {
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const { sendOTP, verifyOTP, loading, error } = useAuth();

  const handleSendOTP = async () => {
    try {
      await sendOTP(phone);
      setStep('otp');
    } catch (error) {
      Alert.alert('Error', 'Failed to send verification code');
    }
  };

  const handleVerifyOTP = async () => {
    try {
      await verifyOTP(phone, otp);
      // Navigation handled by auth state change
    } catch (error) {
      Alert.alert('Error', 'Invalid verification code');
    }
  };

  const formatPhoneNumber = (text: string) => {
    // Format Zimbabwe phone numbers
    let cleaned = text.replace(/\D/g, '');
    if (cleaned.startsWith('0')) {
      cleaned = '263' + cleaned.substring(1);
    }
    if (!cleaned.startsWith('263')) {
      cleaned = '263' + cleaned;
    }
    return '+' + cleaned;
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        <Text style={styles.title}>Welcome to Taxicab</Text>
        <Text style={styles.subtitle}>
          {step === 'phone' 
            ? 'Enter your phone number to get started'
            : 'Enter the verification code sent to your phone'
          }
        </Text>

        {step === 'phone' ? (
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Phone number (e.g., 0771234567)"
              value={phone}
              onChangeText={(text) => setPhone(formatPhoneNumber(text))}
              keyboardType="phone-pad"
              autoFocus
            />
            <TouchableOpacity
              style={[styles.button, !phone && styles.buttonDisabled]}
              onPress={handleSendOTP}
              disabled={!phone || loading}
            >
              {loading ? (
                <LoadingSpinner color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Send Code</Text>
              )}
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Enter 6-digit code"
              value={otp}
              onChangeText={setOtp}
              keyboardType="number-pad"
              maxLength={6}
              autoFocus
            />
            <TouchableOpacity
              style={[styles.button, otp.length !== 6 && styles.buttonDisabled]}
              onPress={handleVerifyOTP}
              disabled={otp.length !== 6 || loading}
            >
              {loading ? (
                <LoadingSpinner color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Verify</Text>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.linkButton}
              onPress={() => setStep('phone')}
            >
              <Text style={styles.linkText}>Change phone number</Text>
            </TouchableOpacity>
          </View>
        )}

        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    color: '#6b7280',
  },
  inputContainer: {
    marginBottom: 24,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  button: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    color: '#3b82f6',
    fontSize: 14,
  },
  errorText: {
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 16,
  },
});
```

### **2. Ride Booking Screen**
```typescript
// src/screens/booking/RideBookingScreen.tsx
import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { useLocation } from '@/hooks/useLocation';
import { useRideBooking } from '@/hooks/useRideBooking';
import { LocationInput } from '@/components/booking/LocationInput';
import { ServiceTypeSelector } from '@/components/booking/ServiceTypeSelector';
import { FareEstimate } from '@/components/booking/FareEstimate';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

const { width, height } = Dimensions.get('window');

export const RideBookingScreen: React.FC = () => {
  const mapRef = useRef<MapView>(null);
  const [pickupLocation, setPickupLocation] = useState<Location | null>(null);
  const [destinationLocation, setDestinationLocation] = useState<Location | null>(null);
  const [serviceType, setServiceType] = useState<ServiceType>('movers_ride');
  
  const { currentLocation, loading: locationLoading } = useLocation();
  const { 
    createRide, 
    fareEstimate, 
    calculateFare, 
    loading: bookingLoading 
  } = useRideBooking();

  React.useEffect(() => {
    if (currentLocation) {
      setPickupLocation(currentLocation);
    }
  }, [currentLocation]);

  React.useEffect(() => {
    if (pickupLocation && destinationLocation) {
      calculateFare(pickupLocation, destinationLocation, serviceType);
    }
  }, [pickupLocation, destinationLocation, serviceType]);

  const handleBookRide = async () => {
    if (!pickupLocation || !destinationLocation) {
      Alert.alert('Error', 'Please select pickup and destination locations');
      return;
    }

    try {
      const ride = await createRide({
        pickup_location: pickupLocation,
        destination_location: destinationLocation,
        service_type: serviceType,
      });

      // Navigate to ride tracking screen
      navigation.navigate('RideTracking', { rideId: ride.id });
    } catch (error) {
      Alert.alert('Error', 'Failed to book ride. Please try again.');
    }
  };

  const fitMapToLocations = () => {
    if (pickupLocation && destinationLocation && mapRef.current) {
      mapRef.current.fitToCoordinates(
        [pickupLocation, destinationLocation],
        {
          edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
          animated: true,
        }
      );
    }
  };

  if (locationLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size="large" />
        <Text style={styles.loadingText}>Getting your location...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={{
          latitude: currentLocation?.latitude || -17.8252,
          longitude: currentLocation?.longitude || 31.0335,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        showsUserLocation
        showsMyLocationButton
      >
        {pickupLocation && (
          <Marker
            coordinate={pickupLocation}
            title="Pickup Location"
            pinColor="green"
          />
        )}
        {destinationLocation && (
          <Marker
            coordinate={destinationLocation}
            title="Destination"
            pinColor="red"
          />
        )}
      </MapView>

      <View style={styles.bottomSheet}>
        <LocationInput
          label="Pickup Location"
          value={pickupLocation}
          onLocationSelect={setPickupLocation}
          placeholder="Enter pickup location"
        />
        
        <LocationInput
          label="Destination"
          value={destinationLocation}
          onLocationSelect={setDestinationLocation}
          placeholder="Where to?"
        />

        <ServiceTypeSelector
          selectedType={serviceType}
          onTypeSelect={setServiceType}
        />

        {fareEstimate && (
          <FareEstimate
            estimate={fareEstimate}
            onDetailsPress={() => {/* Show fare breakdown */}}
          />
        )}

        <TouchableOpacity
          style={[
            styles.bookButton,
            (!pickupLocation || !destinationLocation || bookingLoading) && styles.bookButtonDisabled
          ]}
          onPress={handleBookRide}
          disabled={!pickupLocation || !destinationLocation || bookingLoading}
        >
          {bookingLoading ? (
            <LoadingSpinner color="#fff" />
          ) : (
            <Text style={styles.bookButtonText}>Book Ride</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  bookButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  bookButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  bookButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
});
```

## 🚕 **Driver App Implementation**

### **1. Driver Dashboard**
```typescript
// src/screens/dashboard/DriverDashboardScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Switch } from 'react-native';
import { useDriverStatus } from '@/hooks/useDriverStatus';
import { useDriverEarnings } from '@/hooks/useDriverEarnings';
import { useRideRequests } from '@/hooks/useRideRequests';
import { EarningsCard } from '@/components/dashboard/EarningsCard';
import { RideRequestModal } from '@/components/rides/RideRequestModal';
import { StatsCard } from '@/components/dashboard/StatsCard';

export const DriverDashboardScreen: React.FC = () => {
  const { 
    isOnline, 
    toggleOnlineStatus, 
    loading: statusLoading 
  } = useDriverStatus();
  
  const { 
    todayEarnings, 
    weekEarnings, 
    monthEarnings,
    loading: earningsLoading 
  } = useDriverEarnings();
  
  const { 
    pendingRequest, 
    acceptRide, 
    rejectRide,
    loading: requestLoading 
  } = useRideRequests();

  const handleToggleOnline = async () => {
    try {
      await toggleOnlineStatus();
    } catch (error) {
      Alert.alert('Error', 'Failed to update online status');
    }
  };

  const handleAcceptRide = async () => {
    if (!pendingRequest) return;
    
    try {
      await acceptRide(pendingRequest.ride_id);
      // Navigate to ride screen
    } catch (error) {
      Alert.alert('Error', 'Failed to accept ride');
    }
  };

  const handleRejectRide = async () => {
    if (!pendingRequest) return;
    
    try {
      await rejectRide(pendingRequest.ride_id);
    } catch (error) {
      Alert.alert('Error', 'Failed to reject ride');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Good morning, Driver!</Text>
        <View style={styles.onlineToggle}>
          <Text style={[styles.statusText, isOnline && styles.onlineText]}>
            {isOnline ? 'Online' : 'Offline'}
          </Text>
          <Switch
            value={isOnline}
            onValueChange={handleToggleOnline}
            disabled={statusLoading}
            trackColor={{ false: '#d1d5db', true: '#10b981' }}
            thumbColor={isOnline ? '#fff' : '#f3f4f6'}
          />
        </View>
      </View>

      <View style={styles.statsContainer}>
        <StatsCard
          title="Today's Rides"
          value={todayEarnings?.total_rides || 0}
          icon="car"
        />
        <StatsCard
          title="Online Hours"
          value={`${todayEarnings?.online_hours || 0}h`}
          icon="clock"
        />
      </View>

      <EarningsCard
        todayEarnings={todayEarnings?.net_earnings || 0}
        weekEarnings={weekEarnings?.net_earnings || 0}
        monthEarnings={monthEarnings?.net_earnings || 0}
        loading={earningsLoading}
      />

      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>View Earnings</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>Driver Chat</Text>
        </TouchableOpacity>
      </View>

      {/* Ride Request Modal */}
      <RideRequestModal
        visible={!!pendingRequest}
        request={pendingRequest}
        onAccept={handleAcceptRide}
        onReject={handleRejectRide}
        loading={requestLoading}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  greeting: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  onlineToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginRight: 8,
    fontSize: 16,
    color: '#6b7280',
  },
  onlineText: {
    color: '#10b981',
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
```

### **2. Real-Time Location Tracking**
```typescript
// src/hooks/useLocationTracking.ts
import { useEffect, useRef } from 'react';
import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Platform } from 'react-native';
import { useDriverLocationService } from '@/services/driverLocationService';

export const useLocationTracking = (driverId: string, isOnline: boolean) => {
  const watchId = useRef<number | null>(null);
  const { updateLocation } = useDriverLocationService();

  useEffect(() => {
    if (isOnline && driverId) {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }

    return () => stopLocationTracking();
  }, [isOnline, driverId]);

  const requestLocationPermission = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'Taxicab needs access to your location to track your position while driving.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true; // iOS permissions handled in Info.plist
  };

  const startLocationTracking = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.warn('Location permission denied');
      return;
    }

    watchId.current = Geolocation.watchPosition(
      (position) => {
        const { latitude, longitude, heading, speed, accuracy } = position.coords;
        
        updateLocation({
          latitude,
          longitude,
          heading: heading || undefined,
          speed: speed || undefined,
          accuracy,
        });
      },
      (error) => {
        console.error('Location tracking error:', error);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Update every 10 meters
        interval: 5000, // Update every 5 seconds
        fastestInterval: 2000, // Fastest update every 2 seconds
      }
    );
  };

  const stopLocationTracking = () => {
    if (watchId.current !== null) {
      Geolocation.clearWatch(watchId.current);
      watchId.current = null;
    }
  };

  return {
    startLocationTracking,
    stopLocationTracking,
  };
};
```

### **3. Driver Community Chat**
```typescript
// src/screens/chat/DriverChatScreen.tsx
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDriverChat } from '@/hooks/useDriverChat';
import { ChatMessage } from '@/components/chat/ChatMessage';
import { OnlineDriversList } from '@/components/chat/OnlineDriversList';

export const DriverChatScreen: React.FC = () => {
  const [message, setMessage] = useState('');
  const [showOnlineDrivers, setShowOnlineDrivers] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  
  const {
    messages,
    onlineDrivers,
    sendMessage,
    loading,
  } = useDriverChat('harare'); // City-based chat

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    try {
      await sendMessage(message.trim());
      setMessage('');
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <ChatMessage message={item} />
  );

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Driver Chat - Harare</Text>
        <TouchableOpacity
          style={styles.onlineButton}
          onPress={() => setShowOnlineDrivers(!showOnlineDrivers)}
        >
          <Text style={styles.onlineButtonText}>
            Online ({onlineDrivers.length})
          </Text>
        </TouchableOpacity>
      </View>

      {showOnlineDrivers && (
        <OnlineDriversList
          drivers={onlineDrivers}
          onClose={() => setShowOnlineDrivers(false)}
        />
      )}

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
      />

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={message}
          onChangeText={setMessage}
          placeholder="Type a message..."
          multiline
          maxLength={1000}
        />
        <TouchableOpacity
          style={[styles.sendButton, !message.trim() && styles.sendButtonDisabled]}
          onPress={handleSendMessage}
          disabled={!message.trim() || loading}
        >
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  onlineButton: {
    backgroundColor: '#10b981',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  onlineButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  sendButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
```

This comprehensive mobile app implementation provides native performance, real-time capabilities, and user-friendly interfaces for both passengers and drivers with proper offline handling and security measures.
