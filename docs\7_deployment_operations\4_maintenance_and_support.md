# 4. Maintenance and Support

## 🔧 **Maintenance and Support Overview**

This document outlines comprehensive maintenance procedures and support processes for the Taxicab platform to ensure optimal performance, security, and user satisfaction throughout the platform's lifecycle.

### **Maintenance Strategy**
- **Preventive Maintenance**: Regular updates and optimizations to prevent issues
- **Corrective Maintenance**: Rapid response to fix identified problems
- **Adaptive Maintenance**: Updates to meet changing business requirements
- **Perfective Maintenance**: Enhancements to improve performance and usability
- **24/7 Support**: Round-the-clock monitoring and incident response

## 📅 **Maintenance Schedule**

### **1. Daily Maintenance Tasks**
```bash
#!/bin/bash
# scripts/maintenance/daily-maintenance.sh

set -e

LOG_FILE="/var/log/taxicab/daily-maintenance-$(date +%Y%m%d).log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_FILE}"
}

# Daily health checks
perform_health_checks() {
    log "Starting daily health checks"
    
    # Database health check
    if psql "${DATABASE_URL}" -c "SELECT 1;" > /dev/null 2>&1; then
        log "✓ Database connectivity: OK"
    else
        log "✗ Database connectivity: FAILED"
        send_alert "Database connectivity check failed"
    fi
    
    # API health check
    if curl -f "${API_BASE_URL}/health" > /dev/null 2>&1; then
        log "✓ API health: OK"
    else
        log "✗ API health: FAILED"
        send_alert "API health check failed"
    fi
    
    # Storage health check
    storage_status=$(curl -s "${SUPABASE_URL}/rest/v1/rpc/storage_health" \
        -H "apikey: ${SUPABASE_ANON_KEY}" | jq -r '.status')
    
    if [ "${storage_status}" = "healthy" ]; then
        log "✓ Storage health: OK"
    else
        log "✗ Storage health: FAILED"
        send_alert "Storage health check failed"
    fi
    
    log "Daily health checks completed"
}

# Database maintenance
perform_database_maintenance() {
    log "Starting database maintenance"
    
    # Update table statistics
    psql "${DATABASE_URL}" -c "ANALYZE;"
    log "✓ Table statistics updated"
    
    # Cleanup old sessions
    psql "${DATABASE_URL}" -c "
        DELETE FROM auth.sessions 
        WHERE expires_at < NOW() - INTERVAL '7 days';
    "
    log "✓ Old sessions cleaned up"
    
    # Cleanup old metrics
    psql "${DATABASE_URL}" -c "
        DELETE FROM metrics_api_responses 
        WHERE timestamp < NOW() - INTERVAL '30 days';
        
        DELETE FROM metrics_user_actions 
        WHERE timestamp < NOW() - INTERVAL '90 days';
    "
    log "✓ Old metrics cleaned up"
    
    # Vacuum analyze critical tables
    critical_tables=("rides" "users" "drivers" "payments")
    for table in "${critical_tables[@]}"; do
        psql "${DATABASE_URL}" -c "VACUUM ANALYZE ${table};"
        log "✓ Vacuumed and analyzed table: ${table}"
    done
    
    log "Database maintenance completed"
}

# Log rotation and cleanup
perform_log_cleanup() {
    log "Starting log cleanup"
    
    # Rotate application logs
    find /var/log/taxicab -name "*.log" -mtime +7 -exec gzip {} \;
    find /var/log/taxicab -name "*.log.gz" -mtime +30 -delete
    
    # Cleanup temporary files
    find /tmp -name "taxicab_*" -mtime +1 -delete
    
    log "Log cleanup completed"
}

# Performance monitoring
check_performance_metrics() {
    log "Checking performance metrics"
    
    # Check database performance
    slow_queries=$(psql "${DATABASE_URL}" -t -c "
        SELECT COUNT(*) FROM pg_stat_statements 
        WHERE mean_time > 1000;
    " | tr -d ' ')
    
    if [ "${slow_queries}" -gt 10 ]; then
        log "⚠ Warning: ${slow_queries} slow queries detected"
        send_alert "High number of slow queries: ${slow_queries}"
    else
        log "✓ Database performance: OK"
    fi
    
    # Check API response times
    avg_response_time=$(curl -s "${API_BASE_URL}/metrics/response-time" | jq -r '.average')
    
    if (( $(echo "${avg_response_time} > 1000" | bc -l) )); then
        log "⚠ Warning: High API response time: ${avg_response_time}ms"
        send_alert "High API response time: ${avg_response_time}ms"
    else
        log "✓ API performance: OK"
    fi
    
    log "Performance metrics check completed"
}

# Security checks
perform_security_checks() {
    log "Starting security checks"
    
    # Check for failed login attempts
    failed_logins=$(psql "${DATABASE_URL}" -t -c "
        SELECT COUNT(*) FROM auth.audit_log_entries 
        WHERE event_type = 'login_failed' 
        AND created_at > NOW() - INTERVAL '24 hours';
    " | tr -d ' ')
    
    if [ "${failed_logins}" -gt 100 ]; then
        log "⚠ Warning: High number of failed logins: ${failed_logins}"
        send_alert "Potential brute force attack: ${failed_logins} failed logins"
    else
        log "✓ Login security: OK"
    fi
    
    # Check SSL certificate expiry
    cert_expiry=$(echo | openssl s_client -servername admin.taxicab.co.zw -connect admin.taxicab.co.zw:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    cert_expiry_epoch=$(date -d "${cert_expiry}" +%s)
    current_epoch=$(date +%s)
    days_until_expiry=$(( (cert_expiry_epoch - current_epoch) / 86400 ))
    
    if [ "${days_until_expiry}" -lt 30 ]; then
        log "⚠ Warning: SSL certificate expires in ${days_until_expiry} days"
        send_alert "SSL certificate expires in ${days_until_expiry} days"
    else
        log "✓ SSL certificate: OK (${days_until_expiry} days remaining)"
    fi
    
    log "Security checks completed"
}

# Send alert function
send_alert() {
    local message=$1
    
    # Send to Slack
    if [ -n "${SLACK_WEBHOOK_URL}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 Taxicab Alert: ${message}\"}" \
            "${SLACK_WEBHOOK_URL}"
    fi
    
    # Send to monitoring system
    if [ -n "${MONITORING_WEBHOOK_URL}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"alert\":\"${message}\",\"timestamp\":\"$(date -Iseconds)\",\"severity\":\"warning\"}" \
            "${MONITORING_WEBHOOK_URL}"
    fi
}

# Main execution
main() {
    log "Starting daily maintenance routine"
    
    perform_health_checks
    perform_database_maintenance
    perform_log_cleanup
    check_performance_metrics
    perform_security_checks
    
    log "Daily maintenance routine completed successfully"
}

main "$@"
```

### **2. Weekly Maintenance Tasks**
```bash
#!/bin/bash
# scripts/maintenance/weekly-maintenance.sh

set -e

LOG_FILE="/var/log/taxicab/weekly-maintenance-$(date +%Y%m%d).log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_FILE}"
}

# Database optimization
optimize_database() {
    log "Starting database optimization"
    
    # Full vacuum on large tables during low traffic
    large_tables=("rides" "driver_locations" "metrics_api_responses")
    for table in "${large_tables[@]}"; do
        log "Optimizing table: ${table}"
        psql "${DATABASE_URL}" -c "VACUUM FULL ${table};"
    done
    
    # Reindex critical indexes
    psql "${DATABASE_URL}" -c "REINDEX INDEX CONCURRENTLY idx_rides_status;"
    psql "${DATABASE_URL}" -c "REINDEX INDEX CONCURRENTLY idx_rides_created_at;"
    psql "${DATABASE_URL}" -c "REINDEX INDEX CONCURRENTLY idx_driver_locations_driver_id;"
    
    # Update database statistics
    psql "${DATABASE_URL}" -c "VACUUM ANALYZE;"
    
    log "Database optimization completed"
}

# Security updates
apply_security_updates() {
    log "Checking for security updates"
    
    # Check for Supabase updates
    # This would typically involve checking Supabase dashboard or API
    
    # Check for dependency updates
    cd /opt/taxicab
    npm audit --audit-level high
    
    if [ $? -ne 0 ]; then
        log "⚠ Security vulnerabilities found in dependencies"
        # In production, you might want to automatically apply fixes
        # npm audit fix --force
    else
        log "✓ No high-severity vulnerabilities found"
    fi
    
    log "Security update check completed"
}

# Performance analysis
analyze_performance() {
    log "Starting performance analysis"
    
    # Generate database performance report
    psql "${DATABASE_URL}" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            seq_scan,
            seq_tup_read,
            idx_scan,
            idx_tup_fetch
        FROM pg_stat_user_tables 
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    " > "/tmp/table_stats_$(date +%Y%m%d).txt"
    
    # Analyze slow queries
    psql "${DATABASE_URL}" -c "
        SELECT 
            query,
            calls,
            total_time,
            mean_time,
            rows
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC 
        LIMIT 20;
    " > "/tmp/slow_queries_$(date +%Y%m%d).txt"
    
    # Check for unused indexes
    psql "${DATABASE_URL}" -c "
        SELECT 
            schemaname,
            tablename,
            indexname,
            idx_scan,
            pg_size_pretty(pg_relation_size(indexrelid)) as size
        FROM pg_stat_user_indexes 
        WHERE idx_scan = 0 
        AND pg_relation_size(indexrelid) > 1024*1024; -- Larger than 1MB
    " > "/tmp/unused_indexes_$(date +%Y%m%d).txt"
    
    log "Performance analysis completed"
}

# Backup verification
verify_backups() {
    log "Starting backup verification"
    
    # Check latest backup
    latest_backup=$(aws s3 ls s3://taxicab-backups/database/ --recursive | sort | tail -n 1 | awk '{print $4}')
    
    if [ -n "${latest_backup}" ]; then
        backup_date=$(echo "${latest_backup}" | grep -o '[0-9]\{8\}_[0-9]\{6\}')
        backup_age_hours=$(( ($(date +%s) - $(date -d "${backup_date:0:8} ${backup_date:9:2}:${backup_date:11:2}:${backup_date:13:2}" +%s)) / 3600 ))
        
        if [ "${backup_age_hours}" -lt 25 ]; then
            log "✓ Latest backup is ${backup_age_hours} hours old"
        else
            log "⚠ Warning: Latest backup is ${backup_age_hours} hours old"
            send_alert "Backup is older than expected: ${backup_age_hours} hours"
        fi
    else
        log "✗ No backups found"
        send_alert "No database backups found"
    fi
    
    log "Backup verification completed"
}

main() {
    log "Starting weekly maintenance routine"
    
    optimize_database
    apply_security_updates
    analyze_performance
    verify_backups
    
    log "Weekly maintenance routine completed successfully"
}

main "$@"
```

### **3. Monthly Maintenance Tasks**
```typescript
// scripts/maintenance/monthly-maintenance.ts
import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import path from 'path';

interface MaintenanceReport {
  timestamp: string;
  databaseHealth: any;
  performanceMetrics: any;
  securityAudit: any;
  userAnalytics: any;
  recommendations: string[];
}

class MonthlyMaintenance {
  private supabase: any;
  private report: MaintenanceReport;

  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    this.report = {
      timestamp: new Date().toISOString(),
      databaseHealth: {},
      performanceMetrics: {},
      securityAudit: {},
      userAnalytics: {},
      recommendations: [],
    };
  }

  async runMonthlyMaintenance(): Promise<void> {
    console.log('Starting monthly maintenance routine');

    try {
      await this.analyzeDatabaseHealth();
      await this.generatePerformanceReport();
      await this.conductSecurityAudit();
      await this.analyzeUserMetrics();
      await this.generateRecommendations();
      await this.saveReport();
      await this.sendReport();

      console.log('Monthly maintenance completed successfully');
    } catch (error) {
      console.error('Monthly maintenance failed:', error);
      throw error;
    }
  }

  private async analyzeDatabaseHealth(): Promise<void> {
    console.log('Analyzing database health');

    // Table sizes and growth
    const { data: tableSizes } = await this.supabase.rpc('get_table_sizes');
    
    // Connection statistics
    const { data: connectionStats } = await this.supabase.rpc('get_connection_stats');
    
    // Index usage
    const { data: indexUsage } = await this.supabase.rpc('get_index_usage');
    
    // Query performance
    const { data: queryPerf } = await this.supabase.rpc('get_query_performance');

    this.report.databaseHealth = {
      tableSizes,
      connectionStats,
      indexUsage,
      queryPerformance: queryPerf,
    };

    // Add recommendations based on analysis
    if (tableSizes?.some((table: any) => table.size_bytes > 10 * 1024 * 1024 * 1024)) {
      this.report.recommendations.push('Consider partitioning large tables (>10GB)');
    }

    if (indexUsage?.some((index: any) => index.idx_scan === 0)) {
      this.report.recommendations.push('Remove unused indexes to improve write performance');
    }
  }

  private async generatePerformanceReport(): Promise<void> {
    console.log('Generating performance report');

    // API response times
    const { data: apiMetrics } = await this.supabase
      .from('metrics_api_responses')
      .select('*')
      .gte('timestamp', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    // Calculate averages
    const avgResponseTime = apiMetrics?.reduce((sum, metric) => sum + metric.response_time, 0) / apiMetrics?.length || 0;
    const p95ResponseTime = this.calculatePercentile(apiMetrics?.map(m => m.response_time) || [], 95);

    // Error rates
    const errorRate = apiMetrics?.filter(m => m.status_code >= 400).length / apiMetrics?.length || 0;

    // User activity metrics
    const { data: userActivity } = await this.supabase
      .from('metrics_user_actions')
      .select('*')
      .gte('timestamp', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    this.report.performanceMetrics = {
      avgResponseTime,
      p95ResponseTime,
      errorRate,
      totalRequests: apiMetrics?.length || 0,
      userActions: userActivity?.length || 0,
    };

    // Performance recommendations
    if (avgResponseTime > 500) {
      this.report.recommendations.push('API response times are above target (500ms)');
    }

    if (errorRate > 0.01) {
      this.report.recommendations.push('Error rate is above target (1%)');
    }
  }

  private async conductSecurityAudit(): Promise<void> {
    console.log('Conducting security audit');

    // Check for suspicious login patterns
    const { data: failedLogins } = await this.supabase
      .from('auth.audit_log_entries')
      .select('*')
      .eq('event_type', 'login_failed')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    // Check for privilege escalations
    const { data: privilegeChanges } = await this.supabase
      .from('auth.audit_log_entries')
      .select('*')
      .eq('event_type', 'role_changed')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    // Check for data access patterns
    const { data: dataAccess } = await this.supabase.rpc('analyze_data_access_patterns');

    this.report.securityAudit = {
      failedLoginAttempts: failedLogins?.length || 0,
      privilegeChanges: privilegeChanges?.length || 0,
      suspiciousDataAccess: dataAccess?.filter((access: any) => access.is_suspicious) || [],
    };

    // Security recommendations
    if ((failedLogins?.length || 0) > 1000) {
      this.report.recommendations.push('High number of failed login attempts detected');
    }
  }

  private async analyzeUserMetrics(): Promise<void> {
    console.log('Analyzing user metrics');

    // User growth
    const { data: userGrowth } = await this.supabase.rpc('get_user_growth_metrics');
    
    // Ride metrics
    const { data: rideMetrics } = await this.supabase.rpc('get_ride_metrics');
    
    // Driver metrics
    const { data: driverMetrics } = await this.supabase.rpc('get_driver_metrics');

    this.report.userAnalytics = {
      userGrowth,
      rideMetrics,
      driverMetrics,
    };
  }

  private async generateRecommendations(): Promise<void> {
    console.log('Generating maintenance recommendations');

    // Add general recommendations based on metrics
    const { databaseHealth, performanceMetrics, userAnalytics } = this.report;

    // Database recommendations
    if (databaseHealth.tableSizes?.some((table: any) => table.growth_rate > 0.5)) {
      this.report.recommendations.push('Monitor rapidly growing tables for optimization opportunities');
    }

    // Performance recommendations
    if (performanceMetrics.p95ResponseTime > 2000) {
      this.report.recommendations.push('Consider implementing caching for slow endpoints');
    }

    // Business recommendations
    if (userAnalytics.rideMetrics?.completion_rate < 0.9) {
      this.report.recommendations.push('Investigate factors affecting ride completion rate');
    }
  }

  private async saveReport(): Promise<void> {
    const reportPath = path.join('/var/log/taxicab', `monthly-report-${new Date().toISOString().slice(0, 7)}.json`);
    await fs.writeFile(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`Report saved to ${reportPath}`);
  }

  private async sendReport(): Promise<void> {
    // Send report to stakeholders
    const summary = this.generateReportSummary();
    
    // Send to Slack
    if (process.env.SLACK_WEBHOOK_URL) {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: '📊 Monthly Maintenance Report',
          attachments: [{
            color: 'good',
            text: summary,
            ts: Math.floor(Date.now() / 1000),
          }],
        }),
      });
    }

    // Send detailed report via email
    // Implementation depends on your email service
  }

  private generateReportSummary(): string {
    const { performanceMetrics, userAnalytics, recommendations } = this.report;
    
    return `
Monthly Maintenance Report Summary:

📈 Performance Metrics:
• Average API Response Time: ${performanceMetrics.avgResponseTime?.toFixed(2)}ms
• Error Rate: ${(performanceMetrics.errorRate * 100)?.toFixed(2)}%
• Total Requests: ${performanceMetrics.totalRequests?.toLocaleString()}

👥 User Analytics:
• Active Users: ${userAnalytics.userGrowth?.active_users || 'N/A'}
• Total Rides: ${userAnalytics.rideMetrics?.total_rides || 'N/A'}
• Active Drivers: ${userAnalytics.driverMetrics?.active_drivers || 'N/A'}

🔧 Recommendations (${recommendations.length}):
${recommendations.slice(0, 5).map(r => `• ${r}`).join('\n')}

Full report available in system logs.
    `.trim();
  }

  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }
}

// Execute monthly maintenance
const maintenance = new MonthlyMaintenance();
maintenance.runMonthlyMaintenance()
  .then(() => console.log('Monthly maintenance completed'))
  .catch(error => {
    console.error('Monthly maintenance failed:', error);
    process.exit(1);
  });
```

## 🎫 **Support Ticket System**

### **1. Support Ticket Management**
```typescript
// packages/support/src/ticketSystem.ts
export interface SupportTicket {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category: 'technical' | 'billing' | 'feature_request' | 'bug_report';
  reporter: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  assignee?: {
    id: string;
    name: string;
    email: string;
  };
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  resolution?: string;
  attachments: string[];
  comments: TicketComment[];
}

export interface TicketComment {
  id: string;
  author: {
    id: string;
    name: string;
    role: string;
  };
  content: string;
  created_at: string;
  is_internal: boolean;
}

export class SupportTicketSystem {
  private supabase: any;

  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  async createTicket(ticketData: Partial<SupportTicket>): Promise<SupportTicket> {
    const ticket: SupportTicket = {
      id: crypto.randomUUID(),
      title: ticketData.title!,
      description: ticketData.description!,
      priority: ticketData.priority || 'medium',
      status: 'open',
      category: ticketData.category!,
      reporter: ticketData.reporter!,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      attachments: [],
      comments: [],
    };

    const { data, error } = await this.supabase
      .from('support_tickets')
      .insert(ticket)
      .select()
      .single();

    if (error) throw error;

    // Send notification to support team
    await this.notifySupport(ticket);

    return data;
  }

  async updateTicket(ticketId: string, updates: Partial<SupportTicket>): Promise<SupportTicket> {
    const { data, error } = await this.supabase
      .from('support_tickets')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', ticketId)
      .select()
      .single();

    if (error) throw error;

    // Send notification if status changed
    if (updates.status) {
      await this.notifyStatusChange(data);
    }

    return data;
  }

  async addComment(ticketId: string, comment: Omit<TicketComment, 'id' | 'created_at'>): Promise<void> {
    const newComment: TicketComment = {
      id: crypto.randomUUID(),
      ...comment,
      created_at: new Date().toISOString(),
    };

    const { error } = await this.supabase
      .from('support_ticket_comments')
      .insert({
        ticket_id: ticketId,
        ...newComment,
      });

    if (error) throw error;

    // Update ticket's updated_at timestamp
    await this.supabase
      .from('support_tickets')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', ticketId);

    // Send notification if not internal comment
    if (!comment.is_internal) {
      await this.notifyCommentAdded(ticketId, newComment);
    }
  }

  private async notifySupport(ticket: SupportTicket): Promise<void> {
    const message = `New support ticket created:
    
Title: ${ticket.title}
Priority: ${ticket.priority.toUpperCase()}
Category: ${ticket.category}
Reporter: ${ticket.reporter.name} (${ticket.reporter.email})

Description: ${ticket.description}

View ticket: https://admin.taxicab.co.zw/support/tickets/${ticket.id}`;

    // Send to support channel
    if (process.env.SUPPORT_SLACK_WEBHOOK) {
      await fetch(process.env.SUPPORT_SLACK_WEBHOOK, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: message,
          channel: '#support',
        }),
      });
    }
  }

  private async notifyStatusChange(ticket: SupportTicket): Promise<void> {
    // Notify ticket reporter of status change
    // Implementation depends on your notification system
  }

  private async notifyCommentAdded(ticketId: string, comment: TicketComment): Promise<void> {
    // Notify relevant parties of new comment
    // Implementation depends on your notification system
  }
}
```

This comprehensive maintenance and support system ensures the Taxicab platform operates optimally with proactive maintenance, systematic monitoring, and efficient support processes to maintain high availability and user satisfaction.
