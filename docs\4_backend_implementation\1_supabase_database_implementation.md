# 1. Supabase Database Implementation

## 🗄️ **Database Implementation Overview**

This document provides comprehensive guidance for implementing the Taxicab platform database using Supabase PostgreSQL with Row Level Security (RLS), real-time subscriptions, and optimized performance configurations.

### **Implementation Principles**
- **Security First**: RLS policies protect all data access
- **Performance Optimized**: Proper indexing and query optimization
- **Scalable Design**: Schema designed for millions of rides and users
- **Real-time Ready**: Tables configured for live subscriptions
- **Audit Compliant**: Complete audit trails for regulatory compliance

## 📋 **Migration Implementation Strategy**

### **1. Migration Sequence**
```sql
-- Migration 001: Core Schema and Types
-- File: supabase/migrations/20240101000001_core_schema.sql

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('passenger', 'driver', 'admin', 'corporate_admin');
CREATE TYPE driver_status AS ENUM ('pending', 'approved', 'suspended', 'rejected');
CREATE TYPE vehicle_category AS ENUM ('lite', 'standard', 'executive', 'xl');
CREATE TYPE service_type AS ENUM (
  'movers_ride', 'movers_exec', 'movers_xl', 
  'movers_cruiser', 'movers_express'
);
CREATE TYPE ride_status AS ENUM (
  'requested', 'accepted', 'driver_arrived', 
  'in_progress', 'completed', 'cancelled'
);
CREATE TYPE payment_method AS ENUM ('cash', 'ecocash', 'onemoney', 'card');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE notification_type AS ENUM (
  'ride_request', 'ride_accepted', 'driver_arrived', 
  'trip_started', 'trip_completed', 'payment_processed', 'rating_received'
);
```

### **2. Core Tables Implementation**
```sql
-- Migration 002: Core Tables
-- File: supabase/migrations/20240101000002_core_tables.sql

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  phone VARCHAR(20) UNIQUE NOT NULL,
  email VARCHAR(255),
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'passenger',
  profile_image_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_phone CHECK (phone ~ '^\+[1-9]\d{1,14}$'),
  CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Taxi associations table
CREATE TABLE taxi_associations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  registration_number VARCHAR(100) UNIQUE,
  contact_person VARCHAR(255),
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),
  address TEXT,
  commission_rate DECIMAL(5,2) DEFAULT 5.00 CHECK (commission_rate >= 0 AND commission_rate <= 50),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vehicles table
CREATE TABLE vehicles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  make VARCHAR(100) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL CHECK (year >= 1990 AND year <= EXTRACT(YEAR FROM NOW()) + 1),
  color VARCHAR(50) NOT NULL,
  license_plate VARCHAR(20) UNIQUE NOT NULL,
  category vehicle_category NOT NULL,
  capacity INTEGER DEFAULT 4 CHECK (capacity >= 1 AND capacity <= 15),
  registration_expiry DATE NOT NULL,
  insurance_expiry DATE NOT NULL,
  inspection_expiry DATE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure future expiry dates
  CONSTRAINT valid_registration_expiry CHECK (registration_expiry > CURRENT_DATE),
  CONSTRAINT valid_insurance_expiry CHECK (insurance_expiry > CURRENT_DATE)
);

-- Drivers table
CREATE TABLE drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  association_id UUID REFERENCES taxi_associations(id),
  license_number VARCHAR(50) UNIQUE NOT NULL,
  license_expiry DATE NOT NULL,
  vehicle_id UUID REFERENCES vehicles(id),
  status driver_status DEFAULT 'pending',
  rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
  total_rides INTEGER DEFAULT 0 CHECK (total_rides >= 0),
  total_earnings DECIMAL(10,2) DEFAULT 0.00 CHECK (total_earnings >= 0),
  is_online BOOLEAN DEFAULT FALSE,
  last_location POINT,
  last_location_update TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure future license expiry
  CONSTRAINT valid_license_expiry CHECK (license_expiry > CURRENT_DATE),
  -- Ensure only approved drivers can be online
  CONSTRAINT online_status_check CHECK (
    (is_online = FALSE) OR (is_online = TRUE AND status = 'approved')
  )
);
```

### **3. Rides and Transactions Tables**
```sql
-- Migration 003: Rides and Transactions
-- File: supabase/migrations/20240101000003_rides_transactions.sql

-- Rides table
CREATE TABLE rides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  passenger_id UUID REFERENCES users(id) NOT NULL,
  driver_id UUID REFERENCES drivers(id),
  service_type service_type NOT NULL DEFAULT 'movers_ride',
  status ride_status DEFAULT 'requested',
  
  -- Location data
  pickup_address TEXT NOT NULL,
  pickup_coordinates POINT NOT NULL,
  destination_address TEXT NOT NULL,
  destination_coordinates POINT NOT NULL,
  
  -- Pricing
  estimated_fare DECIMAL(8,2) CHECK (estimated_fare >= 0),
  final_fare DECIMAL(8,2) CHECK (final_fare >= 0),
  distance_km DECIMAL(8,2) CHECK (distance_km >= 0),
  duration_minutes INTEGER CHECK (duration_minutes >= 0),
  surge_multiplier DECIMAL(3,2) DEFAULT 1.00 CHECK (surge_multiplier >= 1.00),
  
  -- Timestamps
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  cancelled_at TIMESTAMP WITH TIME ZONE,
  
  -- Additional data
  cancellation_reason TEXT,
  passenger_rating INTEGER CHECK (passenger_rating >= 1 AND passenger_rating <= 5),
  driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
  passenger_comment TEXT,
  driver_comment TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure logical timestamp ordering
  CONSTRAINT valid_timestamps CHECK (
    (accepted_at IS NULL OR accepted_at >= requested_at) AND
    (started_at IS NULL OR started_at >= COALESCE(accepted_at, requested_at)) AND
    (completed_at IS NULL OR completed_at >= COALESCE(started_at, accepted_at, requested_at)) AND
    (cancelled_at IS NULL OR cancelled_at >= requested_at)
  ),
  
  -- Ensure only one completion status
  CONSTRAINT single_completion CHECK (
    (completed_at IS NULL AND cancelled_at IS NULL) OR
    (completed_at IS NOT NULL AND cancelled_at IS NULL) OR
    (completed_at IS NULL AND cancelled_at IS NOT NULL)
  )
);

-- Payments table
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID REFERENCES rides(id) NOT NULL,
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  payment_method payment_method NOT NULL,
  payment_status payment_status DEFAULT 'pending',
  transaction_id VARCHAR(255),
  gateway_response JSONB,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure processed timestamp for completed payments
  CONSTRAINT processed_timestamp_check CHECK (
    (payment_status != 'completed') OR 
    (payment_status = 'completed' AND processed_at IS NOT NULL)
  )
);

-- Driver earnings table
CREATE TABLE driver_earnings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  ride_id UUID REFERENCES rides(id) NOT NULL,
  gross_amount DECIMAL(10,2) NOT NULL CHECK (gross_amount >= 0),
  platform_commission DECIMAL(10,2) NOT NULL CHECK (platform_commission >= 0),
  association_commission DECIMAL(10,2) DEFAULT 0.00 CHECK (association_commission >= 0),
  net_earnings DECIMAL(10,2) NOT NULL CHECK (net_earnings >= 0),
  commission_rate DECIMAL(5,2) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure earnings calculation is correct
  CONSTRAINT earnings_calculation_check CHECK (
    net_earnings = gross_amount - platform_commission - association_commission
  ),
  
  -- Unique constraint to prevent duplicate earnings records
  UNIQUE(driver_id, ride_id)
);
```

### **4. Real-time and Communication Tables**
```sql
-- Migration 004: Real-time and Communication
-- File: supabase/migrations/20240101000004_realtime_communication.sql

-- Driver locations table for real-time tracking
CREATE TABLE driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  coordinates POINT NOT NULL,
  heading DECIMAL(5,2) CHECK (heading >= 0 AND heading < 360),
  speed DECIMAL(5,2) CHECK (speed >= 0),
  accuracy DECIMAL(8,2) CHECK (accuracy >= 0),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Partition by timestamp for performance
  CONSTRAINT valid_timestamp CHECK (timestamp <= NOW() + INTERVAL '1 minute')
) PARTITION BY RANGE (timestamp);

-- Create partitions for driver locations (monthly partitions)
CREATE TABLE driver_locations_2024_01 PARTITION OF driver_locations
  FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  type notification_type NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT FALSE,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Ensure read_at is set when is_read is true
  CONSTRAINT read_timestamp_check CHECK (
    (is_read = FALSE AND read_at IS NULL) OR
    (is_read = TRUE AND read_at IS NOT NULL)
  )
);

-- Chat messages table for driver community
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel VARCHAR(100) NOT NULL, -- City-based channels (harare, bulawayo, etc.)
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  message TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_deleted BOOLEAN DEFAULT FALSE,
  
  -- Message length constraints
  CONSTRAINT message_length CHECK (LENGTH(message) >= 1 AND LENGTH(message) <= 1000)
);
```

## 🔐 **Row Level Security Implementation**

### **1. RLS Policies for Core Tables**
```sql
-- Migration 005: Row Level Security Policies
-- File: supabase/migrations/20240101000005_rls_policies.sql

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE taxi_associations ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_earnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "users_admin_all" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Drivers can view passenger profiles during active rides
CREATE POLICY "users_driver_view_passenger" ON users
  FOR SELECT USING (
    role = 'passenger' AND
    EXISTS (
      SELECT 1 FROM rides r
      JOIN drivers d ON r.driver_id = d.id
      WHERE r.passenger_id = users.id
        AND d.user_id = auth.uid()
        AND r.status IN ('accepted', 'driver_arrived', 'in_progress')
    )
  );

-- Rides table policies
CREATE POLICY "rides_passenger_own" ON rides
  FOR SELECT USING (passenger_id = auth.uid());

CREATE POLICY "rides_passenger_create" ON rides
  FOR INSERT WITH CHECK (passenger_id = auth.uid());

CREATE POLICY "rides_driver_assigned" ON rides
  FOR SELECT USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "rides_driver_update" ON rides
  FOR UPDATE USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- Corporate admins can view employee rides
CREATE POLICY "rides_corporate_admin" ON rides
  FOR SELECT USING (
    passenger_id IN (
      SELECT ce.user_id 
      FROM corporate_employees ce
      JOIN corporate_accounts ca ON ce.corporate_account_id = ca.id
      WHERE ca.admin_user_id = auth.uid()
    )
  );

-- Driver locations policies
CREATE POLICY "driver_locations_insert_own" ON driver_locations
  FOR INSERT WITH CHECK (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "driver_locations_passenger_view" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM rides 
      WHERE passenger_id = auth.uid() 
        AND driver_id = driver_locations.driver_id
        AND status IN ('accepted', 'driver_arrived', 'in_progress')
        AND created_at >= NOW() - INTERVAL '24 hours'
    )
  );
```

### **2. Advanced RLS Policies**
```sql
-- Migration 006: Advanced RLS Policies
-- File: supabase/migrations/20240101000006_advanced_rls.sql

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is driver
CREATE OR REPLACE FUNCTION is_driver()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM drivers 
    WHERE user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's driver ID
CREATE OR REPLACE FUNCTION get_driver_id()
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT id FROM drivers 
    WHERE user_id = auth.uid()
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enhanced driver earnings policy
CREATE POLICY "driver_earnings_own" ON driver_earnings
  FOR SELECT USING (driver_id = get_driver_id());

-- Enhanced notifications policy
CREATE POLICY "notifications_own" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "notifications_insert_system" ON notifications
  FOR INSERT WITH CHECK (
    -- Only system (service role) can insert notifications
    auth.jwt() ->> 'role' = 'service_role'
  );

-- Chat messages policies
CREATE POLICY "chat_messages_driver_view" ON chat_messages
  FOR SELECT USING (is_driver());

CREATE POLICY "chat_messages_driver_insert" ON chat_messages
  FOR INSERT WITH CHECK (
    driver_id = get_driver_id() AND is_driver()
  );
```

## 📈 **Performance Optimization**

### **1. Indexes Implementation**
```sql
-- Migration 007: Performance Indexes
-- File: supabase/migrations/20240101000007_indexes.sql

-- Spatial indexes for location-based queries
CREATE INDEX idx_drivers_location_gist ON drivers USING GIST (last_location)
  WHERE is_online = true AND status = 'approved';

CREATE INDEX idx_driver_locations_spatial ON driver_locations USING GIST (coordinates);

CREATE INDEX idx_rides_pickup_gist ON rides USING GIST (pickup_coordinates);
CREATE INDEX idx_rides_destination_gist ON rides USING GIST (destination_coordinates);

-- Performance indexes for common queries
CREATE INDEX idx_rides_passenger_status ON rides (passenger_id, status);
CREATE INDEX idx_rides_driver_status ON rides (driver_id, status);
CREATE INDEX idx_rides_created_at ON rides (created_at DESC);
CREATE INDEX idx_rides_status_created ON rides (status, created_at DESC);

-- Driver-specific indexes
CREATE INDEX idx_drivers_online_status ON drivers (is_online, status)
  WHERE is_online = true;
CREATE INDEX idx_drivers_association ON drivers (association_id);
CREATE INDEX idx_drivers_user_id ON drivers (user_id);

-- Payment and earnings indexes
CREATE INDEX idx_payments_ride_status ON payments (ride_id, payment_status);
CREATE INDEX idx_driver_earnings_driver_created ON driver_earnings (driver_id, created_at DESC);

-- Notification indexes
CREATE INDEX idx_notifications_user_unread ON notifications (user_id, is_read, sent_at DESC)
  WHERE is_read = false;

-- Driver location indexes (for partitioned table)
CREATE INDEX idx_driver_locations_driver_time ON driver_locations (driver_id, timestamp DESC);
CREATE INDEX idx_driver_locations_timestamp ON driver_locations (timestamp DESC);

-- Chat message indexes
CREATE INDEX idx_chat_messages_channel_time ON chat_messages (channel, timestamp DESC);
CREATE INDEX idx_chat_messages_driver ON chat_messages (driver_id, timestamp DESC);
```

### **2. Database Functions for Performance**
```sql
-- Migration 008: Performance Functions
-- File: supabase/migrations/20240101000008_performance_functions.sql

-- Optimized function to find nearby drivers
CREATE OR REPLACE FUNCTION find_nearby_drivers(
  pickup_point POINT,
  radius_km DECIMAL DEFAULT 5.0,
  service_category vehicle_category DEFAULT 'standard',
  max_results INTEGER DEFAULT 10
)
RETURNS TABLE (
  driver_id UUID,
  distance_km DECIMAL,
  driver_rating DECIMAL,
  vehicle_info JSONB,
  last_seen TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    (ST_Distance(d.last_location, pickup_point) / 1000)::DECIMAL as distance,
    d.rating,
    jsonb_build_object(
      'make', v.make,
      'model', v.model,
      'color', v.color,
      'plate', v.license_plate,
      'category', v.category
    ) as vehicle_info,
    d.last_location_update
  FROM drivers d
  JOIN vehicles v ON d.vehicle_id = v.id
  WHERE d.is_online = true
    AND d.status = 'approved'
    AND v.category = service_category
    AND v.is_active = true
    AND d.last_location_update > NOW() - INTERVAL '5 minutes'
    AND ST_DWithin(d.last_location, pickup_point, radius_km * 1000)
  ORDER BY distance, d.rating DESC
  LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Function to get ride statistics for driver
CREATE OR REPLACE FUNCTION get_driver_stats(
  driver_uuid UUID,
  start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
  stats JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_rides', COUNT(*),
    'completed_rides', COUNT(*) FILTER (WHERE status = 'completed'),
    'cancelled_rides', COUNT(*) FILTER (WHERE status = 'cancelled'),
    'total_earnings', COALESCE(SUM(de.net_earnings), 0),
    'average_rating', ROUND(AVG(passenger_rating), 2),
    'total_distance', ROUND(SUM(distance_km), 2),
    'completion_rate', ROUND(
      COUNT(*) FILTER (WHERE status = 'completed')::DECIMAL / 
      NULLIF(COUNT(*), 0) * 100, 2
    )
  ) INTO stats
  FROM rides r
  LEFT JOIN driver_earnings de ON r.id = de.ride_id
  WHERE r.driver_id = driver_uuid
    AND r.created_at::DATE BETWEEN start_date AND end_date;
  
  RETURN stats;
END;
$$ LANGUAGE plpgsql;
```

This comprehensive database implementation provides a robust, secure, and performant foundation for the Taxicab platform with proper RLS policies, optimized indexes, and scalable architecture.
