# 2. Web Portal Development

## 🌐 **Web Portal Overview**

This document provides comprehensive implementation guidelines for the UniversalWallet React.js web portal, designed for business users, agents, and administrators with advanced features, responsive design, and enterprise-level functionality.

## 🏗️ **React.js Architecture**

### **Project Structure**
```
src/
├── components/           # Reusable UI components
│   ├── common/          # Common components (Button, Input, etc.)
│   ├── layout/          # Layout components (Header, Sidebar, etc.)
│   ├── forms/           # Form components
│   ├── charts/          # Chart and visualization components
│   └── modals/          # Modal components
├── pages/               # Page components
│   ├── auth/            # Authentication pages
│   ├── dashboard/       # Dashboard pages
│   ├── business/        # Business management pages
│   ├── agent/           # Agent portal pages
│   ├── admin/           # Admin panel pages
│   └── reports/         # Reporting pages
├── hooks/               # Custom React hooks
├── services/            # API services
├── store/               # Redux store
├── utils/               # Utility functions
├── types/               # TypeScript definitions
├── constants/           # App constants
├── styles/              # Global styles and themes
└── assets/              # Static assets
```

### **App Entry Point**
```typescript
// App.tsx
import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import { store, persistor } from './store';
import { AppRoutes } from './routes/AppRoutes';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationProvider';
import { LoadingProvider } from './contexts/LoadingContext';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { GlobalLoading } from './components/common/GlobalLoading';

const theme = createTheme({
  palette: {
    primary: {
      main: '#007AFF',
      light: '#4DA3FF',
      dark: '#0056CC',
    },
    secondary: {
      main: '#34C759',
      light: '#5ED77A',
      dark: '#248A3D',
    },
    background: {
      default: '#f8f9fa',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 8,
  },
});

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <PersistGate loading={<GlobalLoading />} persistor={persistor}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Router>
                <AuthProvider>
                  <NotificationProvider>
                    <LoadingProvider>
                      <AppRoutes />
                    </LoadingProvider>
                  </NotificationProvider>
                </AuthProvider>
              </Router>
            </LocalizationProvider>
          </ThemeProvider>
        </PersistGate>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
```

---

## 🔐 **Authentication Implementation**

### **Login Page**
```typescript
// pages/auth/LoginPage.tsx
import React, { useState } from 'react';
import {
  Container,
  Paper,
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Link,
  Divider,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../store';
import { loginUser } from '../../store/slices/authSlice';
import { LoadingButton } from '../../components/common/LoadingButton';
import { PasswordField } from '../../components/common/PasswordField';

const loginSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
});

interface LoginFormValues {
  email: string;
  password: string;
}

export const LoginPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const [showPassword, setShowPassword] = useState(false);

  const from = (location.state as any)?.from?.pathname || '/dashboard';

  const formik = useFormik<LoginFormValues>({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: loginSchema,
    onSubmit: async (values) => {
      try {
        await dispatch(loginUser(values)).unwrap();
        navigate(from, { replace: true });
      } catch (error) {
        // Error is handled by Redux state
      }
    },
  });

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            width: '100%',
            borderRadius: 2,
          }}
        >
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography component="h1" variant="h4" gutterBottom>
              UniversalWallet
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Business Portal
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={formik.handleSubmit}>
            <TextField
              fullWidth
              id="email"
              name="email"
              label="Email Address"
              type="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
              margin="normal"
              autoComplete="email"
              autoFocus
            />

            <PasswordField
              fullWidth
              id="password"
              name="password"
              label="Password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              margin="normal"
              showPassword={showPassword}
              onTogglePassword={() => setShowPassword(!showPassword)}
            />

            <LoadingButton
              type="submit"
              fullWidth
              variant="contained"
              loading={isLoading}
              sx={{ mt: 3, mb: 2 }}
            >
              Sign In
            </LoadingButton>

            <Box sx={{ textAlign: 'center' }}>
              <Link href="/forgot-password" variant="body2">
                Forgot password?
              </Link>
            </Box>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                OR
              </Typography>
            </Divider>

            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate('/register')}
            >
              Create Business Account
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};
```

---

## 🏢 **Business Dashboard Implementation**

### **Business Dashboard Page**
```typescript
// pages/business/BusinessDashboardPage.tsx
import React, { useEffect, useState } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Receipt,
  People,
  MoreVert,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../store';
import { fetchBusinessAnalytics } from '../../store/slices/businessSlice';
import { DashboardLayout } from '../../components/layout/DashboardLayout';
import { StatCard } from '../../components/dashboard/StatCard';
import { TransactionChart } from '../../components/charts/TransactionChart';
import { RecentTransactionsTable } from '../../components/tables/RecentTransactionsTable';
import { QuickActions } from '../../components/business/QuickActions';

export const BusinessDashboardPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { analytics, isLoading } = useSelector((state: RootState) => state.business);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    dispatch(fetchBusinessAnalytics({
      period: 'month',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
    }));
  }, [dispatch]);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <DashboardLayout>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Business Dashboard
          </Typography>
          <IconButton onClick={handleMenuClick}>
            <MoreVert />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>Export Report</MenuItem>
            <MenuItem onClick={handleMenuClose}>Settings</MenuItem>
          </Menu>
        </Box>

        {/* Key Metrics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Revenue"
              value={analytics?.totalRevenue || 0}
              currency="ZWG"
              change={analytics?.revenueChange || 0}
              icon={<TrendingUp />}
              loading={isLoading}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Transactions"
              value={analytics?.transactionCount || 0}
              change={analytics?.transactionChange || 0}
              icon={<Receipt />}
              loading={isLoading}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Account Balance"
              value={analytics?.accountBalance || 0}
              currency="ZWG"
              icon={<AccountBalance />}
              loading={isLoading}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Active Customers"
              value={analytics?.activeCustomers || 0}
              change={analytics?.customerChange || 0}
              icon={<People />}
              loading={isLoading}
            />
          </Grid>
        </Grid>

        {/* Charts and Quick Actions */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, height: 400 }}>
              <Typography variant="h6" gutterBottom>
                Transaction Volume
              </Typography>
              <TransactionChart
                data={analytics?.chartData || []}
                loading={isLoading}
              />
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <QuickActions />
          </Grid>
        </Grid>

        {/* Recent Transactions */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Recent Transactions
          </Typography>
          <RecentTransactionsTable
            transactions={analytics?.recentTransactions || []}
            loading={isLoading}
          />
        </Paper>
      </Box>
    </DashboardLayout>
  );
};
```

### **Bulk Payment Management**
```typescript
// pages/business/BulkPaymentPage.tsx
import React, { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Alert,
} from '@mui/material';
import { Upload, Send, CheckCircle } from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../store';
import { createBulkPayment, validatePaymentFile } from '../../store/slices/bulkPaymentSlice';
import { DashboardLayout } from '../../components/layout/DashboardLayout';
import { FileUploadZone } from '../../components/common/FileUploadZone';
import { PaymentPreviewTable } from '../../components/business/PaymentPreviewTable';
import { BulkPaymentSummary } from '../../components/business/BulkPaymentSummary';

const steps = ['Upload File', 'Review Payments', 'Confirm & Send'];

export const BulkPaymentPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, validationResult, error } = useSelector(
    (state: RootState) => state.bulkPayment
  );
  
  const [activeStep, setActiveStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [paymentData, setPaymentData] = useState<any[]>([]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      
      try {
        const result = await dispatch(validatePaymentFile(file)).unwrap();
        setPaymentData(result.payments);
        setActiveStep(1);
      } catch (error) {
        // Error handled by Redux state
      }
    }
  }, [dispatch]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    },
    maxFiles: 1,
  });

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async () => {
    try {
      await dispatch(createBulkPayment({
        batchName: `Bulk Payment ${new Date().toLocaleDateString()}`,
        payments: paymentData,
        requiresApproval: true,
      })).unwrap();
      
      setActiveStep(2);
    } catch (error) {
      // Error handled by Redux state
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <FileUploadZone
              {...getRootProps()}
              isDragActive={isDragActive}
            >
              <input {...getInputProps()} />
              <Upload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Upload Payment File
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Drag and drop your CSV or Excel file here, or click to browse
              </Typography>
            </FileUploadZone>
            
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Payment Details
            </Typography>
            
            {validationResult && (
              <Alert 
                severity={validationResult.hasErrors ? 'warning' : 'success'} 
                sx={{ mb: 2 }}
              >
                {validationResult.hasErrors
                  ? `${validationResult.errorCount} errors found. Please review and correct.`
                  : `${paymentData.length} payments ready for processing.`
                }
              </Alert>
            )}

            <PaymentPreviewTable
              payments={paymentData}
              validationResult={validationResult}
            />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button onClick={handleBack}>
                Back
              </Button>
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={validationResult?.hasErrors}
              >
                Continue
              </Button>
            </Box>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Payment Summary
            </Typography>
            
            <BulkPaymentSummary payments={paymentData} />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button onClick={handleBack}>
                Back
              </Button>
              <Button
                variant="contained"
                onClick={handleSubmit}
                loading={isLoading}
                startIcon={<Send />}
              >
                Submit for Approval
              </Button>
            </Box>
          </Box>
        );

      case 3:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Bulk Payment Submitted
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Your bulk payment has been submitted for approval and will be processed shortly.
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Bulk Payments
        </Typography>

        <Paper sx={{ p: 3 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {renderStepContent(activeStep)}
        </Paper>
      </Box>
    </DashboardLayout>
  );
};
```

**This comprehensive web portal implementation provides enterprise-level functionality for business users, agents, and administrators with advanced features, responsive design, and professional user experience.** 🌐
