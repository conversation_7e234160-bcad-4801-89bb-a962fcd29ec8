# 6. Real-Time Architecture

## ⚡ **Real-Time Overview**

The Taxicab platform leverages Supabase Realtime for live data synchronization, providing instant updates for ride tracking, driver locations, and user communications. The real-time architecture ensures low-latency updates while maintaining data consistency and optimal performance.

### **Real-Time Principles**
- **Event-Driven Architecture**: All real-time features built on database change events
- **Selective Subscriptions**: Users only receive relevant updates to minimize bandwidth
- **Conflict Resolution**: Proper handling of concurrent updates and data conflicts
- **Offline Resilience**: Graceful handling of connection drops and reconnections
- **Scalable Broadcasting**: Efficient message distribution to thousands of concurrent users

## 🔄 **Supabase Realtime Implementation**

### **Real-Time Client Configuration**
```typescript
// Supabase Realtime client setup
import { createClient, RealtimeChannel } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!,
  {
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting for real-time events
      },
    },
  }
);

// Real-time subscription manager
class RealtimeManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private subscriptions: Map<string, any> = new Map();
  
  // Subscribe to table changes with RLS filtering
  async subscribeToTable<T>(
    tableName: string,
    filter?: string,
    callback?: (payload: any) => void
  ): Promise<RealtimeChannel> {
    const channelName = `${tableName}${filter ? `:${filter}` : ''}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName,
          filter: filter
        },
        (payload) => {
          this.handleRealtimeEvent(tableName, payload);
          callback?.(payload);
        }
      )
      .subscribe();
    
    this.channels.set(channelName, channel);
    return channel;
  }
  
  // Unsubscribe from real-time updates
  async unsubscribe(channelName: string): Promise<void> {
    const channel = this.channels.get(channelName);
    if (channel) {
      await supabase.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }
  
  private handleRealtimeEvent(tableName: string, payload: any): void {
    // Log real-time events for debugging
    console.log(`Real-time event on ${tableName}:`, payload);
    
    // Handle specific table events
    switch (tableName) {
      case 'rides':
        this.handleRideUpdate(payload);
        break;
      case 'driver_locations':
        this.handleLocationUpdate(payload);
        break;
      case 'notifications':
        this.handleNotificationUpdate(payload);
        break;
    }
  }
  
  private handleRideUpdate(payload: any): void {
    // Emit ride status changes to relevant users
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    if (eventType === 'UPDATE' && newRecord.status !== oldRecord?.status) {
      // Notify passenger and driver of status change
      this.notifyRideStatusChange(newRecord);
    }
  }
  
  private handleLocationUpdate(payload: any): void {
    // Broadcast driver location to passengers in active rides
    const { new: locationUpdate } = payload;
    this.broadcastDriverLocation(locationUpdate);
  }
}
```

### **Live Ride Tracking**
```typescript
// Real-time ride tracking implementation
class RideTrackingService {
  private realtimeManager: RealtimeManager;
  private activeRideSubscriptions: Map<string, RealtimeChannel> = new Map();
  
  constructor(realtimeManager: RealtimeManager) {
    this.realtimeManager = realtimeManager;
  }
  
  // Start tracking a specific ride
  async startRideTracking(rideId: string, userRole: 'passenger' | 'driver'): Promise<void> {
    // Subscribe to ride status updates
    const rideChannel = await this.realtimeManager.subscribeToTable(
      'rides',
      `id=eq.${rideId}`,
      (payload) => this.handleRideUpdate(payload, userRole)
    );
    
    // Subscribe to driver location updates for this ride
    const locationChannel = await this.subscribeToDriverLocation(rideId);
    
    this.activeRideSubscriptions.set(`ride:${rideId}`, rideChannel);
    this.activeRideSubscriptions.set(`location:${rideId}`, locationChannel);
  }
  
  // Stop tracking when ride is completed
  async stopRideTracking(rideId: string): Promise<void> {
    await this.realtimeManager.unsubscribe(`ride:${rideId}`);
    await this.realtimeManager.unsubscribe(`location:${rideId}`);
    
    this.activeRideSubscriptions.delete(`ride:${rideId}`);
    this.activeRideSubscriptions.delete(`location:${rideId}`);
  }
  
  private async subscribeToDriverLocation(rideId: string): Promise<RealtimeChannel> {
    // Get driver ID for this ride
    const { data: ride } = await supabase
      .from('rides')
      .select('driver_id')
      .eq('id', rideId)
      .single();
    
    if (!ride?.driver_id) {
      throw new Error('No driver assigned to this ride');
    }
    
    // Subscribe to driver location updates
    return await this.realtimeManager.subscribeToTable(
      'driver_locations',
      `driver_id=eq.${ride.driver_id}`,
      (payload) => this.handleLocationUpdate(payload, rideId)
    );
  }
  
  private handleRideUpdate(payload: any, userRole: string): void {
    const { eventType, new: rideData } = payload;
    
    // Emit ride status updates to UI
    window.dispatchEvent(new CustomEvent('rideStatusUpdate', {
      detail: { ride: rideData, userRole }
    }));
    
    // Send push notifications for important status changes
    if (rideData.status === 'driver_arrived') {
      this.sendPushNotification(rideData.passenger_id, 'Your driver has arrived!');
    } else if (rideData.status === 'completed') {
      this.sendPushNotification(rideData.passenger_id, 'Trip completed. Please rate your driver.');
    }
  }
  
  private handleLocationUpdate(payload: any, rideId: string): void {
    const { new: locationData } = payload;
    
    // Emit location updates to UI
    window.dispatchEvent(new CustomEvent('driverLocationUpdate', {
      detail: { 
        rideId, 
        location: this.parsePostGISPoint(locationData.coordinates),
        heading: locationData.heading,
        timestamp: locationData.timestamp
      }
    }));
  }
  
  private parsePostGISPoint(pointString: string): { latitude: number; longitude: number } {
    // Parse PostGIS POINT format: "POINT(longitude latitude)"
    const matches = pointString.match(/POINT\(([^)]+)\)/);
    if (!matches) throw new Error('Invalid point format');
    
    const [longitude, latitude] = matches[1].split(' ').map(Number);
    return { latitude, longitude };
  }
}
```

### **Driver Location Broadcasting**
```typescript
// Real-time driver location service
class DriverLocationService {
  private locationUpdateInterval?: NodeJS.Timeout;
  private isTracking: boolean = false;
  
  // Start broadcasting driver location
  async startLocationTracking(driverId: string): Promise<void> {
    if (this.isTracking) return;
    
    this.isTracking = true;
    
    // Update location every 5 seconds when online
    this.locationUpdateInterval = setInterval(async () => {
      try {
        const position = await this.getCurrentPosition();
        await this.updateDriverLocation(driverId, position);
      } catch (error) {
        console.error('Failed to update driver location:', error);
      }
    }, 5000);
  }
  
  // Stop broadcasting driver location
  stopLocationTracking(): void {
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = undefined;
    }
    this.isTracking = false;
  }
  
  private async getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 5000
        }
      );
    });
  }
  
  private async updateDriverLocation(
    driverId: string, 
    position: GeolocationPosition
  ): Promise<void> {
    const { coords } = position;
    
    // Insert location update (triggers real-time broadcast)
    const { error } = await supabase
      .from('driver_locations')
      .insert({
        driver_id: driverId,
        coordinates: `POINT(${coords.longitude} ${coords.latitude})`,
        heading: coords.heading || null,
        speed: coords.speed || null,
        accuracy: coords.accuracy,
        timestamp: new Date().toISOString()
      });
    
    if (error) {
      throw new Error(`Failed to update location: ${error.message}`);
    }
    
    // Also update driver's last known location
    await supabase
      .from('drivers')
      .update({
        last_location: `POINT(${coords.longitude} ${coords.latitude})`,
        last_location_update: new Date().toISOString()
      })
      .eq('id', driverId);
  }
}
```

## 💬 **Real-Time Communication Features**

### **Driver Community Chat**
```typescript
// Real-time chat implementation for driver community
class DriverChatService {
  private chatChannels: Map<string, RealtimeChannel> = new Map();
  
  // Join a city-based chat channel
  async joinChatChannel(city: string, driverId: string): Promise<void> {
    const channelName = `chat:${city.toLowerCase()}`;
    
    if (this.chatChannels.has(channelName)) {
      return; // Already joined
    }
    
    const channel = supabase
      .channel(channelName)
      .on('broadcast', { event: 'message' }, (payload) => {
        this.handleChatMessage(payload, city);
      })
      .on('presence', { event: 'sync' }, () => {
        this.updateOnlineDrivers(city);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        this.handleDriverJoin(key, newPresences, city);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        this.handleDriverLeave(key, leftPresences, city);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          // Track driver presence in the channel
          await channel.track({
            driver_id: driverId,
            online_at: new Date().toISOString()
          });
        }
      });
    
    this.chatChannels.set(channelName, channel);
  }
  
  // Send message to chat channel
  async sendMessage(city: string, message: ChatMessage): Promise<void> {
    const channelName = `chat:${city.toLowerCase()}`;
    const channel = this.chatChannels.get(channelName);
    
    if (!channel) {
      throw new Error(`Not connected to ${city} chat channel`);
    }
    
    // Broadcast message to all drivers in the channel
    await channel.send({
      type: 'broadcast',
      event: 'message',
      payload: {
        id: generateUUID(),
        driver_id: message.driver_id,
        driver_name: message.driver_name,
        message: message.text,
        timestamp: new Date().toISOString(),
        city: city
      }
    });
    
    // Also store in database for message history
    await this.storeChatMessage(city, message);
  }
  
  // Leave chat channel
  async leaveChatChannel(city: string): Promise<void> {
    const channelName = `chat:${city.toLowerCase()}`;
    const channel = this.chatChannels.get(channelName);
    
    if (channel) {
      await supabase.removeChannel(channel);
      this.chatChannels.delete(channelName);
    }
  }
  
  private handleChatMessage(payload: any, city: string): void {
    // Emit chat message to UI
    window.dispatchEvent(new CustomEvent('chatMessage', {
      detail: { ...payload, city }
    }));
  }
  
  private async storeChatMessage(city: string, message: ChatMessage): Promise<void> {
    await supabase
      .from('chat_messages')
      .insert({
        channel: city.toLowerCase(),
        driver_id: message.driver_id,
        message: message.text,
        timestamp: new Date().toISOString()
      });
  }
  
  private updateOnlineDrivers(city: string): void {
    const channelName = `chat:${city.toLowerCase()}`;
    const channel = this.chatChannels.get(channelName);
    
    if (channel) {
      const presenceState = channel.presenceState();
      const onlineDrivers = Object.values(presenceState).flat();
      
      window.dispatchEvent(new CustomEvent('onlineDriversUpdate', {
        detail: { city, drivers: onlineDrivers }
      }));
    }
  }
}
```

### **Real-Time Notifications**
```typescript
// Real-time notification system
class NotificationService {
  private notificationChannel?: RealtimeChannel;
  
  // Subscribe to user-specific notifications
  async subscribeToNotifications(userId: string): Promise<void> {
    this.notificationChannel = supabase
      .channel(`notifications:${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => this.handleNewNotification(payload.new)
      )
      .subscribe();
  }
  
  // Send real-time notification
  async sendNotification(notification: NotificationData): Promise<void> {
    // Insert notification (triggers real-time broadcast)
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: notification.user_id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data || {},
        sent_at: new Date().toISOString()
      });
    
    if (error) {
      throw new Error(`Failed to send notification: ${error.message}`);
    }
    
    // Also send push notification if user is offline
    await this.sendPushNotification(notification);
  }
  
  private handleNewNotification(notification: any): void {
    // Show in-app notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: notification
    }));
    
    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon-192x192.png',
        tag: notification.id
      });
    }
  }
  
  private async sendPushNotification(notification: NotificationData): Promise<void> {
    // Send via Firebase Cloud Messaging or similar service
    await supabase.functions.invoke('send-push-notification', {
      body: notification
    });
  }
  
  // Unsubscribe from notifications
  async unsubscribeFromNotifications(): Promise<void> {
    if (this.notificationChannel) {
      await supabase.removeChannel(this.notificationChannel);
      this.notificationChannel = undefined;
    }
  }
}
```

## 🔧 **Real-Time Performance Optimization**

### **Connection Management**
```typescript
// Optimized real-time connection management
class RealtimeConnectionManager {
  private connectionState: 'connected' | 'disconnected' | 'reconnecting' = 'disconnected';
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000; // Start with 1 second
  
  // Monitor connection status
  monitorConnection(): void {
    supabase.realtime.onOpen(() => {
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      
      window.dispatchEvent(new CustomEvent('realtimeConnected'));
    });
    
    supabase.realtime.onClose(() => {
      this.connectionState = 'disconnected';
      this.handleDisconnection();
    });
    
    supabase.realtime.onError((error) => {
      console.error('Realtime connection error:', error);
      this.handleConnectionError(error);
    });
  }
  
  private async handleDisconnection(): Promise<void> {
    window.dispatchEvent(new CustomEvent('realtimeDisconnected'));
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.connectionState = 'reconnecting';
      this.reconnectAttempts++;
      
      // Exponential backoff for reconnection
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(async () => {
        try {
          await this.reconnect();
        } catch (error) {
          console.error('Reconnection failed:', error);
          await this.handleDisconnection();
        }
      }, delay);
    } else {
      // Max attempts reached, show error to user
      window.dispatchEvent(new CustomEvent('realtimeConnectionFailed'));
    }
  }
  
  private async reconnect(): Promise<void> {
    // Reconnect to Supabase realtime
    await supabase.realtime.connect();
  }
  
  private handleConnectionError(error: any): void {
    // Log error for monitoring
    console.error('Real-time connection error:', error);
    
    // Emit error event for UI handling
    window.dispatchEvent(new CustomEvent('realtimeError', {
      detail: error
    }));
  }
  
  // Get current connection status
  getConnectionState(): string {
    return this.connectionState;
  }
}
```

This comprehensive real-time architecture ensures that the Taxicab platform provides instant, reliable updates for all critical features while maintaining optimal performance and user experience.
