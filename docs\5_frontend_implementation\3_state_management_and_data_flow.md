# 3. State Management and Data Flow

## 🔄 **State Management Overview**

The Taxicab platform uses Zustand for global state management combined with React Query for server state management. This approach provides optimal performance, type safety, and developer experience across all frontend applications.

### **State Management Principles**
- **Separation of Concerns**: Clear distinction between client state and server state
- **Type Safety**: Full TypeScript integration with strict typing
- **Performance**: Optimized re-renders and efficient data fetching
- **Developer Experience**: Simple APIs with excellent debugging capabilities
- **Persistence**: Automatic state persistence where appropriate

## 🏪 **Zustand Store Architecture**

### **1. Authentication Store**
```typescript
// packages/shared-stores/src/authStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

interface AuthActions {
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  signOut: () => Promise<void>;
  clearError: () => void;
  initialize: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      user: null,
      session: null,
      isLoading: true,
      isAuthenticated: false,
      error: null,

      // Actions
      setUser: (user) =>
        set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
        }),

      setSession: (session) =>
        set((state) => {
          state.session = session;
          state.user = session?.user || null;
          state.isAuthenticated = !!session?.user;
        }),

      setLoading: (loading) =>
        set((state) => {
          state.isLoading = loading;
        }),

      setError: (error) =>
        set((state) => {
          state.error = error;
        }),

      clearError: () =>
        set((state) => {
          state.error = null;
        }),

      signOut: async () => {
        try {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          const { error } = await supabase.auth.signOut();
          if (error) throw error;

          set((state) => {
            state.user = null;
            state.session = null;
            state.isAuthenticated = false;
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.error = (error as Error).message;
            state.isLoading = false;
          });
          throw error;
        }
      },

      initialize: async () => {
        try {
          set((state) => {
            state.isLoading = true;
          });

          // Get initial session
          const { data: { session }, error } = await supabase.auth.getSession();
          if (error) throw error;

          set((state) => {
            state.session = session;
            state.user = session?.user || null;
            state.isAuthenticated = !!session?.user;
            state.isLoading = false;
          });

          // Listen for auth changes
          supabase.auth.onAuthStateChange((event, session) => {
            set((state) => {
              state.session = session;
              state.user = session?.user || null;
              state.isAuthenticated = !!session?.user;
              state.isLoading = false;
            });
          });
        } catch (error) {
          set((state) => {
            state.error = (error as Error).message;
            state.isLoading = false;
          });
        }
      },
    })),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage), // Use AsyncStorage for React Native
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors for optimized re-renders
export const useAuth = () => useAuthStore((state) => ({
  user: state.user,
  session: state.session,
  isLoading: state.isLoading,
  isAuthenticated: state.isAuthenticated,
  error: state.error,
}));

export const useAuthActions = () => useAuthStore((state) => ({
  setUser: state.setUser,
  setSession: state.setSession,
  setLoading: state.setLoading,
  setError: state.setError,
  signOut: state.signOut,
  clearError: state.clearError,
  initialize: state.initialize,
}));
```

### **2. Driver Status Store (Driver App)**
```typescript
// apps/driver-app/src/stores/driverStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Driver, DriverLocation } from '@/types/driver';

interface DriverState {
  driver: Driver | null;
  isOnline: boolean;
  currentLocation: DriverLocation | null;
  activeRide: string | null;
  earnings: {
    today: number;
    week: number;
    month: number;
  };
  isLocationTracking: boolean;
  error: string | null;
}

interface DriverActions {
  setDriver: (driver: Driver | null) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  setCurrentLocation: (location: DriverLocation) => void;
  setActiveRide: (rideId: string | null) => void;
  updateEarnings: (earnings: Partial<DriverState['earnings']>) => void;
  setLocationTracking: (tracking: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type DriverStore = DriverState & DriverActions;

export const useDriverStore = create<DriverStore>()(
  persist(
    immer((set) => ({
      // Initial state
      driver: null,
      isOnline: false,
      currentLocation: null,
      activeRide: null,
      earnings: {
        today: 0,
        week: 0,
        month: 0,
      },
      isLocationTracking: false,
      error: null,

      // Actions
      setDriver: (driver) =>
        set((state) => {
          state.driver = driver;
        }),

      setOnlineStatus: (isOnline) =>
        set((state) => {
          state.isOnline = isOnline;
          if (!isOnline) {
            state.isLocationTracking = false;
          }
        }),

      setCurrentLocation: (location) =>
        set((state) => {
          state.currentLocation = location;
        }),

      setActiveRide: (rideId) =>
        set((state) => {
          state.activeRide = rideId;
        }),

      updateEarnings: (earnings) =>
        set((state) => {
          state.earnings = { ...state.earnings, ...earnings };
        }),

      setLocationTracking: (tracking) =>
        set((state) => {
          state.isLocationTracking = tracking;
        }),

      setError: (error) =>
        set((state) => {
          state.error = error;
        }),

      clearError: () =>
        set((state) => {
          state.error = null;
        }),
    })),
    {
      name: 'driver-storage',
      partialize: (state) => ({
        driver: state.driver,
        isOnline: state.isOnline,
        earnings: state.earnings,
      }),
    }
  )
);

// Selectors
export const useDriverStatus = () => useDriverStore((state) => ({
  driver: state.driver,
  isOnline: state.isOnline,
  currentLocation: state.currentLocation,
  activeRide: state.activeRide,
  isLocationTracking: state.isLocationTracking,
}));

export const useDriverEarnings = () => useDriverStore((state) => state.earnings);

export const useDriverActions = () => useDriverStore((state) => ({
  setDriver: state.setDriver,
  setOnlineStatus: state.setOnlineStatus,
  setCurrentLocation: state.setCurrentLocation,
  setActiveRide: state.setActiveRide,
  updateEarnings: state.updateEarnings,
  setLocationTracking: state.setLocationTracking,
  setError: state.setError,
  clearError: state.clearError,
}));
```

### **3. Ride Booking Store (Passenger App)**
```typescript
// apps/passenger-app/src/stores/rideStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { Ride, Location, ServiceType, FareEstimate } from '@/types/ride';

interface RideState {
  currentRide: Ride | null;
  pickupLocation: Location | null;
  destinationLocation: Location | null;
  serviceType: ServiceType;
  fareEstimate: FareEstimate | null;
  isBooking: boolean;
  rideHistory: Ride[];
  error: string | null;
}

interface RideActions {
  setCurrentRide: (ride: Ride | null) => void;
  setPickupLocation: (location: Location | null) => void;
  setDestinationLocation: (location: Location | null) => void;
  setServiceType: (serviceType: ServiceType) => void;
  setFareEstimate: (estimate: FareEstimate | null) => void;
  setBooking: (booking: boolean) => void;
  addToHistory: (ride: Ride) => void;
  setRideHistory: (rides: Ride[]) => void;
  setError: (error: string | null) => void;
  clearBookingData: () => void;
  clearError: () => void;
}

type RideStore = RideState & RideActions;

export const useRideStore = create<RideStore>()(
  immer((set) => ({
    // Initial state
    currentRide: null,
    pickupLocation: null,
    destinationLocation: null,
    serviceType: 'movers_ride',
    fareEstimate: null,
    isBooking: false,
    rideHistory: [],
    error: null,

    // Actions
    setCurrentRide: (ride) =>
      set((state) => {
        state.currentRide = ride;
      }),

    setPickupLocation: (location) =>
      set((state) => {
        state.pickupLocation = location;
        // Clear fare estimate when location changes
        if (state.fareEstimate) {
          state.fareEstimate = null;
        }
      }),

    setDestinationLocation: (location) =>
      set((state) => {
        state.destinationLocation = location;
        // Clear fare estimate when location changes
        if (state.fareEstimate) {
          state.fareEstimate = null;
        }
      }),

    setServiceType: (serviceType) =>
      set((state) => {
        state.serviceType = serviceType;
        // Clear fare estimate when service type changes
        if (state.fareEstimate) {
          state.fareEstimate = null;
        }
      }),

    setFareEstimate: (estimate) =>
      set((state) => {
        state.fareEstimate = estimate;
      }),

    setBooking: (booking) =>
      set((state) => {
        state.isBooking = booking;
      }),

    addToHistory: (ride) =>
      set((state) => {
        state.rideHistory.unshift(ride);
        // Keep only last 50 rides in memory
        if (state.rideHistory.length > 50) {
          state.rideHistory = state.rideHistory.slice(0, 50);
        }
      }),

    setRideHistory: (rides) =>
      set((state) => {
        state.rideHistory = rides;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    clearBookingData: () =>
      set((state) => {
        state.pickupLocation = null;
        state.destinationLocation = null;
        state.fareEstimate = null;
        state.isBooking = false;
        state.error = null;
      }),

    clearError: () =>
      set((state) => {
        state.error = null;
      }),
  }))
);

// Selectors
export const useRideBooking = () => useRideStore((state) => ({
  pickupLocation: state.pickupLocation,
  destinationLocation: state.destinationLocation,
  serviceType: state.serviceType,
  fareEstimate: state.fareEstimate,
  isBooking: state.isBooking,
  error: state.error,
}));

export const useCurrentRide = () => useRideStore((state) => state.currentRide);

export const useRideHistory = () => useRideStore((state) => state.rideHistory);

export const useRideActions = () => useRideStore((state) => ({
  setCurrentRide: state.setCurrentRide,
  setPickupLocation: state.setPickupLocation,
  setDestinationLocation: state.setDestinationLocation,
  setServiceType: state.setServiceType,
  setFareEstimate: state.setFareEstimate,
  setBooking: state.setBooking,
  addToHistory: state.addToHistory,
  setRideHistory: state.setRideHistory,
  setError: state.setError,
  clearBookingData: state.clearBookingData,
  clearError: state.clearError,
}));
```

## 🔄 **React Query Integration**

### **1. Query Client Configuration**
```typescript
// packages/shared-config/src/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});
```

### **2. Custom Query Hooks**
```typescript
// packages/api-client/src/hooks/useRides.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { RideService } from '@/services/rideService';
import { useRideActions } from '@/stores/rideStore';
import type { CreateRideRequest, Ride } from '@/types/ride';

// Query keys factory
export const rideKeys = {
  all: ['rides'] as const,
  lists: () => [...rideKeys.all, 'list'] as const,
  list: (filters: string) => [...rideKeys.lists(), { filters }] as const,
  details: () => [...rideKeys.all, 'detail'] as const,
  detail: (id: string) => [...rideKeys.details(), id] as const,
  history: (userId: string) => [...rideKeys.all, 'history', userId] as const,
};

// Get ride details
export const useRide = (rideId: string) => {
  return useQuery({
    queryKey: rideKeys.detail(rideId),
    queryFn: () => RideService.getRideDetails(rideId),
    enabled: !!rideId,
  });
};

// Get ride history
export const useRideHistory = (userId: string, page: number = 1) => {
  const { setRideHistory } = useRideActions();

  return useQuery({
    queryKey: rideKeys.history(userId),
    queryFn: () => RideService.getRideHistory(userId, page),
    enabled: !!userId,
    onSuccess: (data) => {
      setRideHistory(data.data);
    },
  });
};

// Create ride mutation
export const useCreateRide = () => {
  const queryClient = useQueryClient();
  const { setCurrentRide, addToHistory, setBooking, setError } = useRideActions();

  return useMutation({
    mutationFn: (request: CreateRideRequest) => RideService.createRide(request),
    onMutate: () => {
      setBooking(true);
      setError(null);
    },
    onSuccess: (ride: Ride) => {
      setCurrentRide(ride);
      addToHistory(ride);
      setBooking(false);
      
      // Invalidate and refetch ride history
      queryClient.invalidateQueries({ queryKey: rideKeys.history(ride.passenger_id) });
    },
    onError: (error: Error) => {
      setError(error.message);
      setBooking(false);
    },
  });
};

// Update ride status mutation
export const useUpdateRideStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      rideId, 
      status, 
      updates 
    }: { 
      rideId: string; 
      status: string; 
      updates?: any; 
    }) => RideService.updateRideStatus(rideId, status, updates),
    onSuccess: (updatedRide, variables) => {
      // Update the ride in cache
      queryClient.setQueryData(
        rideKeys.detail(variables.rideId),
        updatedRide
      );
      
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: rideKeys.history(updatedRide.passenger_id) 
      });
    },
  });
};
```

### **3. Real-Time Data Synchronization**
```typescript
// packages/api-client/src/hooks/useRealtimeRide.ts
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useRideActions } from '@/stores/rideStore';
import { rideKeys } from './useRides';

export const useRealtimeRide = (rideId: string) => {
  const queryClient = useQueryClient();
  const { setCurrentRide } = useRideActions();

  useEffect(() => {
    if (!rideId) return;

    const channel = supabase
      .channel(`ride:${rideId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'rides',
          filter: `id=eq.${rideId}`,
        },
        (payload) => {
          const updatedRide = payload.new as Ride;
          
          // Update React Query cache
          queryClient.setQueryData(rideKeys.detail(rideId), updatedRide);
          
          // Update Zustand store
          setCurrentRide(updatedRide);
          
          // Trigger any dependent queries to refetch
          queryClient.invalidateQueries({ 
            queryKey: rideKeys.lists(),
            exact: false 
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [rideId, queryClient, setCurrentRide]);
};
```

### **4. Optimistic Updates**
```typescript
// packages/api-client/src/hooks/useOptimisticRideUpdate.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { RideService } from '@/services/rideService';
import { rideKeys } from './useRides';
import type { Ride } from '@/types/ride';

export const useOptimisticRideUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      rideId, 
      status, 
      updates 
    }: { 
      rideId: string; 
      status: string; 
      updates?: Partial<Ride>; 
    }) => RideService.updateRideStatus(rideId, status, updates),
    
    // Optimistic update
    onMutate: async ({ rideId, status, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: rideKeys.detail(rideId) });

      // Snapshot the previous value
      const previousRide = queryClient.getQueryData<Ride>(rideKeys.detail(rideId));

      // Optimistically update to the new value
      if (previousRide) {
        const optimisticRide: Ride = {
          ...previousRide,
          status: status as any,
          updated_at: new Date().toISOString(),
          ...updates,
        };

        queryClient.setQueryData(rideKeys.detail(rideId), optimisticRide);
      }

      // Return a context object with the snapshotted value
      return { previousRide };
    },

    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousRide) {
        queryClient.setQueryData(
          rideKeys.detail(variables.rideId),
          context.previousRide
        );
      }
    },

    // Always refetch after error or success
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: rideKeys.detail(variables.rideId) 
      });
    },
  });
};
```

### **5. Background Sync**
```typescript
// packages/api-client/src/hooks/useBackgroundSync.ts
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { AppState } from 'react-native';
import { useNetInfo } from '@react-native-community/netinfo';

export const useBackgroundSync = () => {
  const queryClient = useQueryClient();
  const netInfo = useNetInfo();

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active' && netInfo.isConnected) {
        // App came to foreground and we have internet
        // Refetch important queries
        queryClient.invalidateQueries({ 
          predicate: (query) => {
            // Only refetch queries that are considered "important"
            const queryKey = query.queryKey[0] as string;
            return ['rides', 'driver', 'earnings'].includes(queryKey);
          }
        });
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [queryClient, netInfo.isConnected]);

  // Sync when network comes back online
  useEffect(() => {
    if (netInfo.isConnected && netInfo.type !== 'unknown') {
      queryClient.resumePausedMutations();
    }
  }, [netInfo.isConnected, netInfo.type, queryClient]);
};
```

This comprehensive state management implementation provides efficient, type-safe, and performant data flow across all Taxicab platform applications with proper offline handling, optimistic updates, and real-time synchronization.
