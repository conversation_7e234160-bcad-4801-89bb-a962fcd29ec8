# 5. Performance Optimization

## ⚡ **Performance Overview**

The Taxicab platform implements comprehensive performance optimization strategies across all frontend applications to ensure fast loading times, smooth interactions, and efficient resource usage. This document covers optimization techniques for web and mobile applications.

### **Performance Principles**
- **Core Web Vitals**: Optimize for LCP, FID, and CLS metrics
- **Mobile Performance**: Prioritize mobile-first optimization
- **Bundle Optimization**: Minimize JavaScript bundle sizes
- **Caching Strategy**: Implement effective caching at multiple levels
- **Real-Time Efficiency**: Optimize real-time data synchronization

## 📦 **Bundle Optimization**

### **1. Code Splitting and Lazy Loading**
```typescript
// apps/admin-dashboard/src/App.tsx
import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorBoundary } from '@/components/ErrorBoundary';

// Lazy load route components
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const Drivers = React.lazy(() => import('@/pages/Drivers'));
const Rides = React.lazy(() => import('@/pages/Rides'));
const Users = React.lazy(() => import('@/pages/Users'));
const Analytics = React.lazy(() => import('@/pages/Analytics'));
const Settings = React.lazy(() => import('@/pages/Settings'));

// Preload critical routes
const preloadRoutes = () => {
  import('@/pages/Dashboard');
  import('@/pages/Drivers');
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

export const App: React.FC = () => {
  React.useEffect(() => {
    // Preload critical routes after initial render
    setTimeout(preloadRoutes, 1000);
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <Suspense fallback={<LoadingSpinner size="large" />}>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/drivers" element={<Drivers />} />
              <Route path="/rides" element={<Rides />} />
              <Route path="/users" element={<Users />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};
```

### **2. Dynamic Imports and Module Federation**
```typescript
// packages/shared-utils/src/dynamicImport.ts
export const dynamicImport = <T = any>(
  importFn: () => Promise<{ default: T }>,
  fallback?: T
): Promise<T> => {
  return importFn()
    .then(module => module.default)
    .catch(error => {
      console.error('Dynamic import failed:', error);
      if (fallback) {
        return fallback;
      }
      throw error;
    });
};

// Usage example
export const loadChartLibrary = () => 
  dynamicImport(() => import('recharts'));

export const loadMapLibrary = () => 
  dynamicImport(() => import('react-native-maps'));

// Conditional loading based on feature flags
export const loadFeature = async (featureName: string) => {
  const features = {
    analytics: () => import('@/features/analytics'),
    chat: () => import('@/features/chat'),
    payments: () => import('@/features/payments'),
  };

  const loader = features[featureName as keyof typeof features];
  if (!loader) {
    throw new Error(`Feature ${featureName} not found`);
  }

  return dynamicImport(loader);
};
```

### **3. Webpack/Vite Optimization**
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { splitVendorChunkPlugin } from 'vite';

export default defineConfig({
  plugins: [
    react(),
    splitVendorChunkPlugin(),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'query-vendor': ['@tanstack/react-query'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          
          // Feature chunks
          'maps': ['react-native-maps', 'google-maps'],
          'charts': ['recharts', 'd3'],
          'forms': ['react-hook-form', 'zod'],
        },
      },
    },
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    sourcemap: false, // Disable in production
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@supabase/supabase-js',
      'zustand',
    ],
  },
});
```

## 🚀 **React Performance Optimization**

### **1. Memoization and Optimization Hooks**
```typescript
// packages/shared-hooks/src/useOptimizedCallback.ts
import { useCallback, useMemo, useRef } from 'react';

// Optimized callback hook that prevents unnecessary re-renders
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  return useCallback(
    ((...args) => callbackRef.current(...args)) as T,
    deps
  );
};

// Memoized component wrapper
export const withMemo = <P extends object>(
  Component: React.ComponentType<P>,
  propsAreEqual?: (prevProps: P, nextProps: P) => boolean
) => {
  return React.memo(Component, propsAreEqual);
};

// Optimized list rendering hook
export const useVirtualizedList = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  return useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const bufferSize = Math.min(5, Math.floor(visibleCount / 2));
    
    return {
      visibleCount: visibleCount + bufferSize * 2,
      startIndex: 0,
      endIndex: visibleCount + bufferSize * 2,
    };
  }, [items.length, itemHeight, containerHeight]);
};
```

### **2. Component Optimization Patterns**
```typescript
// packages/design-system/src/components/OptimizedTable/OptimizedTable.tsx
import React, { useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';

interface OptimizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    header: string;
    render?: (value: T[keyof T], item: T) => React.ReactNode;
  }>;
  height: number;
  rowHeight: number;
  onRowClick?: (item: T) => void;
}

// Memoized row component to prevent unnecessary re-renders
const TableRow = React.memo<{
  index: number;
  style: React.CSSProperties;
  data: {
    items: any[];
    columns: any[];
    onRowClick?: (item: any) => void;
  };
}>(({ index, style, data }) => {
  const { items, columns, onRowClick } = data;
  const item = items[index];

  const handleClick = useCallback(() => {
    onRowClick?.(item);
  }, [item, onRowClick]);

  return (
    <div
      style={style}
      className="flex items-center border-b hover:bg-muted/50 cursor-pointer"
      onClick={handleClick}
    >
      {columns.map((column) => (
        <div key={String(column.key)} className="flex-1 px-4 py-2">
          {column.render 
            ? column.render(item[column.key], item)
            : String(item[column.key])
          }
        </div>
      ))}
    </div>
  );
});

export function OptimizedTable<T>({
  data,
  columns,
  height,
  rowHeight,
  onRowClick,
}: OptimizedTableProps<T>) {
  // Memoize the item data to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    items: data,
    columns,
    onRowClick,
  }), [data, columns, onRowClick]);

  return (
    <div className="border rounded-md">
      {/* Header */}
      <div className="flex bg-muted font-medium">
        {columns.map((column) => (
          <div key={String(column.key)} className="flex-1 px-4 py-3">
            {column.header}
          </div>
        ))}
      </div>
      
      {/* Virtualized rows */}
      <List
        height={height}
        itemCount={data.length}
        itemSize={rowHeight}
        itemData={itemData}
      >
        {TableRow}
      </List>
    </div>
  );
}
```

### **3. State Management Optimization**
```typescript
// packages/shared-stores/src/optimizedStore.ts
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';

// Optimized store with selective subscriptions
export const useOptimizedStore = create(
  subscribeWithSelector<{
    rides: Ride[];
    drivers: Driver[];
    filters: FilterState;
    updateRides: (rides: Ride[]) => void;
    updateDrivers: (drivers: Driver[]) => void;
    setFilters: (filters: Partial<FilterState>) => void;
  }>((set, get) => ({
    rides: [],
    drivers: [],
    filters: { status: 'all', dateRange: 'today' },
    
    updateRides: (rides) => set({ rides }),
    updateDrivers: (drivers) => set({ drivers }),
    setFilters: (newFilters) => 
      set((state) => ({ 
        filters: { ...state.filters, ...newFilters } 
      })),
  }))
);

// Optimized selectors to prevent unnecessary re-renders
export const useRides = () => 
  useOptimizedStore((state) => state.rides, shallow);

export const useDrivers = () => 
  useOptimizedStore((state) => state.drivers, shallow);

export const useFilters = () => 
  useOptimizedStore((state) => state.filters, shallow);

// Computed selectors with memoization
export const useFilteredRides = () => {
  const rides = useRides();
  const filters = useFilters();
  
  return useMemo(() => {
    return rides.filter(ride => {
      if (filters.status !== 'all' && ride.status !== filters.status) {
        return false;
      }
      
      if (filters.dateRange === 'today') {
        const today = new Date().toDateString();
        return new Date(ride.created_at).toDateString() === today;
      }
      
      return true;
    });
  }, [rides, filters]);
};
```

## 📱 **Mobile Performance Optimization**

### **1. React Native Performance**
```typescript
// apps/passenger-app/src/hooks/useOptimizedFlatList.ts
import { useCallback, useMemo } from 'react';
import { FlatListProps } from 'react-native';

interface OptimizedFlatListProps<T> extends Partial<FlatListProps<T>> {
  data: T[];
  keyExtractor: (item: T, index: number) => string;
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
}

export const useOptimizedFlatList = <T>({
  data,
  keyExtractor,
  renderItem,
  ...props
}: OptimizedFlatListProps<T>) => {
  // Memoize the render item function
  const memoizedRenderItem = useCallback(renderItem, []);
  
  // Memoize the key extractor
  const memoizedKeyExtractor = useCallback(keyExtractor, []);
  
  // Optimize FlatList props
  const optimizedProps = useMemo(() => ({
    data,
    renderItem: memoizedRenderItem,
    keyExtractor: memoizedKeyExtractor,
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    updateCellsBatchingPeriod: 50,
    initialNumToRender: 10,
    windowSize: 10,
    getItemLayout: props.getItemLayout,
    ...props,
  }), [data, memoizedRenderItem, memoizedKeyExtractor, props]);
  
  return optimizedProps;
};

// Optimized list item component
export const OptimizedListItem = React.memo<{
  item: any;
  onPress: (item: any) => void;
}>(({ item, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(item);
  }, [item, onPress]);

  return (
    <TouchableOpacity onPress={handlePress}>
      {/* Item content */}
    </TouchableOpacity>
  );
});
```

### **2. Image Optimization**
```typescript
// packages/shared-components/src/OptimizedImage.tsx
import React, { useState, useCallback } from 'react';
import { Image, ImageProps, View, ActivityIndicator } from 'react-native';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  uri: string;
  placeholder?: string;
  fallback?: string;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk';
  quality?: number;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  uri,
  placeholder,
  fallback,
  cachePolicy = 'memory-disk',
  quality = 0.8,
  style,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleLoadStart = useCallback(() => {
    setLoading(true);
    setError(false);
  }, []);

  const handleLoadEnd = useCallback(() => {
    setLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setLoading(false);
    setError(true);
  }, []);

  // Generate optimized image URL
  const optimizedUri = useMemo(() => {
    if (!uri) return '';
    
    // Add image optimization parameters
    const url = new URL(uri);
    url.searchParams.set('quality', String(Math.round(quality * 100)));
    url.searchParams.set('format', 'webp');
    
    return url.toString();
  }, [uri, quality]);

  const imageSource = error && fallback 
    ? { uri: fallback }
    : { uri: optimizedUri };

  return (
    <View style={style}>
      <Image
        {...props}
        source={imageSource}
        style={style}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        resizeMode="cover"
      />
      
      {loading && (
        <View style={[style, { position: 'absolute', justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="small" color="#3b82f6" />
        </View>
      )}
    </View>
  );
};
```

## 🔄 **Real-Time Performance**

### **1. Optimized Real-Time Subscriptions**
```typescript
// packages/api-client/src/hooks/useOptimizedRealtime.ts
import { useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { useQueryClient } from '@tanstack/react-query';
import { debounce } from 'lodash-es';

interface RealtimeOptions {
  table: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  throttleMs?: number;
  batchUpdates?: boolean;
}

export const useOptimizedRealtime = ({
  table,
  filter,
  event = '*',
  throttleMs = 1000,
  batchUpdates = true,
}: RealtimeOptions) => {
  const queryClient = useQueryClient();
  const updateBatch = useRef<any[]>([]);
  const channelRef = useRef<any>(null);

  // Debounced batch update function
  const processBatchUpdates = useCallback(
    debounce(() => {
      if (updateBatch.current.length > 0) {
        // Process all batched updates at once
        updateBatch.current.forEach(update => {
          queryClient.setQueryData(
            [table, update.id],
            update.new || update.old
          );
        });
        
        // Invalidate related queries
        queryClient.invalidateQueries({ 
          queryKey: [table],
          exact: false 
        });
        
        updateBatch.current = [];
      }
    }, throttleMs),
    [table, throttleMs, queryClient]
  );

  useEffect(() => {
    const channelName = `optimized-${table}-${filter || 'all'}`;
    
    channelRef.current = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event,
          schema: 'public',
          table,
          filter,
        },
        (payload) => {
          if (batchUpdates) {
            updateBatch.current.push(payload);
            processBatchUpdates();
          } else {
            // Immediate update
            queryClient.setQueryData(
              [table, payload.new?.id || payload.old?.id],
              payload.new || payload.old
            );
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
      processBatchUpdates.cancel();
    };
  }, [table, filter, event, batchUpdates, processBatchUpdates]);

  return {
    isConnected: channelRef.current?.state === 'joined',
  };
};
```

### **2. Memory Management**
```typescript
// packages/shared-utils/src/memoryManager.ts
export class MemoryManager {
  private static instance: MemoryManager;
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private cleanupInterval: NodeJS.Timeout;

  private constructor() {
    // Clean up expired cache entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  set(key: string, data: any, ttl: number = 10 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  // Get memory usage statistics
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.cache.clear();
  }
}

// React hook for memory management
export const useMemoryManager = () => {
  const manager = MemoryManager.getInstance();

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      manager.clear();
    };
  }, [manager]);

  return manager;
};
```

This comprehensive performance optimization implementation ensures fast, efficient, and scalable frontend applications across all Taxicab platform interfaces with proper memory management, bundle optimization, and real-time performance.
